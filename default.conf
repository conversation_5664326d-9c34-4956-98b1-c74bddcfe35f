server {
    listen 80;
    server_name localhost;

    client_max_body_size 5m;

    location ^~ / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html /index.html;
    }

    location ^~ /mp {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /mp/index.html /index.html;
    }

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }

}
