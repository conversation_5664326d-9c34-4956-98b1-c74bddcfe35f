{"private": true, "scripts": {"dev": "umi dev", "build": " cross-env Deploy='test' umi build", "build:master": "umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/icons": "^4.2.2", "@ant-design/pro-layout": "^5.0.12", "@ant-design/pro-table": "^2.7.1", "@antv/g6": "^3.8.5", "@umijs/preset-react": "1.x", "@umijs/test": "^3.2.14", "classnames": "^2.2.6", "crypto-js": "^4.0.0", "dva": "^2.4.1", "dva-cli": "^0.10.1", "echarts": "^4.8.0", "gg-editor": "^3.1.3", "html2canvas": "^1.4.1", "html2pdf.js": "^0.9.2", "jspdf": "^2.5.1", "lint-staged": "^10.0.7", "moment": "^2.27.0", "prettier": "^1.19.1", "querystring": "^0.2.0", "react": "^16.12.0", "react-dom": "^16.12.0", "react-helmet-async": "^1.0.6", "reqwest": "^2.0.5", "umi": "^3.2.14", "umi-request": "^1.3.5", "yorkie": "^2.0.0"}, "devDependencies": {"cross-env": "^7.0.2"}}