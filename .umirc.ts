import { defineConfig } from 'umi';

export default defineConfig({
  nodeModulesTransform: {
    type: 'none',
  },
  title: '项目管理系统',
  define: {
    target: 'http://192.168.1.154:6999/', //测试环境
    //target: 'http://192.168.1.153:6999/', //生产环境
  },
  dva: {
    hmr: true,
  },
  metas: [
    {
      httpEquiv: 'Cache-Control',
      content: 'no-cache, no-store, must-revalidate',
    },
    {
      httpEquiv: 'Cache',
      content: 'no-cache',
    },
    {
      httpEquiv: 'Pragma',
      content: 'no-cache',
    },
    {
      httpEquiv: 'Expires',
      content: '0',
    },
  ],
  proxy: {
    '/hrs': {
      // target: 'http://192.168.1.154:6999/', //测试环境
      // target: 'http://mp.it.sw/', //生产环境
      // target: 'http://swit-test.psc.sw', //测试环境
      //target: 'http://192.168.1.153:6999/', //生产环境
      // target: 'http://192.168.30.205:8082/', //联调
      changeOrigin: true,
      // pathRewrite: { '^/api': '' },
    },
  },
  publicPath: process.env.Deploy === 'test' ? '/mp/' : '/',
  base: process.env.Deploy === 'test' ? '/mp/' : '/',
  routes: [
    // { path: '/login', component: '@/layouts/UserLayout' },
    { path: '/', component: 'home' },
    { path: '/hrsweb', component: '@/pages/unifyLogin' },
    { path: '/index', component: '@/pages/unifyLogin/home' },
    {
      path: '/',
      component: 'home',
      routes: [
        //404
        {
          exact: true,
          path: '/404',
          component: '404',
        },
        //工作台
        {
          exact: true,
          path: '/workbench',
          component: 'workbench',
        },
        //项目列表
        {
          exact: true,
          path: '/projectList',
          component: 'projectOverview/projectList',
        },
        //预立项项目列表
        {
          exact: true,
          path: '/readyProject',
          component: 'projectOverview/readyProject',
        },
        //创建预立项项目
        {
          exact: true,
          path: '/createReadyProject',
          component: 'projectOverview/createReadyProject',
        },
        //待办项目
        {
          exact: true,
          path: '/toDoList',
          component: 'projectOverview/toDoList',
        },
        //跟踪页
        {
          exact: true,
          path: '/projectList/track',
          component: 'projectOverview/track',
        },
        //跟踪页--预立项
        {
          exact: true,
          path: '/readyProject/track',
          component: 'projectOverview/trackReady',
        },
        //创建项目
        {
          exact: true,
          path: '/createProject',
          component: 'projectOverview/createProject',
        },
        //结项
        {
          exact: true,
          path: '/closure',
          component: 'projectOverview/closure',
        },

        //项目概况
        {
          exact: true,
          path: '/overview',
          component: 'projectOverview/overview',
        },
        //变更项目
        {
          exact: true,
          path: '/updateProject',
          component: 'projectOverview/createProject',
        },
        //财务预算
        {
          exact: true,
          path: '/projectFinanceBudget',
          component: 'projectFinance/financialBudget',
        },
        //立项审核
        {
          exact: true,
          path: '/projectApproval',
          component: 'projectOverview/projectApproval',
        },
        //结项审核
        {
          exact: true,
          path: '/closureApporval',
          component: 'projectOverview/closureApporval',
        },
        //查看审核中的项目
        {
          exact: true,
          path: '/projectApprovalLookOver',
          component: 'projectOverview/projectApproval',
        },
        //变更审核
        {
          exact: true,
          path: '/projectExamine',
          component: 'projectOverview/examine',
        },
        //财务列表
        {
          exact: true,
          path: '/projectFinanceList',
          component: 'projectFinance/financeList',
        },
        //权限配置
        {
          exact: true,
          path: '/permissionConfiguration',
          component: 'puview/permissionConfiguration',
        },
        //角色管理
        { exact: true, path: '/roleManage', component: 'puview/roleManage' },
        //数据权限
        {
          exact: true,
          path: '/dataPermission',
          component: 'puview/dataPermission',
        },
        //流程配置
        {
          exact: true,
          path: '/process',
          component: 'puview/process',
        },
        //人员配置
        {
          exact: true,
          path: '/staffing',
          component: 'puview/staffing',
        },
      ],
    },
  ],
});
