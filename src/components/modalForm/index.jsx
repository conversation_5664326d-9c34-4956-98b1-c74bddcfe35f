import React, { useEffect, useState } from 'react';
import { Modal, Form, Input } from 'antd';
import styles from './index.less';
import Froms from './Froms';

const ModelForm = props => {
  console.log('props', props);
  let { formProps, handleCancel, handleOk, visible, formItem } = props;
  const [visibles, setVisible] = useState(visible);
  const { TextArea } = Input;

  const handleOks = () => {
    handleOk();
    onFinish();
  };

  const handleCancels = () => {
    handleCancel(false);
  };

  const onFinish = () => {
    console.log('fine');
  };

  return (
    <div className={styles.modelForm}>
      <Modal
        width={formProps.width}
        title={formProps.title}
        visible={false}
        onOk={handleOks}
        maskClosable={false}
        onCancel={handleCancels}
      >
        <div className={styles.ModalBox}>
          <Froms formParam={formProps} formItem={formItem}></Froms>
        </div>
      </Modal>
    </div>
  );
};

export default ModelForm;
