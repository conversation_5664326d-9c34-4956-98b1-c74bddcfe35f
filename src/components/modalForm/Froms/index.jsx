import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Row, Col } from 'antd';
import styles from './index.less';

const ModelForm = props => {
  console.log('props222', props);
  let { formParam, formItem } = props;
  const { TextArea } = Input;

  const onFinish = () => {
    console.log('fine');
  };

  return (
    <div className={styles.modelForm}>
      <Form
        name="basic"
        labelCol={{ span: formParam.labelCol }}
        wrapperCol={{ span: formParam.wrapperCol }}
        onFinish={onFinish}
      >
        <Row gutter={32}>
          {formItem.map(item => {
            return (
              <Col span={item.span ? item.span : 12}>
                <Form.Item
                  label={item.label}
                  name={item.index}
                  rules={[
                    {
                      required: item.rules ? item.rules : false,
                      message: item.message ? item.message : null,
                    },
                  ]}
                >
                  <TextArea autoSize />
                </Form.Item>
              </Col>
            );
          })}
        </Row>
      </Form>
    </div>
  );
};

export default ModelForm;
