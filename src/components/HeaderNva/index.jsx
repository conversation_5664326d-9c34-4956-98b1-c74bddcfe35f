import React, { useEffect, useState } from 'react';
import { <PERSON>u, Dropdown, Avatar, Breadcrumb } from 'antd';
import { DownOutlined, UserOutlined } from '@ant-design/icons';
import { getUser } from './service';
import { Link } from 'umi';
import logo from '@/assets/logo.png';
import { history } from 'umi';
import { MyContext } from '../../pages/home';

import styles from './index.less';
import { LOGIN_URL } from "../../utils/constant";
import getUrlParams from "../../utils/getUrlParams";
import { queryCurrent } from "../../services/user";

const HearderNav = props => {
  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  const [uer, setUser] = useState();

  useEffect(() => {
    setButtonList(context);
  }, [context]);

  useEffect(() => {
    const token= getUrlParams("token") || JSON.parse(localStorage.getItem('userInfo'))?.access_token ||  ""
    if (token) {
      queryCurrents(token);
    }
  }, []);
  const queryCurrents = async params => {
    const { data, code } = await queryCurrent(params);
    if (code === 200) {
      if (data) {
        const userInfo = {
          access_token: params,
          userId: data?.sysUser?.userId,
        };
        setUser(data.sysUser);
        localStorage.clear();
        localStorage.setItem('userInfo', JSON.stringify(userInfo));
        localStorage.setItem('updateTime', new Date().getTime());
        localStorage.setItem('loginType', 'unified');
        // window.location.replace('/workbench');
      }
    }else {
      logOut()
    }
  };
  const logOut = () => {
    localStorage.clear();
    window.location.href = LOGIN_URL;
  };

  const menu = (
    <Menu>
      <Menu.Item>
        <a
          href="https://kd.swcare.com.cn/video/file/dcf9758c1ed948d785eb07eb20200908.pdf"
          target="_blank"
          style={{ marginRight: 15 }}
        >
          用户手册
        </a>
      </Menu.Item>
      <Menu.Item>
        <Link onClick={logOut}>退出登录</Link>
      </Menu.Item>
    </Menu>
  );

  const [breadcrumbList, setBreadcrumbList] = useState([]);

  const breadcrumbNameMap = {
    '/projectList': '项目列表',
    '/projectList/track': '项目跟踪',
    '/createProject': '项目立项',
    '/updateProject': '项目变更',
    '/projectExamine': '变更审核',
    '/projectApprovalLookOver': '审核中',
    '/closureApporval': '结项审核',
    '/projectApproval': '立项审核',
    '/projectFinanceList': '财务列表',
    '/projectFinanceBudget': '财务预算',
    '/toDoList': '待办项目',
    '/permissionConfiguration': '权限配置',
    '/roleManage': '角色管理',
    '/overview': '项目概况',
    '/workbench': '工作台',
    '/dataPermission': '数据权限',
    '/process': '流程配置',
    '/closure': '项目结项',
  };
  useEffect(() => {
    const pathSnippets = location.pathname.split('/').filter(i => i);
    const extraBreadcrumbItems = pathSnippets.map((_, index) => {
      const url = `/${pathSnippets.slice(0, index + 1).join('/')}`;
      return (
        <Breadcrumb.Item key={url}>
          <Link to={url}>{breadcrumbNameMap[url]}</Link>
        </Breadcrumb.Item>
      );
    });
    setBreadcrumbList([...extraBreadcrumbItems]);
  }, [location.pathname]);

  return (
    <div className={styles.heade}>
      <div>
        <div>
          <img src={logo} alt="" className={styles.logo} />
        </div>
        <div className={styles.logoName}>· 市场经营管理平台</div>
      </div>
      <Breadcrumb className={styles.breadcrumb}>{breadcrumbList}</Breadcrumb>
      <div className={styles.headeRight}>
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link" onClick={e => e.preventDefault()}>
            <Avatar
              style={{ backgroundColor: '#1890ff', marginRight: 5 }}
              icon={<UserOutlined />}
            />
            {uer && uer.employeeName}
            <DownOutlined style={{ marginLeft: 5 }} />
          </a>
        </Dropdown>
      </div>
    </div>
  );
};

export default HearderNav;
