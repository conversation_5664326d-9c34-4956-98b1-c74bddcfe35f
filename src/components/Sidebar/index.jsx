import React from 'react';
import { Menu } from 'antd';
import {
  AppstoreOutlined,
  ClusterOutlined,
  CodeSandboxOutlined,
  PayCircleOutlined,
} from '@ant-design/icons';

import { connect } from 'dva';
import { Link } from 'umi';
import styles from './index.less';
import { useEffect, useState } from 'react';
import { getMenu, freeDataAuth } from './service';
import { useLocation } from 'react-router-dom';
import { history } from 'umi';
import { MyContext } from '../../pages/home';
const { SubMenu } = Menu;
const Sidebar = props => {
  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  useEffect(() => {
    setButtonList(context);
  }, [context]);
  const handleClick = e => {};
  const [menuList, setMenuList] = useState([]);
  //获取数据权限下拉
  const getMenulist = async value => {
    try {
      const response = await getMenu(value);
      if (response && response.code === 200) {
        const { data } = response;
        if (data && data.length > 0) {
          data.map(item => {
            item.key = item.id;
          });
        }
        setMenuList(data);
      }
    } catch (error) {
      console.error('Error calling getMenu:', error);
    }
  };
  const freeData = async () => {
    try {
      const response = await freeDataAuth();
      if (response && response.code === 200) {
        // Handle successful response here if needed
      }
    } catch (error) {
      console.error('Error calling freeDataAuth:', error);
    }
  };
  useEffect(() => {
    getMenulist();
    freeData();
  }, []);

  useEffect(() => {
    const list = [...buttonList, '/workbench'];
    if (menuList.length > 0) {
      menuList.forEach(item => {
        if (item.menu.length > 0) {
          item.menu.forEach(menuItem => {
            list.push(menuItem.url);
          });
        }
      });
    }
    let url = '';
    const pathSnippets = location.pathname.split('/').filter(i => i);
    pathSnippets.map((_, index) => {
      url = `/${pathSnippets.slice(0, index + 1).join('/')}`;
    });
    // if (list.length > 0 && url !== '/404') {
    //   if (!list.includes(url)) {
    //     history.replace('/404');
    //   }
    // }
  }, [menuList, props]);
  const iconList = [
    <CodeSandboxOutlined />,
    <PayCircleOutlined />,
    <ClusterOutlined />,
    <AppstoreOutlined />,
    <AppstoreOutlined />,
  ];
  return (
    <Menu
      onClick={handleClick}
      defaultSelectedKeys={'procedure'}
      defaultOpenKeys={['0']}
      mode="inline"
      className={styles.menu}
    >
      <Menu.Item key="procedure" icon={<AppstoreOutlined />}>
        <Link to="/workbench">工作台</Link>
      </Menu.Item>
      {menuList &&
        menuList.length > 0 &&
        menuList.map((item, itemIndex) => (
          <SubMenu
            key={itemIndex}
            icon={iconList[Number(item.level) - 1]}
            title={item.title}
          >
            {item.menu.map((menuItem, menuIndex) => (
              <Menu.Item key={menuItem.url}>
                <Link to={menuItem.url}>{menuItem.title}</Link>
              </Menu.Item>
            ))}
          </SubMenu>
        ))}
      {/* <SubMenu key="sub2" icon={<AppstoreOutlined />} title="项目管理">
        <Menu.Item key="projectList">
          <Link to="/projectList">项目列表</Link>
        </Menu.Item>
        <Menu.Item key="9">
          <Link to="/toDoList">待办项目</Link>
        </Menu.Item>
        <Menu.Item key="7" className={styles.button}>
          <Button>
            {' '}
            <Link to="/createProject">+ 创建新项目</Link>
          </Button>
        </Menu.Item>
      </SubMenu>
      <SubMenu key="projectFinance" icon={<MailOutlined />} title="项目财务">
        <Menu.Item key="projectFinanceList">
          <Link to="/projectFinanceList">财务列表</Link>
        </Menu.Item>
        <Menu.Item key="projectFinanceBudget">
          <Link to="/projectFinanceBudget">财务预算</Link>
        </Menu.Item>
      </SubMenu>
      <SubMenu key="puview" icon={<ClusterOutlined />} title="权限管理"> */}

      {/* <SubMenu title="procedure">
        <Menu.Item key="procedure">
          <Link to="/staffing">人员配置</Link>
        </Menu.Item>
      </SubMenu> */}
    </Menu>
  );
};

export default Sidebar;
