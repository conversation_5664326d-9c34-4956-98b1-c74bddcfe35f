import React, { useState, useEffect } from 'react';
import {
  getDeptListInfo,
  addDataRole,
  updateDataRole,
  getTrialListPage,
  delTrialInfo,
  serchGetTrialInfo,
  addTrialInfo,
  updateTrialInfo,
  getTrialDic,
  getItemTrial,
} from './service';
import {
  Button,
  Modal,
  Pagination,
  Table,
  Form,
  Radio,
  Tooltip,
  Input,
  Select,
  Popconfirm,
  DatePicker,
  message,
  Col,
  Row,
  Divider,
  Card,
} from 'antd';
import {
  PlusOutlined,
  EditTwoTone,
  UpSquareOutlined,
  ExclamationCircleOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import styles from './index.less';
import { MyContext } from '../../home';
import classNames from 'classnames';

import Procedure from '../procedure';

import Staffing from '../staffing';
import { translate } from '@antv/g6/lib/util/math';
const RadioButton = Radio.Button;

const { Option } = Select;
const { TextArea } = Input;

export default props => {
  const { confirm } = Modal;

  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  useEffect(() => {
    setButtonList(context);
  }, [context]);
  useEffect(() => {
    setColumns(totalColumns);
  }, [buttonList]);

  //编辑回显空节点集合

  const [EditNull, setEditNull] = useState([]);

  //下个流程配置个数
  const [ProcessNextNumber, setProcessNextNumber] = useState(0);
  //选择父级节点select列表
  const [selectData, SetSelectData] = useState([]);

  const [standTrial, setStandTrial] = useState([]);
  //表单提交
  const [publicData, setPublicData] = useState({
    id: '', //id     添加为空，修改不为空
    type: null, //流程类型：0立项审核、1变更审核
    remarks: '', //流程描述
    inComeType: '', //项目分类（流程归属的项目分类）
  });
  const [showProcedureModal, setShowProcedureModal] = useState(false);
  const [processData, setProcessData] = useState();

  const [data, setData] = useState([]);
  const [dataTotal, setDataTotal] = useState(0);
  const [parmas, setParmas] = useState({ page: 1, limit: 10 });
  // 点击按钮查询
  const [searchParmas, setSearchParmas] = useState({});
  //新建
  const [visibleModal, setVisibleModal] = useState(false);

  //新增数据--children
  const [lastArrCld, setlastArrCld] = useState([]);

  //类型---新增/编辑
  const [type, setType] = useState('');
  //弹窗标题
  const [modalTitle, setModalTitle] = useState('');
  const [form] = Form.useForm();
  const [seachForm] = Form.useForm();
  //编辑数据权限当前id
  const [updataId, setUpdataId] = useState([]);
  //编辑数据权限回显--所有表单数据
  const [formDatas, setFormDatas] = useState({});
  //项目分类
  const [ItemTypes, setItemTypes] = useState([]);

  //项目审核节点--未加工
  const [standDatas, setStandDatas] = useState([]);
  //项目审核节点
  const [standDataSel, setStandDataSel] = useState([]);

  const [selectedItems, setSelectedItems] = useState([]);

  //新建流程流程个数
  const [setProcessVisible, setProcessVisibles] = useState(false);

  //新建流程流程个数
  const [setProcessVisibleP, setProcessVisiblePs] = useState(false);

  //选择父节点时当前的item
  const [ItemNode, setItemNode] = useState({});

  const [ChangelowerId, setChangelowerId] = useState({});
  //用来存储当前空id
  const [NullId, setNullId] = useState(14);

  //用来存储当前空流程节点
  const [newArrNull, SetnewArrNull] = useState([]);
  //存储的新建流程数据
  const [NoLabelData, setNoLabelData] = useState([]);
  //流程配置结构
  const [setDatass, SetSetDatass] = useState([
    {
      lable: '流程一',
      data: [
        {
          trialId: '0',
          lowerId: [],
          trialName: '发起人',
          id: '0',
          isShow: false,
          isShows: false,
        },
      ],
    },
  ]);

  //提交数据前处理--审核节点
  const [standArr, setStandArr] = useState([
    {
      trialId: '',
      trialName: '',
      lowerId: '',
    },
  ]);

  //配置下一个流程
  const setProcessNext = () => {
    let IsLast = false;
    setDatass.forEach(item => {
      item.data.forEach(items => {
        if (items.trialId == '9') {
          IsLast = true;
        }
      });
    });

    let goNext = false;
    if (setDatass.length < 2) {
      setProcessVisibles(true);
      goNext = true;
    } else {
      setDatass.forEach((item, index) => {
        if (index > 0) {
          item.data.forEach(items => {
            if (items.isShow) {
              goNext = false;
            } else {
              goNext = true;
            }
          });
        }
      });

      if (IsLast) {
        Modal.warning({
          title: '提示',
          content: '流程以‘结束’结尾，‘结尾’节点已存在',
        });
      } else {
        if (!goNext) {
          confirm({
            title: '提示',
            icon: <ExclamationCircleOutlined />,
            content: '此流程还有父节点没有配置,确定进入下一个流程?',
            cancelText: '确定',
            okText: '取消',
            onOk() {},
            onCancel() {
              setProcessVisibles(true);
            },
          });
        } else {
          setProcessVisibles(true);
        }
      }
    }
  };
  //配置下一个流程模态框
  const setProcessHandleOk = () => {
    let addLabel = '';
    switch (setDatass.length) {
      case 1:
        addLabel = '流程二';
        break;
      case 2:
        addLabel = '流程三';
        break;
      case 3:
        addLabel = '流程四';
        break;
      case 4:
        addLabel = '流程五';
        break;
      case 5:
        addLabel = '流程六';
        break;
      case 6:
        addLabel = '流程七';
        break;
      case 7:
        addLabel = '流程八';
        break;
      case 8:
        addLabel = '流程九';
        break;
    }
    let newObj = {
      lable: addLabel,
      data: [],
    };

    if (type == 'add') {
      for (var i = 1; i <= ProcessNextNumber; i++) {
        let newChildOBJ = {
          id: setDatass.length.toString() + i.toString(),
          trialId: '',
          lowerId: [],
          trialName: '',
          isShow: false,
          isShows: false,
          isNew: true,
        };

        newObj.data.push(newChildOBJ);
      }
    } else {
      for (var i = 1; i <= ProcessNextNumber; i++) {
        let newChildOBJ = {
          id: setDatass.length.toString() + i.toString(),
          trialId: '',
          lowerId: [],
          trialName: '',
          isShow: true,
          isShows: false,
          isNew: true,
        };

        newObj.data.push(newChildOBJ);
      }
    }

    SetSetDatass([...setDatass, newObj]);
    setProcessVisibles(false);
  };

  //配置下一个流程模态框
  const setProcessHandleCancel = () => {
    setProcessVisibles(false);
    setSelectedItems([]);
    setNullId(14);
    SetnewArrNull([]);
    setNoLabelData([]);
    SetSetDatass([
      {
        lable: '流程一',
        data: [
          {
            trialId: '0',
            lowerId: [],
            trialName: '发起人',
            id: '0',
            isShow: false,
            isShows: false,
          },
        ],
      },
    ]);
  };

  //为节点配置父节点
  const setProcessHandleOkP = () => {
    let changeSetDatass = setDatass;
    if (type == 'add') {
      changeSetDatass.forEach((items, index) => {
        if (items.data.length > 0) {
          items.data.forEach(itemData => {
            if (ItemNode.trialId == itemData.trialId) {
              if (ChangelowerId.value == '') {
                changeSetDatass[index].data.forEach(itemsNO => {
                  itemsNO.isShow = false;
                });
              } else {
                itemData.isShow = false;
              }
            }
          });
        }
      });

      SetSetDatass(changeSetDatass);
      let newsOBJ = {
        id: ItemNode.id.split(''),
        trialId: ItemNode.trialId,
        lowerId: ItemNode.lowerId,
        trialName: ItemNode.trialName,
      };

      let newsArr = NoLabelData;
      let newArrNulls = newArrNull;

      let idIndex = Number(newsOBJ.id[0]);
      newsArr.forEach((item, index) => {
        if (index > 0) {
          if (index == idIndex) {
            if (ChangelowerId.value == '') {
              item.data.forEach(itemData => {
                itemData.isShow = false;
              });

              if (newsArr[index - 1].data.length > 1) {
                if (item.data.length > 1) {
                  let newNullId = NullId;
                  let strArr = [];
                  newsArr[index - 1].data.forEach((upItem, upIndex) => {
                    upItem.lowerId.push(newNullId.toString());
                  });

                  item.data.forEach((nextItem, nextIndex) => {
                    strArr.push(nextItem.trialId);
                  });

                  let newObj = {
                    lowerId: strArr,
                    trialId: newNullId.toString(),
                    trialName: '',
                  };
                  newArrNulls.push(newObj);
                  SetnewArrNull(newArrNulls);
                  setNullId(newNullId + 1);
                } else {
                  newsArr[index - 1].data.forEach((upItem, upIndex) => {
                    upItem.lowerId.push(item.data[0].trialId);
                  });
                }
              } else {
                let newIndex = newsArr[index - 1].data[0].trialId;
                item.data.forEach((items, indexs) => {
                  newsArr.forEach((TrialIdItem, TrialIdIndex) => {
                    TrialIdItem.data.forEach(TrialIdItems => {
                      if (newIndex == TrialIdItems.trialId) {
                        TrialIdItems.lowerId.push(items.trialId);
                      }
                    });
                  });
                });
              }
            } else {
              item.data.forEach(itemData => {
                if (ItemNode.trialId == itemData.trialId) {
                  itemData.isShow == false;
                }
              });
              item.data.forEach((items, indexs) => {
                if (newsOBJ.id.join('') === items.id.join('')) {
                  newsArr.forEach((TrialIdItem, TrialIdIndex) => {
                    TrialIdItem.data.forEach(TrialIdItems => {
                      if (ChangelowerId.value == TrialIdItems.trialId) {
                        TrialIdItems.lowerId.push(items.trialId);
                      }
                    });
                  });
                }
              });
            }
          }
        }
      });
      setNoLabelData(newsArr);
      setProcessVisiblePs(false);
    } else {
      changeSetDatass.forEach((items, index) => {
        if (items.data.length > 0) {
          items.data.forEach(itemData => {
            if (ItemNode.trialId == itemData.trialId) {
              if (ChangelowerId.value == '') {
                changeSetDatass[index].data.forEach(itemsNO => {
                  itemsNO.isShow = false;
                });
              } else {
                itemData.isShow = false;
              }
            }
          });
        }
      });

      SetSetDatass(changeSetDatass);
      let newsOBJ = {
        id: ItemNode.id.split(''),
        trialId: ItemNode.trialId,
        lowerId: ItemNode.lowerId,
        trialName: ItemNode.trialName,
      };

      let newsArr = NoLabelData;
      let newArrNulls = EditNull;

      let idIndex = Number(newsOBJ.id[0]);
      newsArr.forEach((item, index) => {
        if (index > 0) {
          if (index == idIndex) {
            if (ChangelowerId.value == '') {
              item.data.forEach(itemData => {
                itemData.isShow = false;
              });

              if (newsArr[index - 1].data.length > 1) {
                if (item.data.length > 1) {
                  let newNullId = NullId + EditNull.length;
                  let strArr = [];
                  newsArr[index - 1].data.forEach((upItem, upIndex) => {
                    upItem.lowerId.push(newNullId.toString());
                  });

                  item.data.forEach((nextItem, nextIndex) => {
                    strArr.push(nextItem.trialId);
                  });

                  let newObj = {
                    lowerId: strArr,
                    trialId: newNullId.toString(),
                    trialName: '',
                    child: [],
                  };
                  newArrNulls.push(newObj);
                  SetnewArrNull(newArrNulls);
                  setNullId(newNullId + 1);
                } else {
                  newsArr[index - 1].data.forEach((upItem, upIndex) => {
                    upItem.lowerId.push(item.data[0].trialId);
                  });
                }
              } else {
                let newIndex = newsArr[index - 1].data[0].trialId;
                item.data.forEach((items, indexs) => {
                  newsArr.forEach((TrialIdItem, TrialIdIndex) => {
                    TrialIdItem.data.forEach(TrialIdItems => {
                      if (newIndex == TrialIdItems.trialId) {
                        TrialIdItems.lowerId.push(items.trialId);
                      }
                    });
                  });
                });
              }
            } else {
              item.data.forEach(itemData => {
                if (ItemNode.trialId == itemData.trialId) {
                  itemData.isShow == false;
                }
              });
              item.data.forEach((items, indexs) => {
                if (newsOBJ.id.join('') === items.id.join('')) {
                  newsArr.forEach((TrialIdItem, TrialIdIndex) => {
                    TrialIdItem.data.forEach(TrialIdItems => {
                      if (ChangelowerId.value == TrialIdItems.trialId) {
                        TrialIdItems.lowerId.push(items.trialId);
                      }
                    });
                  });
                }
              });
            }
          }
        }
      });
      setNoLabelData(newsArr);
      setProcessVisiblePs(false);
    }
  };

  //配置下一个流程模态框
  const setProcessHandleCancelP = () => {
    setProcessVisiblePs(false);
    setSelectedItems([]);
    setNullId(14);
    SetnewArrNull([]);
    setNoLabelData([]);
    SetSetDatass([
      {
        lable: '流程一',
        data: [
          {
            trialId: '0',
            lowerId: [],
            trialName: '发起人',
            id: '0',
            isShow: false,
            isShows: false,
          },
        ],
      },
    ]);
  };

  //配置下一个流程数
  const handProcessNextNumber = e => {
    setProcessNextNumber(e);
  };

  //配置父节点
  const setProcessParents = item => {
    let newsData = [];
    setDatass.forEach((item, index) => {
      if (index + 1 < setDatass.length) {
        item.data.forEach(items => {
          newsData.push(items);
        });
      }
    });

    let nullNode = {
      trialId: '',
      trialName: '上一个流程所有流程节点',
      id: '',
      lowerId: '',
    };
    newsData.unshift(nullNode);
    SetSelectData(newsData);

    if (type == 'add') {
      setItemNode(item.item);
      let sendDataNode = [];
      // let sendDataNodes = [];
      sendDataNode = setDatass.map(item => {
        let { data } = item;
        return {
          data: data.map(items => {
            let {
              lowerId,
              trialId,
              trialName,
              id,
              isShow,
              isShows,
              isNew,
            } = items;
            return {
              id: id.split(''),
              trialId: trialId,
              trialName: trialName,
              lowerId: lowerId,
              isShow: isShow,
              isShows: isShows,
              isNew: isNew,
              child: [],
            };
          }),
        };
      });

      setNoLabelData(sendDataNode);
    } else {
      setItemNode(item.item);
      if (setDatass.length >= 1) {
        let sendDataNode = [];
        // let sendDataNodes = [];
        sendDataNode = setDatass.map(item => {
          let { data } = item;
          return {
            data: data.map(items => {
              let {
                lowerId,
                trialId,
                trialName,
                id,
                isShow,
                isShows,
                isNew,
                child,
              } = items;
              return {
                id: id.split(''),
                trialId: trialId,
                trialName: trialName,
                lowerId: lowerId,
                isShow: isShow,
                isShows: isShows,
                isNew: isNew,
                child: child,
              };
            }),
          };
        });

        setNoLabelData(sendDataNode);
      }

      //setNoLabelData(sendDataNode);
    }

    setProcessVisiblePs(true);
  };

  const selectParentslowerId = e => {
    setChangelowerId(e);
  };

  //lowerId
  const [lowerIdArr, setlowerIdArr] = useState(['']);

  //表单样式
  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  };
  //新增--Children
  const addItemChildren = () => {
    if (standTrial.length == 0) {
      setStandTrial([
        {
          id: '1',
          children: [],
        },
      ]);
    } else {
      const len = standTrial.length;
      setStandTrial([
        ...standTrial,

        {
          id: (len + 1).toString(),
          children: [],
        },
      ]);
      setStandArr([...standArr, { trialId: '', trialName: '', lowerId: '' }]);
    }
  };
  //新增--brother
  const addItemBrother = value => {
    var sendArr = standTrial;
    const index = value.index;

    if (sendArr[index].children.length == 0) {
      const newChildrenObj = {
        id: (index + 1).toString() + '-1',
      };
      sendArr[index].children.push(newChildrenObj);
    } else {
      const len = sendArr[index].children.length;
      const newChildrenObj = {
        id: index + 1 + '-' + (len + 1).toString(),
      };
      sendArr[index].children.push(newChildrenObj);
    }

    setStandTrial(sendArr);
    flash();
  };
  //减少-brother
  const removeItem = value => {
    let newArr = setDatass;
    newArr.forEach((item, index) => {
      item.data.forEach((items, indexs) => {
        if (value.id == items.id) {
          if (item.data.length < 2) {
            newArr.splice(index, 1);
          } else {
            item.data.splice(indexs, 1);
          }
        }

        items.lowerId.forEach((itemss, indexss) => {
          if (value.trialId == itemss) {
            items.lowerId.splice(indexss, 1);
          }
        });
      });
    });

    SetSetDatass(newArr);

    let newARRS = selectedItems;
    if (newARRS.length > 0) {
      newARRS.forEach((item, index) => {
        if (item == value.trialId) {
          newARRS.splice(index, 1);
        }
      });
    }

    setSelectedItems(newARRS);
    flash();
  };

  //push
  const flash = () => {
    SetSetDatass([...setDatass]);
  };

  const handSelect = e => {
    const { value, key } = e;
    let selectedItemNew = selectedItems;
    let newsData = setDatass;
    let EditNulls = EditNull;

    if (type == 'add') {
      if (!e.value) {
        e.value = '';
        let arrPULL = [];
        newsData.forEach(item => {
          item.data.forEach(items => {
            if (items.trialId != '') {
              arrPULL.push(items.trialId);
            }
          });
        });
        setSelectedItems(arrPULL);
      } else {
        newsData.forEach((items, index) => {
          items.data.forEach((itemsChild, indexs) => {
            if (itemsChild.id == key) {
              itemsChild.trialId = value.value;
              itemsChild.trialName = value.label;
              itemsChild.isShows = true;
              itemsChild.isShow = true;
            }
          });
        });

        SetSetDatass(newsData);

        setSelectedItems([...selectedItems, value.value]);
      }
    }

    if (type == 'update') {
      let hasResent = false;
      newsData.forEach(elements => {
        elements.data.forEach(itemss => {
          if (e.value == itemss.trialId) {
            hasResent = true;
          }
        });
      });

      if (hasResent) {
        Modal.warning({
          title: '提示',
          content: '该节点已经存在',
        });
      }

      newsData.forEach(item => {
        item.data.forEach(items => {
          if (!items.isNew) {
            if (items.child.includes(e.key)) {
              let isnuLL = false;
              let isLast = false;
              items.lowerId.forEach(lowerS => {
                if (Number(lowerS) > 10) {
                  isnuLL = true;
                }
              });

              newsData.forEach(element => {
                element.data.forEach(elements => {
                  if (elements.id == e.key) {
                    if (elements.isNew) {
                      isLast = true;
                    } else {
                      isLast = false;
                    }
                  }
                });
              });

              if (!isnuLL && !isLast) {
                items.child.forEach((lowercld, indexCld) => {
                  if (lowercld == e.key) {
                    if (!items.lowerId.includes(e.value)) {
                      items.lowerId.splice(indexCld, 1, e.value);
                    }
                  }
                });
              } else {
                if (!isLast) {
                  EditNulls.forEach(nullItem => {
                    newsData.forEach(inItem => {
                      inItem.data.forEach(inItems => {
                        if (nullItem.child.includes(inItems.id)) {
                          let newIndex = nullItem.child.indexOf(e.key);
                          nullItem.lowerId.splice(newIndex, 1, e.value);
                        }
                      });
                    });
                  });
                }
              }
            }
          } else {
          }
        });
      });

      if (!e.value) {
        e.value = '';
        let arrPULL = [];
        newsData.forEach(item => {
          item.data.forEach(items => {
            if (items.trialId != '') {
              arrPULL.push(items.trialId);
            }
          });
        });
        setSelectedItems(arrPULL);
      } else {
        const newitems = standDatas.filter(item => {
          if (item.code == e.value) {
            return item;
          }
        });
        newsData.forEach((items, index) => {
          items.data.forEach((itemsChild, indexs) => {
            if (itemsChild.id == key) {
              itemsChild.trialId = e.value;
              itemsChild.trialName = newitems[0].name;
              itemsChild.isShows = true;
              if (itemsChild.isNew) {
                itemsChild.isShow = true;
              } else {
                itemsChild.isShow = false;
              }
            }
          });
        });
        setEditNull(EditNulls);
        SetSetDatass(newsData);
        setSelectedItems([...selectedItems, e.value]);
      }
    }
  };

  const handleChange = props => {
    const { value, key } = props;
    setPublicData({ ...publicData, [key]: value });
  };

  //获取列表数据
  const getlist = async parmas => {
    const { data, code } = await getTrialListPage(parmas);
    if (code === 200) {
      setData(data.records);
      setDataTotal(data.total);
    }
  };

  //查询项目分类和项目审核节点
  const getTrialDicData = async () => {
    const resp = await getTrialDic();
    if (resp.code === 200) {
      const allData = resp.data;
      const ItemType = allData.filter(item => {
        if (item.type != 'Stand') {
          return item;
        }
      });

      const stand = allData.filter(item => {
        if (item.type == 'Stand' && item.name != '空') {
          return item;
        }
      });
      setStandDatas(stand);
      var ItemTypes = [];
      ItemType.forEach(item => {
        ItemTypes.push(
          <Option key={item.code} value={item.code}>
            {item.name}
          </Option>,
        );
      });
      setItemTypes(ItemTypes);
      let newsArr = stand.filter(item => {
        if (item.code != '0') {
          return item;
        }
      });

      setStandDataSel(newsArr);
    }
  };

  const { Option } = Select;

  //表单提交--新增
  const onFinishFormData = value => {
    if (type == 'add') {
      if (setDatass.length >= 1) {
        let hasNumber = false;
        let hasEnd = false;

        setDatass.forEach((item, index) => {
          item.data.forEach(items => {
            if (items.isShow) {
              hasNumber = true;
            }
          });
        });
        let indexs = setDatass.length - 1;
        if (setDatass[indexs].data[0].trialId != '9') {
          hasEnd = true;
        }
        if (!hasNumber) {
          if (!hasEnd) {
            sendForm();
          } else {
            Modal.error({
              title: '提示',
              content: '流程必需以‘结束’结尾',
              okText: '我知道了',
            });
          }
        } else {
          Modal.error({
            title: '提示',
            content: '有节点未配置父节点！',
            okText: '我知道了',
          });
        }
      }
    }
    if (type == 'update') {
      sendFormUpdate();
    }
  };

  //表单提交
  const sendForm = async () => {
    let sendNewData = [];
    NoLabelData.forEach((item, index) => {
      item.data.forEach((items, indexs) => {
        let newObj = {
          trialId: items.trialId,
          trialName: items.trialName,
          lowerId: items.lowerId.join(','),
        };
        sendNewData.push(newObj);
      });
    });
    newArrNull.forEach(item => {
      let newObj = {
        trialId: item.trialId,
        trialName: item.trialName,
        lowerId: item.lowerId.join(','),
      };
      sendNewData.push(newObj);
    });

    const sendDataArr = publicData;
    sendDataArr['standTrial'] = sendNewData;

    const resp = await addTrialInfo(sendDataArr);
    if (resp.code == 200) {
      message.success({
        content: resp.data.msg,
        key: 'addProjectItem',
        duration: 2,
      });
      getlist(parmas);
      setVisibleModal(false);

      setPublicData({
        id: '',
        type: null,
        remarks: '',
        inComeType: '',
      });

      form.setFieldsValue({
        type: null,
        inComeType: '',
        remarks: '',
      });

      setSelectedItems([]);
      setNullId(14);
      SetnewArrNull([]);
      setNoLabelData([]);
      SetSetDatass([
        {
          lable: '流程一',
          data: [
            {
              trialId: '0',
              lowerId: [],
              trialName: '发起人',
              id: '0',
              isShow: false,
              isShows: false,
            },
          ],
        },
      ]);
    } else {
      setSelectedItems([]);
      setNullId(14);
      SetnewArrNull([]);
      setNoLabelData([]);

      message.error({
        content: '添加失败',
        key: 'addProjectItem',
        duration: 2,
      });
    }
  };

  //表单提交--修改
  const sendFormUpdate = async () => {
    let newSDatas = [];
    if (NoLabelData.length < 1) {
      newSDatas = setDatass.map(item => {
        return {
          data: item.data,
        };
      });
    } else {
      newSDatas = NoLabelData;
    }

    newSDatas.forEach(item => {
      item.data.forEach(items => {
        items.lowerId.forEach((itemss, index) => {
          if (itemss == '') {
            items.lowerId.splice(index, 1);
          }
        });
      });
    });

    let sendNewData = [];
    let sendNewDatas = [];
    newSDatas.forEach((item, index) => {
      item.data.forEach((items, indexs) => {
        let newObj = {
          trialId: items.trialId,
          trialName: items.trialName,
          lowerId: items.lowerId.join(','),
        };
        sendNewData.push(newObj);
        sendNewDatas.push(newObj);
      });
    });
    EditNull.forEach(item => {
      let newObj = {
        trialId: item.trialId,
        trialName: item.trialName,
        lowerId: item.lowerId.join(','),
      };
      sendNewData.push(newObj);
    });

    const sendDataArr = publicData;
    sendDataArr['standTrial'] = sendNewData;

    let hasEnd = false;
    let resent = false;
    let resentArr = [];
    sendNewDatas.forEach(items => {
      if (items.trialId == '9') {
        hasEnd = true;
      } else {
        hasEnd = false;
      }
      resentArr.push(items.trialId);
    });

    resentArr.sort().forEach((itemss, indexs) => {
      if (indexs + 1 < resentArr.length) {
        if (itemss == resentArr[indexs + 1]) {
          resent = true;
        }
      }
    });

    if (resent) {
      Modal.warning({
        title: '提示',
        content: '节点不能重复！',
      });
    } else {
      if (!hasEnd) {
        Modal.warning({
          title: '提示',
          content: '审核节点必须以‘结束’结尾！',
        });
      } else {
        const resp = await updateTrialInfo(sendDataArr);
        if (resp.code == 200) {
          message.success({
            content: '修改成功',
            key: 'addProjectItem',
            duration: 2,
          });
          getlist(parmas);
          setVisibleModal(false);
          setSelectedItems([]);
          setNullId(14);
          SetnewArrNull([]);
          setNoLabelData([]);
          SetSetDatass([
            {
              lable: '流程一',
              data: [
                {
                  trialId: '0',
                  lowerId: [],
                  trialName: '发起人',
                  id: '0',
                  isShow: false,
                  isShows: false,
                },
              ],
            },
          ]);
          setPublicData({
            id: '',
            type: null,
            remarks: '',
            inComeType: '',
          });

          form.setFieldsValue({
            name: '',
            depts: '',
            remarks: '',
          });
        } else {
          message.error({
            content: '操作失败',
            key: 'addProjectItem',
            duration: 2,
          });
        }
      }
    }
  };

  useEffect(() => {
    getTrialDicData();
  }, []);
  useEffect(() => {
    getlist(parmas);
  }, [parmas]);

  //动态表头
  const totalColumns = [
    {
      title: '流程类型',
      dataIndex: 'type',
      key: '1',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: value => {
        if (Number(value) === 0) {
          return '立项审核';
        } else if (Number(value) === 1) {
          return '变更审核';
        } else {
          return '结项审核';
        }
      },
    },
    {
      title: '流程描述',
      dataIndex: 'remarks',
      key: '1',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },

    {
      title: '创建人 ',
      dataIndex: 'createUsername',
      key: '3',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      filterIcon: filtered => handleGetIcon(filtered),
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },

    {
      title: '创建时间',
      dataIndex: 'createTime',
      ellipsis: {
        showTitle: false,
      },
      key: '2',
      align: 'left',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },

    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: '140px',
      render: item => {
        return (
          <div className={styles.deleteBt}>
            <span
              onClick={() => serchShowMoald({ title: '查看详情', item: item })}
            >
              查看
            </span>

            <span
              onClick={() =>
                updateShowMoald({
                  title: '编辑流程配置',
                  item: item,
                  type: 'update',
                })
              }
            >
              编辑
            </span>

            <Popconfirm
              title="是否删除？"
              okText="是"
              cancelText="否"
              onConfirm={() => deleteItem(item)}
            >
              <a>删除</a>
            </Popconfirm>
          </div>
        );
      },
    },
  ];
  const onChangePageNumber = (value, size) => {
    setParmas({ page: value, limit: size });
  };
  const [columns, setColumns] = useState(totalColumns);
  //改变每页条数
  const pageSizeChange = value => {
    setParmas({ ...parmas, limit: value });
  };
  //新增
  const showMoald = porps => {
    let newFormObj = {};
    newFormObj['name'] = '';
    newFormObj['remark'] = '';
    newFormObj['depts'] = [];
    form.setFieldsValue(newFormObj);
    setModalTitle(porps.title);
    setVisibleModal(true);
    setType(porps.type);
  };
  //查看详情
  const serchShowMoald = async porps => {
    showProcess(porps.item.id);
  };

  const showProcess = async id => {
    const { data, code } = await getItemTrial(id);
    if (code === 200) {
      const nodes = [];
      const edges = [];
      data.trialInfo[0].TrialInfo.forEach(item => {
        nodes.push({
          id: String(item.trial_id),
          label: String(item.trial_name),
        });
      });
      data.trialInfo[1].TrialCom.forEach(item => {
        edges.push({
          source: String(item.trial_id),
          target: String(item.lower_id),
        });
      });
      setProcessData({ nodes: nodes, edges: edges });
      setShowProcedureModal(true);
    }
  };

  //编辑
  const updateShowMoald = async porps => {
    setType(porps.type);
    setPublicData({
      id: porps.item.id,
      type: porps.item.typeId,
      remarks: porps.item.remarks,
      inComeType: porps.item.inComeType,
    });

    const resp = await serchGetTrialInfo(porps.item.id);
    if (resp.code === 200) {
      let getStandTrials = resp.data.standTrials;
      let newStandTrials = [];

      let NewsNotNull = []; //非空节点集合
      let NewsIsNull = []; //空节点集合

      getStandTrials.forEach((item, index) => {
        if (item[0].trialName == '') {
          NewsIsNull.push(item);
        } else {
          NewsNotNull.push(item);
        }
      });

      let newNewsIsNull = [];
      NewsIsNull.forEach(items => {
        items.forEach(itemss => {
          newNewsIsNull.push(itemss);
        });
      });

      let newNewsIsNulls = newNewsIsNull.map(items => {
        return {
          trialId: items.trialId,
          lowerId: items.lowerId.split(','),
          trialName: items.trialName,
          child: [],
        };
      });

      if (NewsNotNull.length >= 1) {
        NewsNotNull.forEach((item, index) => {
          let newLabel = '';
          switch (index) {
            case 0:
              newLabel = '流程一';
              break;
            case 1:
              newLabel = '流程二';
              break;
            case 2:
              newLabel = '流程三';
              break;
            case 3:
              newLabel = '流程四';
              break;
            case 4:
              newLabel = '流程五';
              break;
            case 5:
              newLabel = '流程六';
              break;
            case 5:
              newLabel = '流程七';
              break;
            case 6:
              newLabel = '流程八';
              break;
            case 7:
              newLabel = '流程九';
              break;
            case 8:
              newLabel = '流程十';
              break;
          }

          var dataArrs = [];
          item.forEach((items, indexs) => {
            let dataObj = {};
            dataObj = {
              id: index.toString() + (indexs + 1).toString(),
              trialId: items.trialId,
              lowerId: items.lowerId.split(','),
              trialName: items.trialName,
              isShow: false,
              isShows: true,
              isNew: false,
              child: [],
            };
            dataArrs.push(dataObj);
          });
          let newObj = {
            label: newLabel,
            data: dataArrs,
          };
          newStandTrials.push(newObj);
        });
      } else {
        newStandTrials = [];
        setVisibleModal(true);
      }

      newStandTrials.forEach((item, index) => {
        item.data.forEach(items => {
          items.lowerId.forEach(itemss => {
            if (Number(itemss) >= 10) {
              newStandTrials[index + 1].data.forEach(inItem => {
                items.child.push(inItem.id);
              });
            } else {
              newStandTrials.forEach(twoItem => {
                twoItem.data.forEach(twoItems => {
                  if (twoItems.trialId == itemss) {
                    items.child.push(twoItems.id);
                  }
                });
              });
            }
          });
        });
      });

      newNewsIsNulls.forEach(item => {
        newStandTrials.forEach(itemB => {
          itemB.data.forEach(itemBs => {
            if (item.lowerId.includes(itemBs.trialId)) {
              item.child.push(itemBs.id);
            }
          });
        });
      });
      setEditNull(newNewsIsNulls);

      SetSetDatass(newStandTrials);

      setModalTitle(porps.title);
      let newFormObj = {
        type: Number(porps.item.type),
        inComeType: porps.item.inComeType,
        remarks: porps.item.remarks,
      };

      setUpdataId(porps.item.id);
      form.setFieldsValue(newFormObj);
      setVisibleModal(true);
    } else {
      SetSetDatass([
        {
          lable: '流程一',
          data: [
            {
              trialId: '0',
              lowerId: [],
              trialName: '发起人',
              id: '0',
              isShow: false,
              isShows: false,
            },
          ],
        },
      ]);
    }
  };

  const handleOk = () => {
    setVisibleModal(false);

    setPublicData({
      id: '',
      type: null,
      remarks: '',
      inComeType: '',
    });

    form.setFieldsValue({
      type: null,
      inComeType: '',
      remarks: '',
    });
    setSelectedItems([]);
    setStandTrial([]);
    setlastArrCld([]);
  };
  const handleCancel = () => {
    setVisibleModal(false);

    setPublicData({
      id: '',
      type: null,
      remarks: '',
      inComeType: '',
    });

    form.setFieldsValue({
      type: null,
      inComeType: '',
      remarks: '',
    });
    setSelectedItems([]);
    setNullId(14);
    SetnewArrNull([]);
    setNoLabelData([]);
    SetSetDatass([
      {
        lable: '流程一',
        data: [
          {
            trialId: '0',
            lowerId: [],
            trialName: '发起人',
            id: '0',
            isShow: false,
            isShows: false,
          },
        ],
      },
    ]);
  };

  //删除
  const deleteItem = items => {
    delItem(items.id);
  };
  //向后端提交删除数据信息
  const delItem = async id => {
    const resp = await delTrialInfo(id);
    if (resp.code === 200) {
      message.success({ content: '删除成功!', key: 'editItem', duration: 2 });
      getlist(parmas);
      setVisibleModal(false);
    } else {
      message.error({ content: '操作失败!', key: 'editItem', duration: 2 });
    }
  };

  //向后端提交新增信息
  const addItem = async value => {
    value['id'] = null;
    const resp = await addDataRole(value);
    if (resp.code === 200) {
      message.success({ content: '成功!', key: 'editItem', duration: 2 });
      getlist(parmas);
      setVisibleModal(false);
    } else {
      message.error({ content: '失败!', key: 'editItem', duration: 2 });
    }
  };

  //搜索-流程类型
  const handleSearchParamsType = props => {
    setSearchParmas({ ...searchParmas, type: props });
    if (props == null || props == '') {
      let nullSearch = { page: 1, limit: 10 };
      getlist(nullSearch);
    }
  };

  //搜索-状态
  const handleSearchParamsStatus = props => {
    setSearchParmas({ ...searchParmas, is_use: props });
    if (props == null || props == '') {
      let nullSearch = { page: 1, limit: 10 };
      getlist(nullSearch);
    }
  };

  // 搜索
  const handleOnSearch = () => {
    setParmas({ ...parmas, ...searchParmas, page: 1 });
  };
  const { limit, page } = parmas;

  const [switchTable, setSwitchTable] = useState('a');

  const tabChange = value => {
    setSwitchTable(value);
  };
  return (
    <div>
      <>
        <Modal
          title="选择流程个数"
          visible={setProcessVisible}
          onOk={setProcessHandleOk}
          onCancel={setProcessHandleCancel}
        >
          <p>请选择下一个流程流程个数</p>
          <Select
            style={{ width: '220px' }}
            onChange={e => handProcessNextNumber(e)}
          >
            <option value={1}>1个</option>
            <option value={2}>2个</option>
            <option value={3}>3个</option>
            <option value={4}>4个</option>
            <option value={5}>5个</option>
          </Select>
        </Modal>

        <Modal
          title="流程父节点配置"
          visible={setProcessVisibleP}
          onOk={setProcessHandleOkP}
          onCancel={setProcessHandleCancelP}
        >
          <p>请选择当前节点的父节点</p>
          <Select
            labelInValue
            style={{ width: '180px' }}
            onChange={e => selectParentslowerId(e)}
          >
            {selectData.map(itemMaps => (
              <Select.Option key={itemMaps.id} value={itemMaps.trialId}>
                {itemMaps.trialName}
              </Select.Option>
            ))}
          </Select>
        </Modal>
      </>{' '}
      {switchTable === 'a' && (
        <div className={styles.contentBox}>
          <div className={styles.card}>
            <Form
              form={seachForm}
              onFinish={handleOnSearch}
              initialValues={formDatas}
            >
              <div
                className={classNames(styles.searchInput, styles.search_bottom)}
              >
                <div>
                  <p>流程类型：</p>
                  <Form.Item name="deptId1">
                    <Select
                      onChange={e => handleSearchParamsType(e)}
                      className={styles.selects}
                      allowClear
                    >
                      <Option value={0}>立项审核</Option>
                      <Option value={1}>变更审核</Option>
                      <Option value={2}>结项审核</Option>
                    </Select>
                  </Form.Item>
                </div>
                <div>
                  <p>状态：</p>
                  <Form.Item name="deptId2">
                    <Select
                      allowClear
                      className={styles.selects}
                      onChange={e => handleSearchParamsStatus(e)}
                    >
                      <Option value={0}>使用</Option>
                      <Option value={-1}>停用</Option>
                    </Select>
                  </Form.Item>
                </div>

                <>
                  <div>
                    <p></p>
                    <br />
                    <Button
                      type="primary"
                      htmlType="submit"
                      onClick={handleOnSearch}
                    >
                      查询
                    </Button>
                  </div>
                </>
                <div></div>
                <div></div>
              </div>
            </Form>
          </div>
          <div className={styles.card}>
            <div className={styles.cardOption}>
              <div className={styles.tabSwitch}>
                <Radio.Group
                  defaultValue="a"
                  buttonStyle="solid"
                  onChange={e => tabChange(e.target.value)}
                >
                  <RadioButton value="a">流程配置</RadioButton>
                  <RadioButton value="b">人员配置</RadioButton>
                </Radio.Group>
              </div>
              {(buttonList.includes('/access/insertDataRole') ||
                buttonList.includes('admin')) && (
                <div className={styles.cardRight}>
                  <Button
                    icon={<PlusOutlined />}
                    type="primary"
                    onClick={() =>
                      showMoald({ title: '新建流程配置', type: 'add' })
                    }
                  >
                    配置流程
                  </Button>
                </div>
              )}
            </div>
            <Table
              columns={columns}
              dataSource={data}
              pagination={false}
              size="middle"
              className={styles.anTdTable}
            />
            <div className={styles.splitPigination}>
              <div>
                <Select
                  defaultValue="10"
                  style={{ width: 150 }}
                  className={styles.selects}
                  onChange={pageSizeChange}
                >
                  <Option value="10">显示结果：10条</Option>
                  <Option value="20">显示结果：20条</Option>
                  <Option value="50">显示结果：50条</Option>
                </Select>
                <span className={styles.total}>共{dataTotal}条</span>
              </div>
              <Pagination
                total={dataTotal || 0}
                pageSize={limit}
                showSizeChanger={false}
                current={page}
                key={67}
                onChange={onChangePageNumber}
              />
            </div>

            <Modal
              width="80%"
              title={`${modalTitle}`}
              visible={visibleModal}
              onOk={handleOk}
              onCancel={handleCancel}
              footer={null}
            >
              <Form
                {...layout}
                form={form}
                name="nest-messages"
                initialValues={formDatas}
                onFinish={onFinishFormData}
              >
                <>
                  <Form.Item
                    label="流程类型"
                    name="type"
                    rules={[{ required: true, message: '选择流程类型' }]}
                  >
                    <Select
                      style={{ width: 300 }}
                      className={styles.selects}
                      allowClear
                      onChange={e => handleChange({ value: e, key: 'type' })}
                    >
                      <Option value={0}>立项审核</Option>
                      <Option value={1}>变更审核</Option>
                      <Option value={2}>结项审核</Option>
                    </Select>
                  </Form.Item>
                  {/* <Form.Item
                    label="项目分类"
                    name="inComeType"
                    rules={[{ required: true, message: '选择项目类型' }]}
                  >
                    <Select
                      style={{ width: 300 }}
                      className={styles.selects}
                      allowClear
                      onChange={e =>
                        handleChange({ value: e, key: 'inComeType' })
                      }
                    >
                      {ItemTypes}
                    </Select>
                  </Form.Item> */}
                  <Form.Item
                    label="流程描述"
                    name="remarks"
                    rules={[
                      {
                        required: true,
                        message: '请填写流程描述',
                      },
                    ]}
                  >
                    {/* <Input  allowClear={true}  /> */}

                    <TextArea
                      style={{ width: 400 }}
                      allowClear
                      rows={2}
                      className={styles.TextArea}
                      onChange={e =>
                        handleChange({ value: e.target.value, key: 'remarks' })
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    label="流程配置"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  >
                    {/* addProcess开始*/}

                    <div className={styles.addProcess}>
                      {setDatass.map((items, index) => {
                        return (
                          <div className={styles.addProcess_row}>
                            <div className={styles.addProcess_row_left}>
                              {type == 'add' && (
                                <>
                                  {index == 0 ? (
                                    <span>{items.lable}</span>
                                  ) : (
                                    <span
                                      style={{
                                        display: 'block',
                                        marginTop: '23px',
                                      }}
                                    >
                                      {items.lable}
                                    </span>
                                  )}
                                </>
                              )}
                            </div>
                            {/* addProcess_row_left结束*/}
                            <div className={styles.addProcess_row_right}>
                              {items.data.map((itemData, indexs) => {
                                return (
                                  <>
                                    {index == 0 ? (
                                      <div
                                        style={{
                                          minWidth: '135px',
                                          maxWidth: '135px',
                                        }}
                                        className={styles.inputBoxBG}
                                      >
                                        <span>发起人</span>
                                      </div>
                                    ) : (
                                      <div
                                        className={styles.span_UpSquareOutlined}
                                      >
                                        {type == 'add' ? (
                                          <>
                                            {itemData.isShows ? (
                                              <>
                                                {itemData.isShow ? (
                                                  <Tooltip
                                                    placement="topRight"
                                                    title="未配置父节点,请配置"
                                                  >
                                                    <UpSquareOutlined
                                                      style={{
                                                        transform:
                                                          'translateY(-5px)',
                                                        color: 'red',
                                                      }}
                                                      onClick={() =>
                                                        setProcessParents({
                                                          item: itemData,
                                                        })
                                                      }
                                                    />
                                                  </Tooltip>
                                                ) : (
                                                  <Tooltip
                                                    placement="topRight"
                                                    title="已配置父节点,可继续配置"
                                                  >
                                                    <UpSquareOutlined
                                                      style={{
                                                        transform:
                                                          'translateY(-5px)',
                                                        color: '#00000075',
                                                      }}
                                                      onClick={() =>
                                                        setProcessParents({
                                                          item: itemData,
                                                        })
                                                      }
                                                    />
                                                  </Tooltip>
                                                )}
                                              </>
                                            ) : (
                                              <>
                                                <UpSquareOutlined
                                                  style={{
                                                    transform:
                                                      'translateY(-5px)',
                                                    color: 'white',
                                                  }}
                                                />
                                              </>
                                            )}
                                          </>
                                        ) : (
                                          <>
                                            {itemData.isShows ? (
                                              <>
                                                {itemData.isShow ? (
                                                  <Tooltip
                                                    placement="topRight"
                                                    title="未配置父节点,请配置"
                                                  >
                                                    <UpSquareOutlined
                                                      style={{
                                                        transform:
                                                          'translateY(-5px)',
                                                        color: 'red',
                                                      }}
                                                      onClick={() =>
                                                        setProcessParents({
                                                          item: itemData,
                                                        })
                                                      }
                                                    />
                                                  </Tooltip>
                                                ) : (
                                                  <Tooltip
                                                    placement="topRight"
                                                    title="已配置父节点,可继续配置"
                                                  >
                                                    <UpSquareOutlined
                                                      style={{
                                                        transform:
                                                          'translateY(-5px)',
                                                        color: '#00000075',
                                                      }}
                                                      onClick={() =>
                                                        setProcessParents({
                                                          item: itemData,
                                                        })
                                                      }
                                                    />
                                                  </Tooltip>
                                                )}
                                              </>
                                            ) : (
                                              <>
                                                <Tooltip
                                                  placement="topRight"
                                                  title="未配置父节点,请配置"
                                                >
                                                  <UpSquareOutlined
                                                    style={{
                                                      transform:
                                                        'translateY(-5px)',
                                                      color: 'white',
                                                    }}
                                                    onClick={() =>
                                                      setProcessParents({
                                                        item: itemData,
                                                      })
                                                    }
                                                  />
                                                </Tooltip>
                                              </>
                                            )}
                                          </>
                                        )}

                                        {type == 'add' ? (
                                          <Select
                                            // style={{ width: 160 }}
                                            labelInValue
                                            className={styles.selects}
                                            allowClear
                                            style={{
                                              minWidth: '140px',
                                              maxWidth: '140px',
                                            }}
                                            dropdownMatchSelectWidth="140px"
                                            onChange={e =>
                                              handSelect({
                                                value: e,
                                                key: itemData.id,
                                                item: itemData,
                                              })
                                            }
                                            // style={{ marginLeft: '10px' }}
                                          >
                                            {standDatas
                                              .filter(itemss => {
                                                if (
                                                  !selectedItems.includes(
                                                    itemss.code,
                                                  ) &&
                                                  itemss.code != '0'
                                                ) {
                                                  return itemss;
                                                }
                                              })
                                              .map(itemMaps => (
                                                <Select.Option
                                                  key={Number(itemMaps.code)}
                                                  value={itemMaps.code.toString()}
                                                >
                                                  {itemMaps.name}
                                                </Select.Option>
                                              ))}
                                          </Select>
                                        ) : (
                                          <Select
                                            // style={{ width: 160 }}
                                            //labelInValue
                                            className={styles.selects}
                                            defaultValue={itemData.trialId}
                                            allowClear
                                            style={{
                                              minWidth: '140px',
                                              maxWidth: '140px',
                                            }}
                                            dropdownMatchSelectWidth="140px"
                                            onChange={e =>
                                              handSelect({
                                                value: e,
                                                key: itemData.id,
                                                item: itemData,
                                              })
                                            }
                                            // style={{ marginLeft: '10px' }}
                                          >
                                            {standDataSel.map(itemMap => (
                                              <Select.Option
                                                key={Number(itemMap.code)}
                                                value={itemMap.code.toString()}
                                              >
                                                {itemMap.name}
                                              </Select.Option>
                                            ))}
                                          </Select>
                                        )}
                                      </div>
                                    )}

                                    {indexs + 1 == items.data.length && (
                                      <>
                                        {index + 1 == setDatass.length && (
                                          <>
                                            <>
                                              {index != 0 && (
                                                <a>
                                                  <DeleteOutlined
                                                    style={{
                                                      marginTop: '18px',
                                                      marginLeft: '8px',
                                                      color: '#1890ff',
                                                    }}
                                                    onClick={() =>
                                                      removeItem(itemData)
                                                    }
                                                  />
                                                </a>
                                              )}
                                            </>

                                            <Tooltip
                                              placement="topRight"
                                              title="配置下一个流程"
                                            >
                                              {index == 0 ? (
                                                <EditTwoTone
                                                  style={{
                                                    marginTop: '8px',
                                                    marginLeft: '8px',
                                                  }}
                                                  onClick={setProcessNext}
                                                />
                                              ) : (
                                                <EditTwoTone
                                                  style={{
                                                    marginTop: '18px',
                                                    marginLeft: '8px',
                                                  }}
                                                  onClick={setProcessNext}
                                                />
                                              )}
                                            </Tooltip>
                                          </>
                                        )}
                                      </>
                                    )}
                                  </>
                                );
                              })}
                            </div>
                            {/*addProcess_row_right结束*/}
                          </div>
                        );
                      })}
                    </div>
                    {/* addProcess结束*/}

                    {/* <div className={styles.addProcess}>
                      <Row>
                        <Col span={8}>
                          <div className={styles.addProcessFirst}>发起人</div>
                        </Col>
                      </Row>
                      {standTrial.length == 0 && (
                        <Row>
                          <Col span={8}>
                            <div className={styles.addProcessFirst}>
                              <Tooltip placement="top" title="添加下一个流程">
                                <PlusCircleOutlined onClick={addItemChildren} />
                              </Tooltip>
                            </div>
                          </Col>
                        </Row>
                      )}
                      <>
                        {standTrial.length > 0 &&
                          standTrial.map((item, indexAll) => {
                            return (
                              <div
                                key={item.id}
                                className={styles.addProcessRows}
                              >
                                <div className={styles.input_box}>
                                  <div className={styles.list}>
                                    <div className={styles.input_box_input}>
                                      {type == 'add' ? (
                                        <Select
                                          // style={{ width: 160 }}
                                          labelInValue
                                          className={styles.selects}
                                          allowClear
                                          onChange={e =>
                                            handSelect({
                                              value: e,
                                              key: item.id,
                                            })
                                          }
                                          // style={{ marginLeft: '10px' }}
                                        >
                                          {standDatas
                                            .filter(itemss => {
                                              if (
                                                !selectedItems.includes(
                                                  itemss.code,
                                                ) &&
                                                itemss.code != '0'
                                              ) {
                                                return itemss;
                                              }
                                            })
                                            .map(itemMaps => (
                                              <Select.Option
                                                key={Number(itemMaps.code)}
                                                value={itemMaps.code.toString()}
                                              >
                                                {itemMaps.name}
                                              </Select.Option>
                                            ))}
                                        </Select>
                                      ) : (
                                        <>
                                          {type == 'update' && (
                                            <Select
                                              // style={{ width: 160 }}
                                              allowClear
                                              defaultValue={item.trialId}
                                              className={styles.selects}
                                              onChange={e =>
                                                handSelect({
                                                  value: e,
                                                  key: indexAll,
                                                  items: item,
                                                })
                                              }
                                              style={{ marginLeft: '10px' }}
                                            >
                                              {standDataSel.map(itemMap => (
                                                <Select.Option
                                                  key={Number(itemMap.code)}
                                                  value={itemMap.code.toString()}
                                                >
                                                  {itemMap.name}
                                                </Select.Option>
                                              ))}
                                            </Select>
                                          )}
                                        </>
                                      )}
                                    </div>
                                    {item.children.length == 0 && (
                                      <div className={styles.input_box_span}>
                                        <Tooltip
                                          placement="top"
                                          title="添加并行流程"
                                        >
                                          <PlusCircleOutlined
                                            onClick={value =>
                                              addItemBrother({
                                                item: item,
                                                index: indexAll,
                                              })
                                            }
                                          />
                                        </Tooltip>
                                      </div>
                                    )}
                                  </div>

                                  {item.children.length > 0 &&
                                    item.children.map((items, index) => {
                                      return (
                                        <div
                                          key={items.id}
                                          className={styles.list}
                                        >
                                          <div
                                            className={styles.input_box_input}
                                          >
                                            {type == 'add' ? (
                                              <Select
                                                // style={{ width: 160 }}
                                                className={styles.selects}
                                                allowClear
                                                labelInValue
                                                defaultValue={items.trialId}
                                                onChange={e =>
                                                  handSelectChild({
                                                    value: e,
                                                    key: item.id,
                                                    keys: items.id,
                                                    items: items,
                                                  })
                                                }
                                                style={{ marginLeft: '10px' }}
                                              >
                                                {standDatas
                                                  .filter(itemss => {
                                                    if (
                                                      !selectedItems.includes(
                                                        itemss.code,
                                                      ) &&
                                                      itemss.code != '0'
                                                    ) {
                                                      return itemss;
                                                    }
                                                  })
                                                  .map(itemMap => (
                                                    <Select.Option
                                                      key={Number(itemMap.code)}
                                                      value={itemMap.code.toString()}
                                                    >
                                                      {itemMap.name}
                                                    </Select.Option>
                                                  ))}
                                              </Select>
                                            ) : (
                                              <Select
                                                className={styles.selects}
                                                allowClear
                                                defaultValue={items.trialId}
                                                onChange={e =>
                                                  handSelectChild({
                                                    value: e,
                                                    key: item.id,
                                                    keys: items.id,
                                                    items: items,
                                                  })
                                                }
                                                style={{ marginLeft: '10px' }}
                                              >
                                                {standDataSel.map(itemMap => (
                                                  <Select.Option
                                                    key={Number(itemMap.code)}
                                                    value={itemMap.code.toString()}
                                                  >
                                                    {itemMap.name}
                                                  </Select.Option>
                                                ))}
                                              </Select>
                                            )}
                                          </div>
                                          {index ==
                                            item.children.length - 1 && (
                                            <div
                                              className={styles.input_box_span}
                                            >
                                              <>
                                                <Tooltip
                                                  placement="top"
                                                  title="添加并行流程"
                                                >
                                                  <PlusCircleOutlined
                                                    onClick={value =>
                                                      addItemBrother({
                                                        item: item,
                                                        index: indexAll,
                                                      })
                                                    }
                                                  />
                                                </Tooltip>
                                                <MinusCircleOutlined
                                                  onClick={() =>
                                                    removeItemBrother(item)
                                                  }
                                                  style={{ marginLeft: 10 }}
                                                />
                                              </>
                                            </div>
                                          )}
                                        </div>
                                      );
                                    })}
                                </div>
                                <div className={styles.downSpan}>
                                  <div className={styles.downSpan_icon}>
                                    {indexAll == standTrial.length - 1 && (
                                      <>
                                        <Tooltip
                                          placement="top"
                                          title="添加下一个流程"
                                        >
                                          <PlusCircleOutlined
                                            onClick={addItemChildren}
                                          />
                                        </Tooltip>
                                        <MinusCircleOutlined
                                          style={{ marginLeft: '10px' }}
                                          onClick={() =>
                                            removeItemChildren(item)
                                          }
                                        />
                                      </>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                      </>
                    </div> */}
                  </Form.Item>
                </>
                <div className={styles.formButtons}>
                  <Button type="primary" htmlType="submit">
                    确定
                  </Button>
                </div>
              </Form>
            </Modal>
            <Modal
              // title="Basic Modal"
              visible={showProcedureModal}
              onOk={() => setShowProcedureModal(false)}
              onCancel={() => setShowProcedureModal(false)}
              width={1100}
            >
              <Procedure processData={processData} />
            </Modal>
          </div>
        </div>
      )}
      {switchTable === 'b' && (
        <Staffing tabChange={value => tabChange(value)} />
      )}
    </div>
  );
};
