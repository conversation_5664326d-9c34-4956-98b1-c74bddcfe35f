import request from '@/utils/request';
import { BASE_URl } from "../../../utils/constant";

//获取数据权限列表list
export function getItemDataRolePage(parmas) {
  return request(`${BASE_URl}/access/getItemDataRolePage`, {
    method: 'post',
    data: parmas,
  });
}

//新增数据角色
export function addDataRole(parmas) {
  return request(`${BASE_URl}/access/insertDataRole`, {
    method: 'post',
    data: parmas,
  });
}

//编辑数据角色
export function updateDataRole(parmas) {
  return request(`${BASE_URl}/access/editDataRole`, {
    method: 'post',
    data: parmas,
  });
}

//删除数据权限角色
export function delDataRole(parmas) {
  return request(`${BASE_URl}/access/deleteDataRole?dataRoleVo=` + parmas, {
    method: 'post',
  });
}

//获取部门下拉框数据
export function getDeptListInfo() {
  return request(`${BASE_URl}/dept/SWCARESdeptAll`, {
    method: 'POST',
  });
}

//获取审核流程配置列表
export function getTrialListPage(parmas) {
  return request(`${BASE_URl}/projectList/getTrialListPage`, {
    method: 'post',
    data: parmas,
  });
}

//删除审核流程配置
export function delTrialInfo(parmas) {
  return request(`${BASE_URl}/projectList/delTrialInfo?id=` + parmas, {
    method: 'post',
  });
}

//查询审核流程配置
export function serchGetTrialInfo(parmas) {
  return request(`${BASE_URl}/projectList/getTrialInfo?id=` + parmas, {
    method: 'post',
  });
}
//新增审批流程配置
export function addTrialInfo(parmas) {
  return request(`${BASE_URl}/projectList/addTrialInfo`, {
    method: 'post',
    data: parmas,
  });
}

//修改审批流程配置
export function updateTrialInfo(parmas) {
  return request(`${BASE_URl}/projectList/updateTrialInfo`, {
    method: 'post',
    data: parmas,
  });
}

//查询项目分类和项目审核节点
export function getTrialDic() {
  return request(`${BASE_URl}/projectList/getTrialDic`, {
    method: 'post',
  });
}

//查询项目审批流程图信息
export function getItemTrial(id) {
  return request(`${BASE_URl}/projectList/getTrialInfo?id=${id}`, {
    method: 'post',
  });
}
