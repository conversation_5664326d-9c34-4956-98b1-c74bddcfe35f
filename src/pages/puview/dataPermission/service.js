import request from '@/utils/request';
import { BASE_URl } from "../../../utils/constant";

//获取数据权限列表list
export function getItemDataRolePage(parmas) {
  return request(`${BASE_URl}/access/getItemDataRolePage`, {
    method: 'post',
    data: parmas,
  });
}

//新增数据角色
export function addDataRole(parmas) {
  return request(`${BASE_URl}/access/insertDataRole`, {
    method: 'post',
    data: parmas,
  });
}

//编辑数据角色
export function updateDataRole(parmas) {
  return request(`${BASE_URl}/access/editDataRole`, {
    method: 'post',
    data: parmas,
  });
}

//删除数据权限角色
export function delDataRole(parmas) {
  return request(`${BASE_URl}/access/deleteDataRole?dataRoleVo=` + parmas, {
    method: 'post',
  });
}

//获取部门下拉框数据
export function getDeptListInfo() {
  return request(`${BASE_URl}/dept/SWCARESdeptAll`, {
    method: 'POST',
  });
}
