import React, { useState, useEffect } from 'react';
import {
  getItemDataRolePage,
  getDeptListInfo,
  addDataRole,
  updateDataRole,
  delDataRole,
} from './service';
import {
  Button,
  Modal,
  Pagination,
  Table,
  Form,
  Tooltip,
  Input,
  Select,
  Popconfirm,
  message,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import styles from './index.less';
import { MyContext } from '../../home';
import classNames from 'classnames';

const { TextArea } = Input;

export default props => {
  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  useEffect(() => {
    setButtonList(context);
  }, [context]);
  useEffect(() => {
    setColumns(totalColumns);
  }, [buttonList]);

  const [data, setData] = useState([]);
  const [dataTotal, setDataTotal] = useState(0);
  const [parmas, setParmas] = useState({ page: 1, limit: 10 });
  // 点击按钮查询
  const [searchParmas, setSearchParmas] = useState({});
  //新建数据权限
  const [visibleModal, setVisibleModal] = useState(false);
  //弹窗标题
  const [modalTitle, setModalTitle] = useState('');
  const [form] = Form.useForm();
  const [seachForm] = Form.useForm();
  const [children, setChildren] = useState([]);
  //编辑数据权限当前id
  const [updataId, setUpdataId] = useState([]);
  //编辑数据权限回显--所有表单数据
  const [formDatas, setFormDatas] = useState({});

  //部门列表
  const [addDeptList, setAddDeptList] = useState([]);
  //表单样式
  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 14 },
  };

  //获取列表数据
  const getlist = async parmas => {
    const { data, code } = await getItemDataRolePage(parmas);
    if (code === 200) {
      setData(data.records);
      setDataTotal(data.total);
    }
  };

  const { Option } = Select;
  //获取部门下拉
  const getDeptListInfos = async value => {
    const { code, data } = await getDeptListInfo(value);
    if (code === 200) {
      var children = [];
      var newArr = [];
      var deptres = new Map();
      newArr = data.filter(
        x =>
          x.deptFullName &&
          !deptres.has(x.deptFullName) &&
          deptres.set(x.deptFullName, 1),
      );
      newArr.forEach(item => {
        item.deptFullName &&
          children.push(<Option value={item.id}>{item.deptFullName}</Option>);
      });
      setChildren(children);
    }
  };

  useEffect(() => {
    getDeptListInfos();
  }, []);
  useEffect(() => {
    getlist(parmas);
  }, [parmas]);

  //动态表头
  const totalColumns = [
    {
      title: '数据名称',
      dataIndex: 'name',
      key: '1',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '数据描述',
      dataIndex: 'remark',
      key: '1',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      ellipsis: {
        showTitle: false,
      },
      key: '2',
      align: 'left',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '创建人 ',
      dataIndex: 'createUser',
      key: '3',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      filterIcon: filtered => handleGetIcon(filtered),
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },

    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: '10%',
      render: item => {
        return (
          <div className={styles.deleteBt}>
            {(buttonList.includes('/access/editDataRole') ||
              buttonList.includes('admin')) && (
              <span
                onClick={() =>
                  updateShowMoald({ title: '编辑数据权限', item: item })
                }
              >
                编辑
              </span>
            )}
            {(buttonList.includes('/access/deleteDataRole') ||
              buttonList.includes('admin')) && (
              <Popconfirm
                title="是否删除？"
                okText="是"
                cancelText="否"
                onConfirm={() => deleteItem(item)}
              >
                <a>删除</a>
              </Popconfirm>
            )}
          </div>
        );
      },
    },
  ];
  const onChangePageNumber = (value, size) => {
    setParmas({ page: value, limit: size });
  };
  const [columns, setColumns] = useState(totalColumns);
  //改变每页条数
  const pageSizeChange = value => {
    setParmas({ ...parmas, limit: value });
  };
  //新增
  const showMoald = porps => {
    let newFormObj = {};
    newFormObj['name'] = '';
    newFormObj['remark'] = '';
    newFormObj['depts'] = [];
    form.setFieldsValue(newFormObj);
    setModalTitle(porps.title);
    setVisibleModal(true);
  };
  //编辑
  const updateShowMoald = porps => {
    let newARR = [];
    newARR = porps.item.depts.map(items => {
      let { deptId, deptName } = items;
      return {
        value: deptId,
        label: deptName,
        key: deptId,
      };
    });
    let newFormObj = {};
    newFormObj['name'] = porps.item.name;
    newFormObj['remark'] = porps.item.remark;
    newFormObj['depts'] = newARR;
    form.setFieldsValue(newFormObj);
    setUpdataId(porps.item.id);
    setModalTitle(porps.title);
    setVisibleModal(true);
  };

  const handleOk = () => {
    setVisibleModal(false);
  };
  const handleCancel = () => {
    setVisibleModal(false);
  };
  //表单提交--新增
  const onFinishFormData = value => {
    let newArr = [];
    newArr = value.depts.map(items => {
      let { value, label } = items;
      return {
        deptId: value,
        deptName: label,
      };
    });
    value['depts'] = newArr;
    if (modalTitle == '新建数据权限') {
      addItem(value);
    } else if (modalTitle == '编辑数据权限') {
      updateItem(value);
    }
  };
  //删除数据角色
  const deleteItem = items => {
    delItem(items.id);
  };
  //向后端提交删除数据信息
  const delItem = async id => {
    const resp = await delDataRole(id);
    if (resp.code === 200) {
      message.success({ content: '删除成功!', key: 'editItem', duration: 2 });
      getlist(parmas);
      setVisibleModal(false);
    } else {
      message.error({ content: '操作失败!', key: 'editItem', duration: 2 });
    }
  };

  //向后端提交新增信息
  const addItem = async value => {
    value['id'] = null;
    const resp = await addDataRole(value);
    if (resp.code === 200) {
      message.success({ content: '成功!', key: 'editItem', duration: 2 });
      getlist(parmas);
      setVisibleModal(false);
    } else {
      message.error({ content: '失败!', key: 'editItem', duration: 2 });
    }
  };
  //向后端提交修改信息
  const updateItem = async value => {
    value['id'] = updataId;
    const resp = await updateDataRole(value);
    if (resp.code === 200) {
      message.success({ content: '编辑成功!', key: 'editItem', duration: 2 });
      getlist(parmas);
      setVisibleModal(false);
    } else {
      message.error({ content: '编辑失败!', key: 'editItem', duration: 2 });
    }
  };

  //搜索
  const handleSearchParams = props => {
    const { key, value } = props;
    setSearchParmas({ ...searchParmas, [key]: value });
    if (value == null || value == '') {
      let nullSearch = { page: 1, limit: 10 };
      getlist(nullSearch);
    }
  };

  // 搜索
  const handleOnSearch = () => {
    setParmas({ ...parmas, ...searchParmas, page: 1 });
  };
  const { limit, page } = parmas;
  return (
    <div className={styles.contentBox}>
      <div className={styles.card}>
        <Form form={seachForm} onFinish={handleOnSearch}>
          <div className={classNames(styles.searchInput, styles.search_bottom)}>
            <div>
              <p>数据名称</p>
              <Form.Item name="deptId1">
                <Input
                  allowClear
                  className={styles.selects}
                  onChange={e =>
                    handleSearchParams({ value: e.target.value, key: 'name' })
                  }
                  placeholder="请输入数据名称"
                />
              </Form.Item>
            </div>
            <div>
              <p></p>
              <br />
              <Button type="primary" htmlType="submit" onClick={handleOnSearch}>
                查询
              </Button>
            </div>
            <div></div>
            <div></div>
            <div></div>
          </div>
        </Form>
      </div>
      <div className={styles.card}>
        <div className={styles.cardOption}>
          <div className={styles.tabSwitch}></div>
          {(buttonList.includes('/access/insertDataRole') ||
            buttonList.includes('admin')) && (
            <div className={styles.cardRight}>
              <Button
                icon={<PlusOutlined />}
                type="primary"
                onClick={() => showMoald({ title: '新建数据权限' })}
              >
                数据权限
              </Button>
            </div>
          )}
        </div>
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          size="middle"
          className={styles.anTdTable}
        />
        <div className={styles.splitPigination}>
          <div>
            <Select
              defaultValue="10"
              style={{ width: 150 }}
              className={styles.selects}
              onChange={pageSizeChange}
            >
              <Option value="10">显示结果：10条</Option>
              <Option value="20">显示结果：20条</Option>
              <Option value="50">显示结果：50条</Option>
            </Select>
            <span className={styles.total}>共{dataTotal}条</span>
          </div>
          <Pagination
            total={dataTotal || 0}
            pageSize={limit}
            showSizeChanger={false}
            current={page}
            key={67}
            onChange={onChangePageNumber}
          />
        </div>
        <Modal
          title={`${modalTitle}`}
          visible={visibleModal}
          onOk={handleOk}
          onCancel={handleCancel}
          footer={null}
        >
          <Form
            {...layout}
            form={form}
            name="nest-messages"
            onFinish={onFinishFormData}
            initialValues={formDatas}
          >
            <>
              <Form.Item
                label="数据名称"
                name="name"
                rules={[{ required: true, message: '请输入数据名称' }]}
              >
                <Input allowClear={true} />
              </Form.Item>

              <Form.Item
                label="数据描述"
                name="remark"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                {/* <Input  allowClear={true}  /> */}

                <TextArea
                  allowClear
                  className={styles.selects}
                  autoSize={{ minRows: 1, maxRows: 4 }}
                />
              </Form.Item>
              <Form.Item
                label="数据选择"
                name="depts"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  mode="multiple"
                  allowClear
                  style={{ width: '100%' }}
                  placeholder="请选择部门"
                  labelInValue={true}
                >
                  {children}
                </Select>
              </Form.Item>
            </>

            <div className={styles.formButtons}>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </div>
          </Form>
        </Modal>
      </div>
    </div>
  );
};
