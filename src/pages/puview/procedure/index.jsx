import React, { useState, useEffect } from 'react';
import { CheckCircleOutlined, RightOutlined } from '@ant-design/icons';
import redDot_icon from '@/assets/redDot_icon.svg';
import 忽略 from '@/assets/忽略.svg';
import 进行中 from '@/assets/进行中.svg';
import 已完成 from '@/assets/已完成.svg';
import 拒绝 from '@/assets/拒绝.svg';
import dotsRed from '@/assets/dots-red.svg';
import styles from './index.less';

import G6 from '@antv/g6';
const Procedure = props => {
  const { processData } = props;
  const { nodes = [], edges = [] } = processData;
  const [show, setShow] = useState(false);
  const [trace, setTrace] = useState(false);
  const [graph, setGraph] = useState();

  const [showShUserName, setShowShUserName] = useState(false);
  const [shUserName, setShUserName] = useState();
  let x = 0;
  let y = 0;
  document.onmousemove = function(e) {
    x = e.screenX;
    y = e.screenY;
  };

  const tooltip = new G6.Tooltip({
    // offsetX 与 offsetY 需要加上父容器的 padding
    // 允许出现 tooltip 的 item 类型
    itemTypes: ['node'],
    // 自定义 tooltip 内容
    getContent: e => {
      const outDiv = document.createElement('div');
      outDiv.style.width = 'fit-content';
      outDiv.innerHTML = `
          <div>
            <div>审核意见: ${e.item.getModel().trace}</div>
          </div>`;
      return outDiv;
    },
    shouldBegin: e => {
      let res = false;
      setShow(false);
      setShowShUserName(false);
      if (e.item.getModel().trace) {
        // res = true;
        setShow(true);
        setTrace({
          label: e.item.getModel().label,
          trace: e.item.getModel().trace,
          update_time: e.item.getModel().update_time,
          username: e.item.getModel().username,
        });
      }
      if (e.item.getModel().status === '1') {
        setShowShUserName(true);
        setShUserName(e.item.getModel().ShUserName);
      }
      return res;
    },
  });
  G6.registerNode(
    'round-rect',
    {
      drawShape: function drawShape(cfg, group) {
        let img = '';
        if (cfg.status === '0') {
          img = 已完成;
        }
        if (cfg.status === '1') {
          img = 进行中;
        }
        if (cfg.status === '2') {
          img = 忽略;
        }
        if (cfg.status === '3') {
          img = 拒绝;
        }

        let width = cfg.style.width;
        let height = 50;
        if (!cfg.label) {
          width = 20;
          height = 50;
        }
        const stroke = cfg.style.stroke;
        const rect = group.addShape('rect', {
          attrs: {
            x: -width / 2,
            y: -25,
            width,
            height,
            radius: 15,
            stroke,
            lineWidth: 1.2,
            fillOpacity: 1,
          },
          name: 'rect-shape',
        });
        group.addShape('text', {
          attrs: {
            text: cfg.label,
            x: 0,
            y: 8,
            fontSize: 16,
            textAlign: 'center',
            textBaseline: 'center',
            fill: 'rgba(0,0,0,0.65)',
          },
          name: 'type-text-shape',
        });
        group.addShape('image', {
          attrs: {
            y: -35,
            x: -10,
            height: 30,
            width: 30,
            cursor: 'pointer',
            img: img,
          },
          name: 'node-icon',
        });
        graph.on('node:mouseenter', evt => {
          setShow(true);
        });
        graph.on('node:mouseout', evt => {
          setShow(false);
        });
        return rect;
      },
      getAnchorPoints: function getAnchorPoints() {
        return [
          [0, 0.5],
          [1, 0.5],
        ];
      },
      update: function update(cfg, item) {
        const group = item.getContainer();
        const children = group.get('children');
        const node = children[0];
        const circleLeft = children[1];
        const circleRight = children[2];
        const stroke = cfg.style.stroke;
        if (stroke) {
          node.attr('stroke', stroke);
          circleLeft.attr('fill', stroke);
          circleRight.attr('fill', stroke);
        }
      },
    },
    'single-node',
  );

  G6.registerEdge('polyline', {
    itemType: 'edge',
    draw: function draw(cfg, group) {
      const startPoint = cfg.startPoint;
      const endPoint = cfg.endPoint;

      const Ydiff = endPoint.y - startPoint.y;

      const slope = Ydiff !== 0 ? 500 / Math.abs(Ydiff) : 0;

      const cpOffset = 16;
      const offset = Ydiff < 0 ? cpOffset : -cpOffset;

      const line1EndPoint = {
        x: startPoint.x + slope,
        y: endPoint.y + offset,
      };
      const line2StartPoint = {
        x: line1EndPoint.x + cpOffset,
        y: endPoint.y,
      };

      // 控制点坐标
      const controlPoint = {
        x:
          ((line1EndPoint.x - startPoint.x) * (endPoint.y - startPoint.y)) /
            (line1EndPoint.y - startPoint.y) +
          startPoint.x,
        y: endPoint.y,
      };

      let path = [
        ['M', startPoint.x, startPoint.y],
        ['L', line1EndPoint.x, line1EndPoint.y],
        [
          'Q',
          controlPoint.x,
          controlPoint.y,
          line2StartPoint.x,
          line2StartPoint.y,
        ],
        ['L', endPoint.x, endPoint.y],
      ];

      if (Ydiff === 0) {
        path = [
          ['M', startPoint.x, startPoint.y],
          ['L', endPoint.x, endPoint.y],
        ];
      }

      const line = group.addShape('path', {
        attrs: {
          path,
          endArrow: {
            path: G6.Arrow.triangle(),
            fill: '#1A91FF',
          },
          stroke: '#1A91FF',
          lineWidth: 1.2,
        },
        name: 'path-shape',
      });
      return line;
    },
  });

  useEffect(() => {
    // 创建 G6 图实例
    let width = document.getElementById('container').scrollWidth * 1.5;
    if (nodes.length < 4) {
      width = width / 3;
    }
    if (nodes.length > 10) {
      width = width * 2;
    }
    if (nodes.length > 13) {
      width = width * 2;
    }
    const height = document.getElementById('container').scrollHeight || 500;
    const graph = new G6.Graph({
      container: 'container',
      width,
      height,
      fitView: true,
      plugins: [tooltip],
      modes: {
        default: ['drag-canvas'],
      },
      layout: {
        type: 'dagre',
        rankdir: 'LR',
        nodesep: 30,
        ranksep: 100,
      },
      defaultNode: {
        type: 'round-rect',
        style: {
          stroke: '#1A91FF',
          width: 180,
          fontSize: 16,
        },
        labelCfg: {
          style: {
            fill: '#000000A6',
            fontSize: 20,
            opacity: 0,
          },
        },
      },
      defaultEdge: {
        //边的样式
        type: 'polyline',
        endArrow: true,
        style: {
          stroke: '#1A91FF',
        },
      },
    });
    setGraph(graph);
    graph.on('node:click', function(evt) {});
  }, []);

  useEffect(() => {
    if (graph) {
      graph.data({ nodes: nodes, edges: edges });
      graph.render();
    }
  }, [processData, graph]);

  useEffect(() => {
    let box = document.getElementById('box');
    box.scrollLeft = box.scrollWidth / 5;
  }, []);
  return (
    <div>
      <div className={styles.tips}>
        <div>
          <img src={已完成} alt="" style={{ width: 20 }} />
          已审核节点
        </div>
        <div>
          <img src={进行中} alt="" style={{ width: 20 }} />
          审核中节点
        </div>
        <div>
          <img src={忽略} alt="" style={{ width: 20 }} />
          略过的节点
        </div>
        <div>
          <img src={拒绝} alt="" style={{ width: 20 }} />
          未通过节点
        </div>
      </div>
      <div className={styles.box} id="box">
        <div id="container"></div>
      </div>
      {show &&
        (trace ? (
          <div className={styles.trace_box} id="box">
            {' '}
            {`审核人员：${trace.username}`}
            <br />
            {`审核时间：${trace.update_time} `}
            <br />
            审核意见：{trace.trace || '无'}{' '}
          </div>
        ) : (
          <div className={styles.trace_box}> 审核意见：无</div>
        ))}
      {showShUserName && (
        <div className={styles.trace_box} id="box">
          {`审核中人员：${shUserName}`}
        </div>
      )}
    </div>
  );
};

export default Procedure;
