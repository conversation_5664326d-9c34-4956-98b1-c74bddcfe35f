import request from '@/utils/request';
import { BASE_URl } from "../../../utils/constant";

//获取收入列表list
export function getUserAccessListPage(parmas) {
  return request(`${BASE_URl}/access/getUserAccessListPage`, {
    method: 'post',
    data: parmas,
  });
}

//删除
export function deleteDepBudget(parmas) {
  return request(`${BASE_URl}/Finance/deleteDepBudget?id=${parmas}`, {
    method: 'delete',
  });
}


//编辑
export function editUserAccess(parmas) {
  return request(`${BASE_URl}/access/editUserAccess`, {
    method: 'post',
    data: parmas,
  });
}

//获取角色下拉框数据
export function getRolesList() {
  return request(`${BASE_URl}/access/getRoles`, {
    method: 'POST',
  });
}


//获取角色下拉框数据
export function getDataRoles() {
  return request(`${BASE_URl}/access/getDataRoles`, {
    method: 'POST',
  });
}

//获取部门下拉框数据
export function getDeptListInfo() {
  return request(`${BASE_URl}/dept/SWCARESdeptAll`, {
    method: 'POST',
  });
}





