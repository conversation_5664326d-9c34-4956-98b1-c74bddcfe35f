import React, { useState, useEffect } from 'react';
import {
  getUserAccessListPage,
  editUserAccess,
  getRolesList,
  getDataRoles,
  getDeptListInfo,
} from './service';
import {
  Button,
  Modal,
  Pagination,
  Table,
  Form,
  Tooltip,
  Input,
  Select,
  message,
} from 'antd';

import classNames from 'classnames';
import styles from './index.less';
import { MyContext } from '../../home';

const { Option } = Select;
const { TextArea, Search } = Input;
export default props => {
  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  useEffect(() => {
    setButtonList(context);
  }, [context]);
  useEffect(() => {
    setColumns(totalColumns);
  }, [buttonList]);

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [data, setData] = useState([]);
  const [dataTotal, setDataTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [parmas, setParmas] = useState({ page: 1, limit: 10 });

  //弹窗标题
  const [modalTitle, setModalTitle] = useState('');
  //
  const [modalItem, setModalItem] = useState({});
  const [visibleModal, setVisibleModal] = useState(false);

  const [form] = Form.useForm();
  const [seachForm] = Form.useForm();
  //部门列表
  const [deptList, setDeptList] = useState([]);
  //角色列表
  const [roleList, setRoleList] = useState([]);
  const [dataRoleList, setDataRoleList] = useState([]);
  // 点击按钮查询
  const [searchParmas, setSearchParmas] = useState({});
  const getlist = async parmas => {
    setLoading(true);
    const { data, code } = await getUserAccessListPage(parmas);
    if (code === 200) {
      setLoading(false);
      if (data.records && data.records.length > 0) {
        data.records.map(item => {
          item.key = item.userId;
        });
      }
      setData(data.records);
      setDataTotal(data.total);
    } else {
    }
  };

  //获取角色下拉
  const getRolesLists = async value => {
    const { code, data } = await getRolesList(value);
    if (code === 200) {
      if (data.records && data.records.length > 0) {
        data.records.map(item => {
          item.key = item.id;
        });
      }
      setRoleList(data);
    }
  };

  //获取数据权限下拉
  const getDataRolesList = async value => {
    const { code, data } = await getDataRoles(value);
    if (code === 200) {
      if (data && data.length > 0) {
        data.map(item => {
          item.key = item.id;
        });
      }
      setDataRoleList(data);
    }
  };

  //获取部门下拉
  const getDeptListInfos = async value => {
    const { code, data } = await getDeptListInfo(value);
    if (code === 200) {
      const addDeptList = new Set();
      data.forEach(item => {
        let repeat = false;
        addDeptList.forEach(value => {
          if (value.deptFullName === item.deptFullName) {
            repeat = true;
          }
        });
        if (!repeat) {
          item.deptFullName && addDeptList.add(item);
        }
      });
      setDeptList(addDeptList);
    }
  };

  useEffect(() => {
    getDeptListInfos();
    getRolesLists();
    getDataRolesList();
  }, []);
  useEffect(() => {
    getlist(parmas);
  }, [parmas]);
  //动态表头
  const totalColumns = [
    {
      title: '编号',
      dataIndex: 'index',
      key: 'index',
      align: 'left',
      width: 80,
      ellipsis: {
        showTitle: false,
      },
      render: (value, item, index) => index + 1,
    },
    {
      title: '部门',
      dataIndex: 'deptName',
      key: '1',
      align: 'left',
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '员工姓名',
      dataIndex: 'userName',
      ellipsis: {
        showTitle: false,
      },
      key: '2',
      width: 130,
      align: 'left',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '工号',
      dataIndex: 'userNum',
      key: '3',
      width: 130,
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      filterIcon: filtered => handleGetIcon(filtered),
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '角色',
      align: 'left',
      dataIndex: 'roles',
      render: text => {
        return (
          <div className={styles.tag}>
            {text.map(item => (
              <Tooltip placement="topLeft" title={item.name}>
                <div>{item.name}</div>
              </Tooltip>
            ))}
          </div>
        );
      },
      key: 'roles',
    },
    {
      title: '数据权限',
      align: 'left',
      dataIndex: 'DataRole',
      render: text => {
        return (
          <div>
            {text.map(item => (
              <Tooltip placement="topLeft" title={item.name}>
                <span>{item.name}</span>
              </Tooltip>
            ))}
          </div>
        );
      },
      key: 'roles',
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: '10%',
      render: item => {
        return (
          <div className={styles.editBt}>
            {(buttonList.includes('/access/editUserAccess') ||
              buttonList.includes('admin')) && (
              <span
                onClick={() => showMoald({ title: '编辑权限', item: item })}
              >
                编辑权限
              </span>
            )}
          </div>
        );
      },
    },
  ];
  const onChangePageNumber = (value, size) => {
    setParmas({ ...searchParmas, page: value, limit: size });
  };

  const [keys, setKeys] = useState([]);

  //多选
  const rowSelection = {
    // 设置key值单选框才能被选中
    onChange: (selectedRowKeys, selectedRows) => {
      setKeys(selectedRowKeys);
    },
    onSelectAll: (selected, selectedRows, changeRows) => {},
    // selectedRowKeys: keys,
  };

  const [columns, setColumns] = useState(totalColumns);
  //改变每页条数
  const pageSizeChange = value => {
    setParmas({ ...parmas, limit: value });
  };

  const [selectItem, setSelectItem] = useState({});

  const showMoald = porps => {
    const { title, item } = porps;
    setSelectItem({ userId: item.userId, userNum: item.userNum });
    setModalTitle(title);
    setVisibleModal(true);
  };
  const handleOk = () => {
    setVisibleModal(false);
  };

  const handleCancel = () => {
    setVisibleModal(false);
  };

  //表单提交
  const onFinishFormData = value => {
    const data = {
      userId: selectItem.userId,
      userNum: selectItem.userNum,
      roleIds: [...value.roleIds],
      dataRoles: [...(value.dataRoles || '')],
    };
    editItem(data);
  };

  const editItem = async value => {
    const resp = await editUserAccess(value);
    if (resp.code === 200) {
      message.success({ content: '成功!', key: 'editItem', duration: 2 });
      getlist(parmas);
      setVisibleModal(false);
    } else {
      message.error({ content: '失败!', key: 'editItem', duration: 2 });
    }
  };

  /**
   * 搜索值变化
   */
  const handleSearchParams = props => {
    const { key, value } = props;
    // 时间插件获取值不同
    if (key === 'year') {
      setSearchParmas({
        ...searchParmas,
        startYear: value[0],
        endYear: value[1],
      });
    } else {
      setSearchParmas({ ...searchParmas, [key]: value });
    }
  };
  // 搜索
  const handleOnSearch = () => {
    setParmas({ ...parmas, ...searchParmas, page: 1 });
  };
  const { limit, page } = parmas;

  const resetSearch = () => {
    let resetParam = {
      deptName: '',
      userName: '',
      userId: '',
    };
    setSearchParmas({
      deptName: '',
      userName: '',
      userId: '',
    });
    seachForm.setFieldsValue({
      deptName: '',
      userName: '',
      userId: '',
    });
    setParmas({ ...parmas, ...resetParam, page: 1 });
  };

  return (
    <div className={styles.contentBox}>
      <div className={styles.card}>
        <Form form={seachForm} onFinish={handleOnSearch}>
          <div className={classNames(styles.searchInput, styles.search_bottom)}>
            <div>
              <p>部门名称</p>
              <Form.Item name="deptName">
                <Select
                  showSearch
                  defaultActiveFirstOption={false}
                  placeholder="选择部门"
                  style={{ width: '100%' }}
                  filterOption={false}
                  allowClear={true}
                  className={styles.selects}
                  notFoundContent={null}
                  onChange={value =>
                    handleSearchParams({ value, key: 'deptName' })
                  }
                >
                  {deptList.map(item => (
                    <Option value={item.deptFullName} key={item.deptFullName}>
                      {item.deptFullName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
            <div>
              <p>员工姓名</p>
              <Form.Item name="userName">
                <Input
                  onChange={e =>
                    handleSearchParams({
                      value: e.target.value,
                      key: 'userName',
                    })
                  }
                ></Input>
              </Form.Item>
            </div>
            <div>
              <p>工号</p>
              <Form.Item name="userId">
                <Input
                  onChange={e =>
                    handleSearchParams({ value: e.target.value, key: 'userId' })
                  }
                ></Input>
              </Form.Item>
            </div>
            <>
              <div>
                <p></p>
                <br />
                <Button onClick={resetSearch}>重置</Button>
                <Button type="primary" htmlType="submit">
                  查询
                </Button>
              </div>
            </>
          </div>
        </Form>
      </div>

      <div className={styles.card}>
        <div className={styles.cardOption}>
          <div className={styles.tabSwitch}></div>
          {/* <div className={styles.cardRight}>
            <Button
              type="primary"
              onClick={() => showMoald({ title: '编辑权限' })}
            >编辑权限
            </Button>
          </div> */}
        </div>
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          // rowSelection={{
          //   ...rowSelection,
          // }}
          loading={loading}
          size="middle"
          className={styles.anTdTable}
        />
        <div className={styles.splitPigination}>
          <div>
            <Select
              defaultValue="10"
              style={{ width: 150 }}
              className={styles.selects}
              onChange={pageSizeChange}
            >
              <Option value="10">显示结果：10条</Option>
              <Option value="20">显示结果：20条</Option>
              <Option value="50">显示结果：50条</Option>
            </Select>
            <span className={styles.total}>共{dataTotal}条</span>
          </div>
          <Pagination
            total={dataTotal || 0}
            pageSize={limit}
            showSizeChanger={false}
            current={page}
            key={67}
            onChange={onChangePageNumber}
          />
        </div>
        <Modal
          title={`${modalTitle}`}
          visible={visibleModal}
          onOk={handleOk}
          onCancel={handleCancel}
          footer={null}
        >
          <Form
            layout="vertical"
            form={form}
            name="nest-messages"
            onFinish={onFinishFormData}
          >
            <Form.Item
              label="角色"
              name="roleIds"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Select
                showSearch
                defaultActiveFirstOption={false}
                placeholder="请选择角色"
                mode="multiple"
                style={{ width: '80%' }}
                filterOption={false}
                allowClear={true}
                className={styles.selects}
                notFoundContent={null}
              >
                {roleList.map(item => (
                  <Option value={item.id} key={item.id}>
                    {item.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label="数据权限" name="dataRoles">
              <Select
                showSearch
                defaultActiveFirstOption={false}
                placeholder="请选择数据权限"
                mode="multiple"
                style={{ width: '80%' }}
                filterOption={false}
                allowClear={true}
                className={styles.selects}
                notFoundContent={null}
              >
                {dataRoleList.map(item => (
                  <Option value={item.id} key={item.id}>
                    {item.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item style={{ marginBottom: 0 }}>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
};
