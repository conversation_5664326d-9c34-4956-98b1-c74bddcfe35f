import React, { useState, useEffect } from 'react';
import {
  getRolesListPage,
  editRole,
  deleteRole,
  getNowAccess,
  insertRole,
} from './service';
import {
  Button,
  Modal,
  Checkbox,
  Pagination,
  Table,
  Form,
  Tooltip,
  Switch,
  Input,
  InputNumber,
  Select,
  Slider,
  Popconfirm,
  Radio,
  Row,
  Col,
  DatePicker,
  message,
  Upload,
} from 'antd';
import {
  ClockCircleOutlined,
  PlusOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import classNames from 'classnames';

import styles from './index.less';
import { MyContext } from '../../home';

import moment from 'moment';

const { Option } = Select;
const CheckboxGroup = Checkbox.Group;

const { TextArea, Search } = Input;

export default props => {
  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  useEffect(() => {
    setButtonList(context);
  }, [context]);
  useEffect(() => {
    setColumns(totalColumns);
  }, [buttonList]);

  //动态表头
  const totalColumns = [
    {
      title: '编号',
      dataIndex: 'index',
      key: 'index',
      align: 'left',
      width: 80,
      ellipsis: {
        showTitle: false,
      },
      render: (value, item, index) => index + 1,
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '角色描述',
      dataIndex: 'remark',
      ellipsis: {
        showTitle: false,
      },
      key: 'remark',
      align: 'left',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '创建人',
      dataIndex: 'createUser',
      key: 'createUser',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      filterIcon: filtered => handleGetIcon(filtered),
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '状态',
      align: 'left',
      dataIndex: 'status',
      key: 'status',
      render: (value, item) => {
        return (
          item.name !== 'admin' && (
            <div>
              {' '}
              <Switch
                style={{ borderRadius: 15, marginRight: 10 }}
                checked={value == 1}
                onClick={() => switchClick(item)}
                className={value == 1 ? styles.switch : ''}
              />
              {value == 1 ? (
                <span>已启用</span>
              ) : (
                <span style={{ color: '#A3A3A3' }}>已停用</span>
              )}
            </div>
          )
        );
      },

      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: '10%',
      render: (item, record) => {
        return (
          record.name !== 'admin' && (
            <div className={styles.deleteBt}>
              {(buttonList.includes('/access/editRole') ||
                buttonList.includes('admin')) && (
                <span
                  onClick={() => showMoald({ title: '编辑角色', item: item })}
                >
                  编辑
                </span>
              )}
              {(buttonList.includes('/projectList/delStandUser') ||
                buttonList.includes('admin')) && (
                <Popconfirm
                  title="是否删除？"
                  okText="是"
                  cancelText="否"
                  onConfirm={() => deleteItem(item)}
                >
                  <a>删除</a>
                </Popconfirm>
              )}
            </div>
          )
        );
      },
    },
  ];

  const [data, setData] = useState([]);
  const [dataTotal, setDataTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [parmas, setParmas] = useState({ page: 1, limit: 10 });

  //弹窗标题
  const [modalTitle, setModalTitle] = useState('');
  //
  const [visibleModal, setVisibleModal] = useState(false);

  const [form] = Form.useForm();
  const [seachForm] = Form.useForm();

  //部门列表
  const [menuList, setMenuList] = useState([]);
  // 点击按钮查询
  const [searchParmas, setSearchParmas] = useState({});

  const getlist = async parmas => {
    setLoading(true);
    const { data, code } = await getRolesListPage(parmas);
    if (code === 200) {
      setLoading(false);
      if (data.records && data.records.length > 0) {
        data.records.map(item => {
          item.key = item.id;
        });
      }
      setData(data.records);
      setDataTotal(data.total);
    } else {
    }
  };
  const getNowAccessMune = async value => {
    const { data, code } = await getNowAccess(value);
    if (code === 200) {
      console.log('权限', data);
      setMenuList(data);
    }
  };

  useEffect(() => {
    getlist(parmas);
  }, [parmas]);

  const onChangePageNumber = (value, size) => {
    setParmas({ page: value, limit: size });
  };

  const [keys, setKeys] = useState([]);
  //多选
  const rowSelection = {
    // 设置key值单选框才能被选中
    onChange: (selectedRowKeys, selectedRows) => {
      setKeys(selectedRowKeys);
    },
    onSelectAll: (selected, selectedRows, changeRows) => {},
    // selectedRowKeys: keys,
  };

  const [columns, setColumns] = useState(totalColumns);
  //改变每页条数
  const pageSizeChange = value => {
    setParmas({ ...parmas, limit: value });
  };

  const [modalItem, setModalItem] = useState({ status: '0' });
  const showMoald = porps => {
    const { title, item } = porps;
    setModalItem({ ...modalItem, ...item });
    setModalTitle(title);
    getNowAccessMune((item && item.id) || null);
    if (item) {
      form.setFieldsValue({
        roleName: item.name,
        remark: item.remark,
        id: item.id,
      });
    } else {
      form.setFieldsValue({
        roleName: '',
        remark: '',
        id: null,
      });
    }
    setVisibleModal(true);
  };
  const addRoleSwitchClick = props => {
    const { value, type } = props;
    setModalItem({ ...modalItem, status: value ? '1' : '0' });
  };

  const handleOk = () => {
    setVisibleModal(false);
  };

  const handleCancel = () => {
    setVisibleModal(false);
  };

  console.log(modalItem, 'status');

  //表单提交
  const onFinishFormData = value => {
    const accessVoList = [];
    menuList.forEach(item => {
      console.log(item, 'itemitem');
      item.menu.forEach(menuItem => {
        if (menuItem.check === '1') {
          accessVoList.push({
            id: menuItem.accessId,
          });
        }
        menuItem.handledAccess.forEach(valueItme => {
          if (valueItme.check === '1') {
            accessVoList.push({
              id: valueItme.accessId,
            });
          }
        });
      });
    });
    if (modalItem && modalItem.id) {
      editItem({
        accessVoList,
        id: modalItem.id || null,
        status: modalItem.status,
        name: value.roleName,
        remark: value.remark,
      });
    } else {
      addItem({
        accessVoList,
        id: null,
        status: modalItem.status,
        name: value.roleName,
        remark: value.remark,
      });
    }
  };

  const switchClick = item => {
    editItem({ ...item, status: item.status === '1' ? '0' : '1' });
  };

  const editItem = async value => {
    const resp = await editRole(value);
    if (resp.code === 200) {
      message.success({ content: '成功!', key: 'editItem', duration: 2 });
      getlist(parmas);
      setVisibleModal(false);
    } else {
      message.error({ content: '失败!', key: 'editItem', duration: 2 });
    }
  };

  const addItem = async value => {
    const resp = await insertRole(value);
    if (resp.code === 200) {
      message.success({ content: '成功!', key: 'addItem', duration: 2 });
      getlist(parmas);
      setVisibleModal(false);
    } else {
      message.error({ content: '失败!', key: 'addItem', duration: 2 });
    }
  };

  /**
   * 搜索值变化
   */
  const handleSearchParams = props => {
    const { key, value } = props;
    // 时间插件获取值不同
    if (key === 'year') {
      setSearchParmas({
        ...searchParmas,
        startYear: value[0],
        endYear: value[1],
      });
    } else {
      setSearchParmas({ ...searchParmas, [key]: value });
    }
  };
  // 搜索
  const handleOnSearch = () => {
    setParmas({ ...parmas, ...searchParmas, page: 1 });
  };
  const { limit, page } = parmas;

  const resetSearch = () => {
    setSearchParmas({});
    seachForm.setFieldsValue({
      deptId1: '',
      costType1: '',
      year: '',
    });
  };
  //删除
  const deleteItem = value => {
    deleteItems(value.id);
  };

  //删除
  const deleteItems = async value => {
    const { data, code } = await deleteRole(value);
    if (code === 200) {
      message.success({ content: '删除成功!', key: 'deleteRole', duration: 2 });
      getlist({ ...parmas });
    }
  };

  const menuCheckAll = value => {
    menuList.forEach(item => {
      item.menu.forEach(menuItem => {
        menuItem.check = value ? '1' : '0';
        menuItem.handledAccess.forEach(valueItme => {
          valueItme.check = value ? '1' : '0';
        });
      });
    });
    setMenuList([...menuList]);
  };

  const menuCheckboxChenge = props => {
    const { itemIndex, menuIndex, valueIndex, value } = props;
    const list = [...menuList];
    if (itemIndex !== undefined) {
      if (menuIndex !== undefined) {
        if (valueIndex !== undefined) {
          list[itemIndex].menu[menuIndex].handledAccess[
            valueIndex
          ].check = value ? '1' : '0';
        } else {
          list[itemIndex].menu[menuIndex].check = value ? '1' : '0';
        }
      } else {
        list[itemIndex].check = value ? '1' : '0';
        list[itemIndex].menu.forEach(element => {
          element.check = value ? '1' : '0';
        });
      }
    }
    setMenuList([...list]);
  };

  return (
    <div className={styles.contentBox}>
      <div className={styles.card}>
        <Form form={seachForm} onFinish={handleOnSearch}>
          <div className={classNames(styles.searchInput, styles.search_bottom)}>
            <div>
              <p>角色名称</p>
              <Form.Item name="name">
                <Input
                  onChange={e =>
                    handleSearchParams({ value: e.target.value, key: 'name' })
                  }
                ></Input>
              </Form.Item>
            </div>
            <div>
              <p>角色状态</p>
              <Form.Item name="status">
                <Select
                  style={{ width: '100%' }}
                  allowClear={true}
                  defaultActiveFirstOption={false}
                  placeholder="选择角色状态"
                  onChange={value =>
                    handleSearchParams({ value, key: 'status' })
                  }
                  className={styles.selects}
                  notFoundContent={null}
                >
                  <Option value={'1'}>开启</Option>
                  <Option value={'0'}>停用</Option>
                </Select>
              </Form.Item>
            </div>
            <>
              <div>
                <p></p>
                <br />
                <Button type="primary" htmlType="submit">
                  查询
                </Button>
              </div>
            </>
            <div></div>
            <div></div>
          </div>
        </Form>
      </div>

      <div className={styles.card}>
        <div className={styles.cardOption}>
          <div className={styles.tabSwitch}></div>
          <div className={styles.cardRight}>
            {(buttonList.includes('/projectList/addStandUser') ||
              buttonList.includes('admin')) && (
              <Button
                type="primary"
                onClick={() => showMoald({ title: '新建角色' })}
              >
                新建角色
              </Button>
            )}
          </div>
        </div>
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          // rowSelection={{
          //   ...rowSelection,
          // }}
          loading={loading}
          size="middle"
          className={styles.anTdTable}
        />
        <div className={styles.splitPigination}>
          <div>
            <Select
              defaultValue="10"
              style={{ width: 150 }}
              className={styles.selects}
              onChange={pageSizeChange}
            >
              <Option value="10">显示结果：10条</Option>
              <Option value="20">显示结果：20条</Option>
              <Option value="50">显示结果：50条</Option>
            </Select>
            <span className={styles.total}>共{dataTotal}条</span>
          </div>
          <Pagination
            total={dataTotal || 0}
            pageSize={limit}
            showSizeChanger={false}
            current={page}
            key={67}
            onChange={onChangePageNumber}
          />
        </div>
        <Modal
          title={`${modalTitle}`}
          visible={visibleModal}
          onOk={handleOk}
          onCancel={handleCancel}
          footer={null}
        >
          <Form
            layout="vertical"
            form={form}
            name="nest-messages"
            onFinish={onFinishFormData}
          >
            <Row>
              <Col span={16}>
                <Form.Item
                  label="角色名称"
                  name="roleName"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Input className={styles.selects} />
                </Form.Item>
              </Col>
              <Col span={1} />
              <Col span={6}>
                <Form.Item label="角色状态">
                  <Switch
                    style={{ borderRadius: 15, marginRight: 10 }}
                    checked={modalItem && modalItem.status == 1}
                    className={
                      modalItem && modalItem.status == 1 ? styles.switch : ''
                    }
                    onClick={value =>
                      addRoleSwitchClick({ value, type: '角色状态' })
                    }
                  />
                  {modalItem && modalItem.status == 1 ? (
                    <span>已启用</span>
                  ) : (
                    <span style={{ color: '#A3A3A3' }}>已停用</span>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Form.Item label="角色描述" name="remark">
              <Input className={styles.selects} />
            </Form.Item>
            <Form.Item label="权限选择">
              <Checkbox onChange={e => menuCheckAll(e.target.checked)}>
                全选
              </Checkbox>
              {menuList &&
                menuList.length > 0 &&
                menuList.map((item, itemIndex) => {
                  return (
                    <div className={styles.menu_box} key={itemIndex}>
                      <div>{item.title}</div>
                      <div>
                        {item.menu &&
                          item.menu.map((menu, menuIndex) => {
                            return (
                              <div
                                className={classNames(styles.flex, styles.mens)}
                                key={menuIndex}
                              >
                                <Checkbox
                                  onChange={e =>
                                    menuCheckboxChenge({
                                      itemIndex,
                                      menuIndex,
                                      value: e.target.checked,
                                    })
                                  }
                                  checked={menu.check == '1'}
                                  style={{ marginLeft: 10 }}
                                >
                                  {menu.title}
                                </Checkbox>
                                <div className={styles.flex}>
                                  {menu.handledAccess &&
                                    menu.handledAccess.length > 0 &&
                                    menu.handledAccess.map(
                                      (value, valueIndex) => (
                                        <div key={valueIndex}>
                                          <Checkbox
                                            checked={value.check == '1'}
                                            onChange={e =>
                                              menuCheckboxChenge({
                                                itemIndex,
                                                menuIndex,
                                                valueIndex,
                                                value: e.target.checked,
                                              })
                                            }
                                          >
                                            {value.title}
                                          </Checkbox>
                                        </div>
                                      ),
                                    )}
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    </div>
                  );
                })}
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
};
