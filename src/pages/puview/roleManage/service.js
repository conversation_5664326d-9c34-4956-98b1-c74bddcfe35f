import request from '@/utils/request';
import { BASE_URl } from "../../../utils/constant";

//获取列表list
export function getRolesListPage(parmas) {
  return request(`${BASE_URl}/access/getRolesListPage`, {
    method: 'post',
    data: parmas,
  });
}

//删除
export function deleteRole(parmas) {
  return request(`${BASE_URl}/access/deleteRole?roleId=${parmas}`, {
    method: 'post',
  });
}


//编辑
export function editRole(parmas) {
  return request(`${BASE_URl}/access/editRole`, {
    method: 'post',
    data: parmas,
  });
}

//新增
export function insertRole(parmas) {
  return request(`${BASE_URl}/access/insertRole`, {
    method: 'post',
    data: parmas,
  });
}

//获取下拉框数据
export function getNowAccess(parmas) {
  return request(`${BASE_URl}/access/getNowAccess?roleId=${parmas}`, {
    method: 'POST',
  });
}



