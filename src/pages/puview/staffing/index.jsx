import React, { useState, useEffect } from 'react';
import {
  getStandUserListPage,
  editDepBudget,
  delStandUser,
  getDeptListInfo,
  getTrialDic,
  addStandUse,
  editStandUse,
  getNameListInfo,
} from './service';
import {
  Button,
  Modal,
  Pagination,
  Table,
  Form,
  Tooltip,
  Input,
  InputNumber,
  Select,
  Slider,
  Popconfirm,
  Radio,
  Row,
  Col,
  DatePicker,
  message,
  Upload,
} from 'antd';
import {
  ClockCircleOutlined,
  PlusOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import classNames from 'classnames';
import styles from './index.less';
import moment from 'moment';
import { MyContext } from '../../home';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea, Search } = Input;
const RadioButton = Radio.Button;
export default porps => {
  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  useEffect(() => {
    setButtonList(context);
  }, [context]);
  useEffect(() => {
    setColumns(totalColumns);
  }, [buttonList]);

  const [data, setData] = useState([]);
  const [dataTotal, setDataTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [parmas, setParmas] = useState({ page: 1, limit: 10 });

  //弹窗标题
  const [modalTitle, setModalTitle] = useState('');
  const [modalItem, setModalItem] = useState({});
  const [visibleModal, setVisibleModal] = useState(false);

  const [form] = Form.useForm();
  const [seachForm] = Form.useForm();

  //部门列表
  const [addDeptList, setAddDeptList] = useState([]);
  // 点击按钮查询
  const [searchParmas, setSearchParmas] = useState({});
  //人员列表
  const [nameList, setNameList] = useState([]);
  const getlist = async parmas => {
    setLoading(true);
    const { data, code } = await getStandUserListPage(parmas);
    if (code === 200) {
      setLoading(false);
      setData(data.records);
      setDataTotal(data.total);
    } else {
    }
  };
  const [examineList, setExamineList] = useState([]);
  //获取审核节点
  const getTrialDics = async () => {
    const { data, code } = await getTrialDic();
    if (code === 200) {
      const list = [];
      data.forEach(item => {
        if (
          item.type_name === '项目审核节点' &&
          item.code !== '0' &&
          item.code !== '9'
        ) {
          list.push(item);
        }
      });
      setExamineList(list);
    }
  };
  //获取部门下拉
  const getDeptListInfos = async value => {
    const { code, data } = await getDeptListInfo(value);
    if (code === 200) {
      const addDeptList = new Set();
      data.forEach(item => {
        let repeat = false;
        addDeptList.forEach(value => {
          if (value.deptFullName === item.deptFullName) {
            repeat = true;
          }
        });
        if (!repeat) {
          item.deptFullName && addDeptList.add(item);
        }
      });
      setAddDeptList(addDeptList);
    }
  };
  useEffect(() => {
    getTrialDics();
    getDeptListInfos();
  }, []);
  useEffect(() => {
    getlist(parmas);
  }, [parmas]);
  //动态表头
  const totalColumns = [
    {
      title: '部门名称',
      dataIndex: 'dept',
      key: '1',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '审核节点',
      dataIndex: 'trialName',
      key: 'trialId',
      align: 'center',
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: '审核员工工号',
      dataIndex: 'username',
      ellipsis: {
        showTitle: false,
      },
      key: '2',
      align: 'left',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '审核员工姓名',
      dataIndex: 'name',
      key: '3',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '创建时间',
      align: 'left',
      render: value => moment(value).format('YYYY-MM-DD'),
      dataIndex: 'createTime',
      key: '4',
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: '10%',
      render: item => {
        return (
          <div className={styles.deleteBt}>
            {(buttonList.includes('/Finance/delStandUser') ||
              buttonList.includes('admin')) && (
              <span
                style={{ marginLeft: 10 }}
                onClick={() => showMoald({ title: '编辑审批人员', item: item })}
              >
                编辑
              </span>
            )}
            {(buttonList.includes('/Finance/delStandUser') ||
              buttonList.includes('admin')) && (
              <Popconfirm
                title="是否删除？"
                okText="是"
                cancelText="否"
                onConfirm={() => deleteItem(item)}
              >
                <a>删除</a>
              </Popconfirm>
            )}
          </div>
        );
      },
    },
  ];
  const onChangePageNumber = (value, size) => {
    setParmas({ ...parmas, page: value, limit: size });
  };

  const [columns, setColumns] = useState(totalColumns);
  //改变每页条数
  const pageSizeChange = value => {
    setParmas({ ...parmas, limit: value });
  };

  const showMoald = porps => {
    const { title, item } = porps;
    if (title === '编辑审批人员') {
      setModalItem({ ...item });
      setModalTitle(title);
      form.setFieldsValue({
        username: `${item.name}-${item.dept}`,
      });
    } else {
      setModalTitle(title);
      form.setFieldsValue({
        year: '',
        depart: '',
        totalBudget: '',
      });
    }
    setVisibleModal(true);
  };
  const handleOk = () => {
    setVisibleModal(false);
  };

  const handleCancel = () => {
    setVisibleModal(false);
  };

  //表单提交
  const onFinishFormData = value => {
    if (modalTitle !== '新增审批人员') {
      editItem(value);
    } else {
      addItem(value);
    }
  };

  //新增
  const addItem = async value => {
    const resp = await addStandUse(value);
    if (resp.code === 200) {
      message.success({ content: '成功!', key: 'editItem', duration: 2 });
      getlist(parmas);
      setVisibleModal(false);
    } else {
      message.error({ content: '失败!', key: 'editItem', duration: 2 });
    }
  };

  //编辑
  const editItem = async value => {
    const resp = await editStandUse({
      username: value.username,
      id: `${modalItem.id}`,
    });
    if (resp.code === 200) {
      message.success({ content: '成功!', key: 'editItem', duration: 2 });
      getlist(parmas);
      setVisibleModal(false);
    } else {
      message.error({ content: '失败!', key: 'editItem', duration: 2 });
    }
  };

  /**
   * 搜索值变化
   */
  const handleSearchParams = props => {
    const { key, value } = props;
    // 时间插件获取值不同
    if (key === 'year') {
      setSearchParmas({
        ...searchParmas,
        startYear: value[0],
        endYear: value[1],
      });
    } else {
      setSearchParmas({ ...searchParmas, [key]: value });
    }
  };
  // 搜索
  const handleOnSearch = () => {
    setParmas({ ...parmas, ...searchParmas, page: 1 });
  };
  const { limit, page } = parmas;

  const resetSearch = () => {
    setSearchParmas({});
    seachForm.setFieldsValue({
      username: '',
      trial_id: '',
    });
  };
  //删除
  const deleteItem = value => {
    deleteItems({ id: value.id, departmentId: value.departmentId });
  };

  const deleteItems = async value => {
    const { id, departmentId } = value;
    const { data, code } = await delStandUser(id);
    if (code === 200) {
      message.success({
        content: '删除成功!',
        key: 'delStandUser',
        duration: 2,
      });
      getlist({ ...parmas, deptId: departmentId });
    }
  };

  let timeout;
  function fetch(value, callback) {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    function fake() {
      const getNameListInfos = async value => {
        const { code, data } = await getNameListInfo({
          page: 1,
          limit: 10,
          employeeName: value,
        });
        if (code === 200) {
          callback(data.records);
        }
      };
      getNameListInfos(value);
    }
    timeout = setTimeout(fake, 300);
  }
  const handleSearch = value => {
    if (value) {
      fetch(value, data => setNameList(data));
    }
  };
  return (
    <div className={styles.contentBox}>
      <div className={styles.card}>
        <Form form={seachForm} onFinish={handleOnSearch}>
          <div className={classNames(styles.searchInput, styles.search_bottom)}>
            <div>
              <p>人员</p>
              <Form.Item name="username">
                <Select
                  showSearch
                  defaultActiveFirstOption={false}
                  placeholder=""
                  showArrow={false}
                  className={styles.selects}
                  filterOption={false}
                  allowClear
                  onSearch={handleSearch}
                  notFoundContent={null}
                  onChange={value =>
                    handleSearchParams({ value: value, key: 'username' })
                  }
                >
                  {nameList.length > 0 &&
                    nameList.map((item, index) => (
                      <Option value={`${item.username}`} key={index}>
                        {item.employee_name}-{item.deptName}
                      </Option>
                    ))}
                </Select>
              </Form.Item>
            </div>
            <div>
              <p>审核节点</p>
              <Form.Item name="trial_id">
                <Select
                  showSearch
                  defaultActiveFirstOption={false}
                  placeholder="选择审核节点"
                  filterOption={false}
                  // onSearch={handleSearch}
                  allowClear
                  className={styles.selects}
                  notFoundContent={null}
                  filterOption={(input, option) =>
                    option.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                  onChange={value =>
                    handleSearchParams({ value: value, key: 'trial_id' })
                  }
                >
                  {examineList.map(item => (
                    <Option value={`${item.code}`} key={item.code}>
                      {item.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
            <div>
              <p></p>
              <br />
              {/* <Button onClick={resetSearch}>重置</Button> */}
              <Button type="primary" htmlType="submit">
                查询
              </Button>
            </div>
            <div></div>
            <div></div>
          </div>
        </Form>
      </div>

      <div className={styles.card}>
        <div className={styles.cardOption}>
          <div className={styles.tabSwitch}>
            <Radio.Group
              defaultValue="b"
              buttonStyle="solid"
              onChange={e => porps.tabChange(e.target.value)}
            >
              <RadioButton value="a">流程配置</RadioButton>
              <RadioButton value="b">人员配置</RadioButton>
            </Radio.Group>
          </div>
          <div className={styles.cardRight}>
            {(buttonList.includes('/Finance/insertDepBudget') ||
              buttonList.includes('admin')) && (
              <Button
                type="primary"
                onClick={() => showMoald({ title: '新增审批人员' })}
              >
                新增审批人员
              </Button>
            )}
          </div>
        </div>
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          // rowSelection={rowSelection}
          loading={loading}
          size="middle"
          className={styles.anTdTable}
        />
        <div className={styles.splitPigination}>
          <div>
            <Select
              defaultValue="10"
              style={{ width: 150 }}
              className={styles.selects}
              onChange={pageSizeChange}
            >
              <Option value="10">显示结果：10条</Option>
              <Option value="20">显示结果：20条</Option>
              <Option value="50">显示结果：50条</Option>
            </Select>
            <span className={styles.total}>共{dataTotal}条</span>
          </div>
          <Pagination
            total={dataTotal || 0}
            pageSize={limit}
            showSizeChanger={false}
            current={page}
            key={67}
            onChange={onChangePageNumber}
          />
        </div>
        <Modal
          title={`${modalTitle}`}
          visible={visibleModal}
          onOk={handleOk}
          onCancel={handleCancel}
          footer={null}
        >
          <Form
            layout="vertical"
            form={form}
            name="nest-messages"
            onFinish={onFinishFormData}
          >
            <Form.Item
              label="员工"
              name="username"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Select
                showSearch
                defaultActiveFirstOption={false}
                placeholder="请输入"
                showArrow={false}
                className={styles.selects}
                style={{ width: 300 }}
                filterOption={false}
                onSearch={handleSearch}
                notFoundContent={null}
              >
                {nameList.length > 0 &&
                  nameList.map((item, index) => (
                    <Option value={`${item.username}`} key={index}>
                      {item.employee_name}-{item.deptName}
                    </Option>
                  ))}
              </Select>
            </Form.Item>
            {modalTitle !== '编辑审批人员' && (
              <>
                {' '}
                <Form.Item label="部门" name="deptId">
                  <Select
                    showSearch
                    defaultActiveFirstOption={false}
                    placeholder="选择部门"
                    filterOption={false}
                    style={{ width: 300 }}
                    // onSearch={handleSearch}
                    className={styles.selects}
                    notFoundContent={null}
                    filterOption={(input, option) =>
                      option.children
                        .toLowerCase()
                        .indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {addDeptList.map(item => (
                      <Option value={`${item.id}`} key={item.deptFullName}>
                        {item.deptFullName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  label="审核节点"
                  name="trialId"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    showSearch
                    defaultActiveFirstOption={false}
                    placeholder="选择审核节点"
                    filterOption={false}
                    style={{ width: 300 }}
                    // onSearch={handleSearch}
                    className={styles.selects}
                    notFoundContent={null}
                    filterOption={(input, option) =>
                      option.children
                        .toLowerCase()
                        .indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {examineList.map(item => (
                      <Option value={`${item.code}`} key={item.code}>
                        {item.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </>
            )}

            <Form.Item style={{ marginBottom: 0 }}>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
};
