import request from '@/utils/request';
import { BASE_URl } from "../../../utils/constant";

//获取收入列表list
export function getStandUserListPage(parmas) {
  return request(`${BASE_URl}/projectList/getStandUserListPage`, {
    method: 'post',
    data: parmas,
  });
}

//删除
export function delStandUser(parmas) {
  return request(`${BASE_URl}/projectList/delStandUser?id=${parmas}`, {
    method: 'post',
  });
}

//新增
export function addStandUse(parmas) {
  return request(`${BASE_URl}/projectList/addStandUser`, {
    method: 'post',
    data: parmas,
  });
}

//编辑
export function editStandUse(parmas) {
  return request(
    `${BASE_URl}/projectList/UpdateTrialUser?id=${parmas.id}&username=${parmas.username}`,
    {
      method: 'post',
    },
  );
}

//获取部门下拉框数据
export function getDeptListInfo() {
  return request(`${BASE_URl}/dept/SWCARESdeptAll`, {
    method: 'POST',
  });
}

//下拉框信息
export function getTrialDic() {
  return request(`${BASE_URl}/projectList/getTrialDic`, {
    method: 'post',
  });
}

//获取姓名下拉框数据
export function getNameListInfo(params) {
  return request(`${BASE_URl}/user/selectSysUserPageData`, {
    method: 'POST',
    data: params,
  });
}
