import { <PERSON><PERSON>, Spin, Space } from 'antd';
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { encryption, randomLenNum } from '@/utils/utils';
import LoginFrom from './components/Login';
import styles from './style.less';
import { useLocation } from 'umi';
import { queryCurrent } from '../../../services/user';
import { BASE_URl } from "../../../utils/constant";
const { Tab, UserName, Password, ImgCaptcha, Submit } = LoginFrom;

const LoginMessage = ({ content }) => (
  <Alert
    style={{
      marginBottom: 24,
    }}
    message={content}
    type="error"
    showIcon
  />
);

const Login = props => {
  const location = useLocation();
  const { userLogin = {}, submitting } = props;
  const { status, type: loginType, key } = userLogin;
  const [type, setType] = useState('account');

  const [codeurl, setCodeurl] = useState(null);
  const [randomStr, setRandomStr] = useState(randomLenNum(4, true));

  useEffect(() => {
    if (location.query.token) {
      queryCurrents(location.query.token);
    }
  });

  const queryCurrents = async parmas => {
    const { data, code } = await queryCurrent(parmas);
    if (code === 200) {
      if (data) {
        console.log(data.sysUser.userId, 'userId');
        const userInfo = {
          access_token: location.query.token,
          userId: data.sysUser.userId,
        };
        localStorage.clear();
        localStorage.setItem('userInfo', JSON.stringify(userInfo));
        localStorage.setItem('updateTime', new Date().getTime());
        window.location.replace('/workbench');
      }
    }
  };

  const handleSubmit = values => {
    const { dispatch } = props;
    const userInfo = {
      ...values,
      randomStr,
      grant_type: 'password',
      scope: 'server',
    };
    const user = encryption({
      data: userInfo,
      key,
      param: ['password'],
    });
    dispatch({
      type: 'login/login',
      payload: { ...user },
      callback: resp => {
        refreshCode();
      },
    });
  };

  const refreshCode = e => {
    if (e) {
      e.preventDefault(); // 修复 Android 上点击穿透
      e.stopPropagation();
    }
    const code = randomLenNum(4, true);
    setRandomStr(code);
    setCodeurl(BASE_URl + `/code/${code}`);
  };

  useEffect(() => {
    refreshCode();
  }, []);

  return (
    <div className={styles.main}>
      <LoginFrom activeKey={type} onTabChange={setType} onSubmit={handleSubmit}>
        <Tab key="account" tab="账户密码登录">
          {status === 'error' && loginType === 'account' && !submitting && (
            <LoginMessage content="账户或密码错误（admin/ant.design）" />
          )}

          <UserName
            name="username"
            placeholder="用户名"
            rules={[
              {
                required: true,
                message: '请输入用户名!',
              },
            ]}
          />
          <Password
            name="password"
            placeholder="密码"
            rules={[
              {
                required: true,
                message: '请输入密码！',
              },
            ]}
          />
          <ImgCaptcha
            name="code"
            placeholder="验证码"
            codeurl={codeurl}
            refreshcode={refreshCode}
            getCaptchaButtonText=""
            getCaptchaSecondText="秒"
            rules={[
              {
                required: true,
                message: '请输入验证码！',
              },
            ]}
          />
        </Tab>
        {/* <Tab key="mobile" tab="手机号登录">
          {status === 'error' && loginType === 'mobile' && !submitting && (
            <LoginMessage content="验证码错误" />
          )}
          <Mobile
            name="mobile"
            placeholder="手机号"
            rules={[
              {
                required: true,
                message: '请输入手机号！',
              },
              {
                pattern: /^1\d{10}$/,
                message: '手机号格式错误！',
              },
            ]}
          />
          <Captcha
            name="captcha"
            placeholder="验证码"
            countDown={120}
            getCaptchaButtonText=""
            getCaptchaSecondText="秒"
            rules={[
              {
                required: true,
                message: '请输入验证码！',
              },
            ]}
          />
        </Tab> */}
        {/* <div>
          <Checkbox checked={autoLogin} onChange={e => setAutoLogin(e.target.checked)}>
            自动登录
          </Checkbox>
          <a
            style={{
              float: 'right',
            }}
          >
            忘记密码
          </a>
        </div> */}
        <Submit loading={submitting}>登录</Submit>
        {/* <div className={styles.other}>
          其他登录方式
          <AlipayCircleOutlined className={styles.icon} />
          <TaobaoCircleOutlined className={styles.icon} />
          <WeiboCircleOutlined className={styles.icon} />
          <Link className={styles.register} to="/user/register">
            注册账户
          </Link>
        </div> */}
      </LoginFrom>
    </div>
  );
};

export default connect(({ login, loading }) => ({
  userLogin: login,
  submitting: loading.effects['login/login'],
}))(Login);
