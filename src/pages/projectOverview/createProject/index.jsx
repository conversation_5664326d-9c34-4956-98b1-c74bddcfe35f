import React, { useState, useEffect } from 'react';
import styles from './index.less';
import {
  InfoCircleOutlined,
  QuestionCircleOutlined,
  CloseOutlined,
  ClockCircleOutlined,
  PlusOutlined,
  UpCircleOutlined,
  DownCircleOutlined,
} from '@ant-design/icons';
import edit_icon from '@/assets/edit_icon.svg';
import delete_icon from '@/assets/delete_icon.svg';
import hollow_icon from '@/assets/hollow_icon.svg';
import moment from 'moment';
import {
  Button,
  Modal,
  Anchor,
  Table,
  Collapse,
  Checkbox,
  Form,
  Tooltip,
  Input,
  Select,
  Popconfirm,
  Radio,
  DatePicker,
  InputNumber,
  message,
} from 'antd';
import {
  gitItemInfo,
  getInComeType,
  getDeptListInfo,
  getNameListInfo,
  getSearchListInfo,
  inProjectStand,
  getItemInfoUp,
} from './service';
import { history } from 'umi';

import classNames from 'classnames';
import { htmlToPDF } from '../../../utils/htmlToPDF';
// import html2pdf from 'html2pdf.js/src/index.js';

const { RangePicker } = DatePicker;
const { TextArea, Search } = Input;
const { Link } = Anchor;
const { Panel } = Collapse;
const { confirm } = Modal;
const { Option } = Select;
const dateFormat = 'YYYY-MM-DD';

export default props => {
  const { location } = props;
  const [disabled, setDisabled] = useState(false);
  const milestoneInfo = [
    {
      itemStageName: '达成合作',
      itemStageType: 'demand_survey',
      info: '确定意向客户，达成合作意向',
    },
    {
      itemStageName: '合同确立',
      itemStageType: 'business_negotiation',
      info: '确定合同条款，启动签署流程',
    },
    {
      itemStageName: '合同签订',
      itemStageType: 'agreement_signed',
      info: '完成合同签订',
    },
    {
      itemStageName: '项目实施',
      itemStageType: 'project_implemen',
      info: '完成项目实施',
    },
    {
      itemStageName: '项目验收',
      itemStageType: 'project_acceptance',
      info: '完成项目验收（根据验收次数填写）',
    },
    {
      itemStageName: '项目开票',
      itemStageType: 'project_invoice',
      info: '完成项目开票（根据开票次数填写）',
    },
    {
      itemStageName: '项目回款',
      itemStageType: 'invoice_refund',
      info: '完成项目回款（根据回款次数填写）',
    },
    {
      itemStageName: '结项备案',
      itemStageType: 'project_node',
      info: '完成项目收尾及结项备案',
    },
  ];
  const columns = [
    {
      title: '名称',
      fixed: 'left',
      align: 'left',
      dataIndex: 'name',
      key: '1',
      ellipsis: {
        showTitle: false,
      },
      render: name => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    {
      title: '部门',
      dataIndex: 'dept',
      fixed: 'left',
      align: 'left',
      key: '2',
      ellipsis: {
        showTitle: false,
      },
      render: name => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    {
      title: '角色',
      dataIndex: 'character',
      fixed: 'left',
      align: 'left',
      key: '3',
      ellipsis: {
        showTitle: false,
      },
      render: name => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    ,
    {
      title: '职责',
      dataIndex: 'dutie',
      fixed: 'left',
      align: 'left',
      key: '4',
      ellipsis: {
        showTitle: false,
      },
      render: name => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    {
      title: '进入项目组时间',
      dataIndex: 'startTime',
      key: 'startTime',
      fixed: 'left',
      align: 'left',
      render: value => (
        <Tooltip placement="topLeft" title={value}>
          {value && moment(value).format('YYYY-MM-DD')}
        </Tooltip>
      ),
    },
    {
      title: '出项目组时间',
      dataIndex: 'endTime',
      key: 'endTime',
      fixed: 'left',
      align: 'left',
      render: value => (
        <Tooltip placement="topLeft" title={value}>
          {(value && moment(value).format('YYYY-MM-DD')) || ''}
        </Tooltip>
      ),
    },

    {
      title: '操作',
      render: (item, record, index) =>
        type !== 'update' &&
        type !== 'updateDraft' &&
        type !== 'look' &&
        item && (
          <div>
            <a
              onClick={e =>
                showMoald({
                  title: '编辑团队成员',
                  item: item,
                  itemIndex: index,
                  e,
                })
              }
            >
              编辑
            </a>
            <Popconfirm
              title="是否删除？"
              okText="是"
              cancelText="否"
              onConfirm={() => deleteTeamItem(index)}
            >
              <a className={styles.delete}>删除</a>
            </Popconfirm>
          </div>
        ),
    },
  ];

  //协作部门信息列表
  const [cooperationDeptList, setCooperationDeptList] = useState([
    {
      departmentCooperation: '', //协作部门
      cooperationManager: '', //协作经理
      cooperationManagerDutie: '', //协作项目经理职责
      cooperationDutie: '', //协作部门职责
    },
  ]);
  //项目风险
  const [riskList, setRiskList] = useState([]);
  //计划收入列
  const [plannedRevenueInputList, setPlannedRevenueInputList] = useState([
    {
      year: '',
      money: 0,
      item: [{ planTime: '', planIncome: '' }],
    },
  ]);
  const [itemPlanIncome, setItemPlanIncome] = useState(0);

  const [type, SetType] = useState('');

  //初始化数据
  useEffect(() => {
    const itemID = location.query.id;
    const type = location.query.type;
    SetType(type);
    if (itemID) {
      gitItemInfos({ id: itemID, type });
    } else {
      form.resetFields();
      setCooperationDeptInputList([]);
      setCooperationDeptList([]);
      setRiskList([]);
      setIsCross(false);
      setIfStart(false);
      setPlannedRevenueInputList([
        {
          year: '',
          money: 0,
          item: [{ planTime: '', planIncome: '' }],
        },
      ]);
      setDeptInputList([
        {
          year: '',
          money: 0,
          item: [{ type: '', money: 0, proof: '' }],
        },
      ]);
      setDataSource([]);
    }
  }, [location]);

  const [projectName, setProjectName] = useState('立项部门');
  const nameChange = value => {
    setProjectName(value);
  };

  //获取项目
  const gitItemInfos = async value => {
    const { code, data } = await gitItemInfo(value);
    if (code === 200) {
      const formData = { ...data };
      if (location.query.type === 'update') {
        const resps = await getItemInfoUp(location.query.itemNum);
        if (resps.code === 200) {
          if (resps.data) {
            if (resps.data.length > 0) {
              formData.changeReason = resps.data[0].change_reason;
              formData.changeContent = resps.data[0].change_content;
              formData.changeInfluence = resps.data[0].change_influence;
            }
          }
        }
      }

      formData.name = data.name;
      setProjectName(data.dept);
      setItemPlanIncome(data.itemPlanIncome);
      // formData.managerFull = `${data.manager}@${data.managerIdCard}`;
      formData.managerFull = data.manager;
      formData.deptFull = data.dept;
      if (data.changeReason) {
        formData.changeReason = data.changeReason;
      }
      if (data.changeContent) {
        formData.changeContent = data.changeContent;
      }
      if (data.changeInfluence) {
        formData.changeInfluence = data.changeInfluence;
      }

      if (data.startTime && data.endTime) {
        formData.planTimes = [moment(data.startTime), moment(data.endTime)];
      }
      if (data.itemRisk && data.itemRisk.length > 0) {
        setRiskList([...data.itemRisk]);
      }
      if (data.type) {
        changeManageType(data.type);
      }
      setDataSource(data.itemMember || []);
      setIfStart(data.ifStart === 'Y');
      setIsCross(data.isDeptCooperation === 'Y');
      setPlanData(data.itemSchedule);
      if (data.cooperation) {
        setCooperationDeptList([...data.cooperation]);
      }
      let cooperationlist = {};
      if (data.cooperation) {
        data.cooperation.forEach((item, index) => {
          const keysList = Object.keys(item);
          keysList.forEach(key => {
            cooperationlist[`${key}${index}`] = item[key];
          });
        });
      }
      if (data.itemPlan && data.itemPlan.length > 0) {
        setPlannedRevenueInputList([
          {
            year: '',
            money: 0,
            item: data.itemPlan,
          },
        ]);
      }

      if (!data.itemCostDept && data.cooperation) {
        let newcooperationList = [];
        data.cooperation.forEach(item => {
          if (item.deptId) {
            let newObj = {
              itemId: item.itemId,
              itemDept: item.departmentCooperation,
              deptId: item.deptId,
              planTime: null,
              planCost: null,
              name: item.departmentCooperation,
              itemCostDept: [
                {
                  deptId: null,
                  itemCostDept: null,
                  itemCostHis: null,
                  itemDept: null,
                  itemId: null,
                  planCost: '0.00',
                  planTime: null,
                },
              ],
              itemCostHis: [
                {
                  deptId: item.itemId,
                  itemDept: item.departmentCooperation,
                  itemId: item.itemId,
                  itemNum: null,
                  planContent: null,
                  planContentSubject: null,
                  planCost: '0.00',
                  planTime: null,
                  proof: '',
                },
              ],
              contentList: [
                {
                  deptId: item.itemId,
                  itemDept: item.departmentCooperation,
                  itemId: item.itemId,
                  itemNum: null,
                  planContent: null,
                  planContentSubject: null,
                  planCost: '0.00',
                  planTime: null,
                  proof: '',
                },
              ],
              content: [
                {
                  dept: item.departmentCooperation,
                  deptId: item.itemId,
                  item: [
                    {
                      money: '0.00',
                      planContent: null,
                      planContentSubject: null,
                      planCost: '0.00',
                      proof: '',
                      type: null,
                    },
                  ],
                  money: '0.00',
                  year: null,
                },
              ],
            };
            newcooperationList.push(newObj);
          }
        });
        setCooperationDeptInputList([...newcooperationList]);
      }

      if (data.itemCostDept && data.itemCostDept.length > 0) {
        if (data.itemCostDept) {
          const yearList = [];
          let contentList = [];
          const cooperationList = [];
          data.itemCostDept.forEach(item => {
            if (item.deptId === data.deptId) {
              //立项部门
              // contentList = item.itemCostHis;
              contentList = contentList.concat(item.itemCostHis);
              item.itemCostDept.forEach(value => {
                yearList.push({
                  year: moment(value.planTime).format('YYYY'),
                  money: value.planCost,
                  item: [],
                });
              });
            } else {
              //协作部门
              cooperationList.push(item);
            }
          });
          contentList.forEach(item => {
            yearList.forEach(yearItem => {
              if (moment(item.planTime).format('YYYY') === yearItem.year) {
                yearItem.item.push({
                  type: item.planContent,
                  planContent: item.planContent,
                  planContentSubject: item.planContentSubject,
                  money: item.planCost,
                  planCost: item.planCost,
                  proof: item.proof,
                });
              }
            });
          });
          if (yearList.length > 0) {
            // 项目成本预算去重
            const map = new Map();
            const newYearList = yearList.filter(
              v => !map.has(v.year) && map.set(v.year, 1),
            );
            setDeptInputList(newYearList);
          }
          if (cooperationList.length > 0) {
            cooperationList.forEach(deptItems => {
              deptItems.content = [];
              deptItems.name = deptItems.itemDept;
              deptItems.contentList = deptItems.itemCostHis;
              deptItems.itemCostDept.forEach(value => {
                deptItems.content.push({
                  year: moment(value.planTime).format('YYYY'),
                  money: value.planCost,
                  deptId: deptItems.deptId,
                  dept: deptItems.itemDept,
                  item: [],
                });
              });
            });
            cooperationList.forEach(deptItems => {
              deptItems.contentList.forEach(item => {
                deptItems.content.forEach(yearItem => {
                  if (moment(item.planTime).format('YYYY') === yearItem.year) {
                    yearItem.item.push({
                      type: item.planContent,
                      planContent: item.planContent,
                      planContentSubject: item.planContentSubject,
                      money: item.planCost,
                      planCost: item.planCost,
                      proof: item.proof,
                    });
                  }
                });
              });
            });
            setCooperationDeptInputList([...cooperationList]);
          }

          if (data.cooperation && cooperationList) {
            if (data.cooperation.length - cooperationList.length > 0) {
              let deptNumber = data.cooperation.length - cooperationList.length;
              let newcooperationList = cooperationList;
              let allDept = [];
              let accDept = [];
              let midDept = [];
              let midcooperation = data.cooperation;
              cooperationList.forEach(item => {
                accDept.push(item.deptId);
              });
              data.cooperation.forEach(item => {
                allDept.push(item.deptId);
                midDept.push(item.deptId);
              });

              accDept.forEach((items, indexs) => {
                if (allDept.indexOf(items) >= 0) {
                  midDept.splice(allDept.indexOf(items), 1);
                  midcooperation.splice(allDept.indexOf(items), 1);
                }
              });
              midcooperation.forEach(item => {
                if (item.deptId) {
                  let newObj = {
                    itemId: item.itemId,
                    itemDept: item.departmentCooperation,
                    deptId: item.deptId,
                    planTime: null,
                    planCost: null,
                    name: item.departmentCooperation,
                    itemCostDept: [
                      {
                        deptId: null,
                        itemCostDept: null,
                        itemCostHis: null,
                        itemDept: null,
                        itemId: null,
                        planCost: '0.00',
                        planTime: null,
                      },
                    ],
                    itemCostHis: [
                      {
                        deptId: item.itemId,
                        itemDept: item.departmentCooperation,
                        itemId: item.itemId,
                        itemNum: null,
                        planContent: null,
                        planContentSubject: null,
                        planCost: '0.00',
                        planTime: null,
                        proof: '',
                      },
                    ],
                    contentList: [
                      {
                        deptId: item.itemId,
                        itemDept: item.departmentCooperation,
                        itemId: item.itemId,
                        itemNum: null,
                        planContent: null,
                        planContentSubject: null,
                        planCost: '0.00',
                        planTime: null,
                        proof: '',
                      },
                    ],
                    content: [
                      {
                        dept: item.departmentCooperation,
                        deptId: item.itemId,
                        item: [
                          {
                            money: '0.00',
                            planContent: null,
                            planContentSubject: null,
                            planCost: '0.00',
                            proof: '',
                            type: null,
                          },
                        ],
                        money: '0.00',
                        year: null,
                      },
                    ],
                  };
                  newcooperationList.push(newObj);
                  deptNumber = deptNumber - 1;
                } else {
                }
              });
              setCooperationDeptInputList([...newcooperationList]);
            }
          }
        }
      }
      form.setFieldsValue({ ...formData, ...cooperationlist });
    }
  };

  const [form] = Form.useForm();
  const [modalFrom] = Form.useForm();
  const [teamForm] = Form.useForm();
  const [visibleModal, setVisibleModal] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  //团队成员
  const [dataSource, setDataSource] = useState([]);
  const [itemIndex, setItemIndex] = useState(0);

  const [levelList, setLevelList] = useState([]);
  const [newLevelList, setNewLevelList] = useState([]);
  const [revenueTypeList, setRevenueTypeList] = useState([]);
  // 获取表列的所有种类
  const getTotalDorpList = async () => {
    const { data, code } = await getSearchListInfo();
    if (code === 200) {
      const levelList = new Set();
      const revenueTypeList = new Set();
      data.forEach(item => {
        if (item.type === 'item_level') {
          levelList.add(item);
        }
        if (item.type === 'in_come_type') {
          revenueTypeList.add(item);
        }
      });
      setLevelList([...levelList]);
      setRevenueTypeList([...revenueTypeList]);
    }
  };
  //新增项目风险
  const addRiskList = e => {
    setModalTitle('新增项目风险');
    setMoaldType('risk');
    modalFrom.setFieldsValue({ risk: '', solutions: '' });
    setVisibleModal(true);
    e.stopPropagation();
  };
  //编辑项目风险
  const editRiskItem = (item, index) => {
    setModalTitle('编辑项目风险');
    setMoaldType('risk');
    modalFrom.setFieldsValue({ ...item });
    setItemIndex(index);
    setVisibleModal(true);
  };
  //删除项目风险
  const deleteRiskItem = index => {
    let list = riskList;
    list.splice(index, 1);
    setRiskList([...list]);
  };
  //弹出框提交
  const onFinishMoadlFormData = value => {
    if (modalTitle === '编辑项目风险') {
      let list = riskList;
      list[itemIndex] = value;
      setRiskList([...list]);
    } else if (modalTitle === '新增项目风险') {
      setRiskList([...riskList, value]);
    } else if (modalTitle === '新增项目信息') {
      setRiskList([...riskList, value]);
    } else if (modalTitle === '新增团队成员') {
      const index = value.name.indexOf('-');
      const indexDept = value.name.indexOf('@');
      const indexDeptId = value.name.indexOf('+');
      const name = value.name.substring(0, index);
      const nameIdCard = value.name.substring(index + 1, indexDept);
      const dept = value.name.substring(indexDept + 1, indexDeptId);
      const deptId = value.name.substring(indexDeptId + 1);
      const list = dataSource;
      setDataSource([
        ...list,
        {
          name: name,
          dept: dept,
          deptId: deptId,
          nameIdCard: nameIdCard,
          character: value.character,
          dutie: value.dutie,
          startTime: moment(value.startTime).format('YYYY-MM-DD'),
          endTime:
            (value.endTime && moment(value.endTime).format('YYYY-MM-DD')) || '',
        },
      ]);
    } else if (modalTitle === '编辑团队成员') {
      const list = dataSource;
      list[itemIndex] = {
        ...list[itemIndex],
        character: value.character,
        dutie: value.dutie,
        startTime: moment(value.startTime).format('YYYY-MM-DD'),
        endTime:
          (value.endTime && moment(value.endTime).format('YYYY-MM-DD')) || '',
      };
      setDataSource([...list]);
    } else {
      const list = planData;
      list[itemIndex].planEndTime = moment(value.planEndTime).format(
        'YYYY-MM-DD',
      );
      list[itemIndex].facts = value.facts;
      setPlanData([...list]);
    }
    modalFrom.setFieldsValue({});
    setVisibleModal(false);
  };

  //弹出框类别
  const [moaldType, setMoaldType] = useState('');

  const [manageType, setManageType] = useState('manage');
  const changeManageType = value => {
    form.setFieldsValue({ ...form, level: '', inComeType: '' });
    if (value === 'no_manage') {
      setManageType('no_manage');
      setPlanData([
        {
          itemStageName: '合同确立',
          itemStageType: 'business_negotiation',
          planEndTime: '',
        },
        {
          itemStageName: '项目实施',
          itemStageType: 'project_implemen',
          planEndTime: '',
        },
        {
          itemStageName: '项目验收',
          itemStageType: 'project_acceptance',
          planEndTime: '',
        },
        {
          itemStageName: '结项备案',
          itemStageType: 'project_node',
          planEndTime: '',
        },
      ]);
      let list = [...levelList];
      list.splice(0, 3);
      setNewLevelList([...list]);
    } else if (value === 'product_category') {
      setManageType('product_category');
      setPlanData([
        {
          itemStageName: '合同确立',
          itemStageType: 'business_negotiation',
          planEndTime: '',
        },
        {
          itemStageName: '项目实施',
          itemStageType: 'project_implemen',
          planEndTime: '',
        },
        {
          itemStageName: '项目验收',
          itemStageType: 'project_acceptance',
          planEndTime: '',
        },
        {
          itemStageName: '结项备案',
          itemStageType: 'project_node',
          planEndTime: '',
        },
      ]);
      let list = [...levelList];
      list.splice(0, 3);
      setNewLevelList([...list]);
    } else {
      setManageType('manage');
      setPlanData([
        {
          itemStageName: '达成合作',
          itemStageType: 'demand_survey',
          planEndTime: '',
        },
        {
          itemStageName: '合同确立',
          itemStageType: 'business_negotiation',
          planEndTime: '',
        },
        {
          itemStageName: '合同签订',
          itemStageType: 'agreement_signed',
          planEndTime: '',
        },
        {
          itemStageName: '项目实施',
          itemStageType: 'project_implemen',
          planEndTime: '',
        },
        {
          itemStageName: '项目验收',
          itemStageType: 'project_acceptance',
          planEndTime: '',
        },
        {
          itemStageName: '项目开票',
          itemStageType: 'project_invoice',
          planEndTime: '',
        },
        {
          itemStageName: '项目回款',
          itemStageType: 'invoice_refund',
          planEndTime: '',
        },
        {
          itemStageName: '结项备案',
          itemStageType: 'project_node',
          planEndTime: '',
        },
      ]);
      let list = [...levelList];
      list.splice(3);
      setNewLevelList([...list]);
    }
  };

  const InComeTypeChange = value => {
    if (manageType === 'manage') {
      if (value === 'system_maintenance') {
        setPlanData([
          {
            itemStageName: '达成合作',
            itemStageType: 'demand_survey',
            planEndTime: '',
          },
          {
            itemStageName: '合同确立',
            itemStageType: 'business_negotiation',
            planEndTime: '',
          },
          {
            itemStageName: '合同签订',
            itemStageType: 'agreement_signed',
            planEndTime: '',
          },
          {
            itemStageName: '项目开票',
            itemStageType: 'project_invoice',
            planEndTime: '',
          },
          {
            itemStageName: '项目回款',
            itemStageType: 'invoice_refund',
            planEndTime: '',
          },
          {
            itemStageName: '结项备案',
            itemStageType: 'project_node',
            planEndTime: '',
          },
        ]);
      } else {
        setPlanData([
          {
            itemStageName: '达成合作',
            itemStageType: 'demand_survey',
            planEndTime: '',
          },
          {
            itemStageName: '合同确立',
            itemStageType: 'business_negotiation',
            planEndTime: '',
          },
          {
            itemStageName: '合同签订',
            itemStageType: 'agreement_signed',
            planEndTime: '',
          },
          {
            itemStageName: '项目实施',
            itemStageType: 'project_implemen',
            planEndTime: '',
          },
          {
            itemStageName: '项目验收',
            itemStageType: 'project_acceptance',
            planEndTime: '',
          },
          {
            itemStageName: '项目开票',
            itemStageType: 'project_invoice',
            planEndTime: '',
          },
          {
            itemStageName: '项目回款',
            itemStageType: 'invoice_refund',
            planEndTime: '',
          },
          {
            itemStageName: '结项备案',
            itemStageType: 'project_node',
            planEndTime: '',
          },
        ]);
      }
    }
  };
  //计划里程碑
  const [planData, setPlanData] = useState([]);

  const editMilesTone = (index, item) => {
    if (item && item.planEndTime) {
      modalFrom.setFieldsValue({
        planEndTime: moment(item.planEndTime || ''),
        facts: item.facts,
      });
    } else {
      modalFrom.resetFields();
    }
    setItemIndex(index);
    setModalTitle(`${planData[index].itemStageName}`);
    setMoaldType('milesTone');

    setVisibleModal(true);
  };

  //费用类分类
  const [inComeTypeList, setInComeTypeList] = useState([]);
  //部门列表
  const [deptList, setDeptList] = useState([]);
  //人员列表
  const [nameList, setNameList] = useState([]);
  //获取费用类分类
  const getInComeTypes = async () => {
    const { data, code } = await getInComeType();
    if (code === 200) {
      setInComeTypeList(data);
    }
  };
  //获取部门下拉
  const getDeptListInfos = async value => {
    const { code, data } = await getDeptListInfo(value);
    if (code === 200) {
      const addDeptList = new Set();
      data.forEach(item => {
        let repeat = false;
        addDeptList.forEach(value => {
          if (value.deptFullName === item.deptFullName) {
            repeat = true;
          }
        });
        if (!repeat) {
          item.deptFullName && addDeptList.add(item);
        }
      });
      setDeptList(addDeptList);
    }
  };

  let timeout;
  function fetch(value, callback) {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    function fake() {
      const getNameListInfos = async value => {
        const { code, data } = await getNameListInfo({
          page: 1,
          limit: 10,
          employeeName: value,
        });
        if (code === 200) {
          callback(data.records);
        }
      };
      getNameListInfos(value);
    }
    timeout = setTimeout(fake, 300);
  }
  const handleSearch = value => {
    if (value) {
      fetch(value, data => setNameList(data));
    }
  };
  //获取费用类分类
  useEffect(() => {
    getInComeTypes();
    getDeptListInfos();
    getTotalDorpList();
  }, []);

  //是否合同签署前实施
  const [ifStart, setIfStart] = useState(false);

  //是否跨部门协作
  const [isCross, setIsCross] = useState(false);

  const ifStartCheck = value => {
    if (type !== 'update' && type !== 'updateDraft') {
      setIfStart(value);
    }
  };
  //是否跨部门协作
  const deptCheck = e => {
    setIsCross(e.target.checked);
    if (e.target.checked) {
      setCooperationDeptList([
        {
          departmentCooperation: '', //协作部门
          cooperationManager: '', //协作经理
          cooperationManagerDutie: '', //协作项目经理职责
          cooperationDutie: '', //协作部门职责
        },
      ]);
      setCooperationDeptInputList([
        {
          name: '协作部门',
          content: [
            {
              year: '',
              money: 0,
              item: [{ type: '', money: 0, proof: '' }],
            },
          ],
        },
      ]);
    }
  };

  //新增跨部门
  const addCooperationDept = e => {
    setCooperationDeptList([
      ...cooperationDeptList,
      {
        departmentCooperation: '', //协作部门
        cooperationManager: '', //协作经理
        cooperationManagerDutie: '', //协作项目经理职责
        cooperationDutie: '', //协作部门职责
      },
    ]);
    setCooperationDeptInputList([
      ...cooperationDeptInputList,
      {
        name: '协作部门',
        content: [
          {
            year: '',
            money: 0,
            item: [{ type: '', money: 0, proof: '' }],
          },
        ],
      },
    ]);
    e.stopPropagation();
  };
  //删除协作部门
  const deleteCooperationDept = index => {
    const list = cooperationDeptList;
    list.splice(index, 1);
    setCooperationDeptList([...list]);
    const newCooperationDeptInputList = cooperationDeptInputList;
    newCooperationDeptInputList.splice(index, 1);
    setCooperationDeptInputList([...newCooperationDeptInputList]);
  };

  //协作部门
  const cooperationDeptListChange = porps => {
    const { value, index, key } = porps;
    const list = cooperationDeptList;
    if (key === 'departmentCooperation') {
      const idIndex = value.indexOf('@');
      const deptId = value.substring(idIndex + 1);
      const departmentCooperation = value.substring(0, idIndex);
      list[index].deptId = deptId;
      list[index].departmentCooperation = departmentCooperation;
      const assistList = cooperationDeptInputList;
      assistList[index].name = departmentCooperation;
      assistList[index].id = deptId;
      setCooperationDeptInputList([...assistList]);
    }
    if (key === 'cooperationManager') {
      const idIndex = value.indexOf('@');
      const nameIdCard = value.substring(idIndex + 1);
      const cooperationManager = value.substring(0, idIndex);
      list[index].nameIdCard = nameIdCard;
      list[index].cooperationManager = cooperationManager;
    }
    if (key !== 'cooperationManager' && key !== 'departmentCooperation') {
      list[index][key] = value;
    }
    setCooperationDeptList([...list]);
  };

  const deleteTeamItem = index => {
    const list = dataSource;
    list.splice(index, 1);
    setDataSource([...list]);
  };

  const showMoald = data => {
    const { title, item, itemIndex, e } = data;
    setVisibleModal(true);
    if (title === '编辑团队成员' || title === '新增团队成员') {
      setMoaldType('team');
    }
    if (title === '编辑团队成员') {
      setItemIndex(itemIndex);
      const name = `${item.name}-${item.dept}`;
      const startTime = (item.startTime && moment(item.startTime)) || '';
      const endTime = (item.endTime && moment(item.endTime)) || '';
      modalFrom.setFieldsValue({ ...item, name, endTime, startTime });
    }
    setModalTitle(title);
    setVisibleModal(true);
    e.stopPropagation();
  };

  const handleOk = () => {
    setVisibleModal(false);
    modalFrom.setFieldsValue({});
  };

  const handleCancel = () => {
    setVisibleModal(false);
    modalFrom.setFieldsValue({
      name: '',
      character: '',
      dutie: '',
    });
    modalFrom.setFieldsValue({});
  };

  const plannedRevenueInputListMoneyChange = porps => {
    const { value, index, indexs } = porps;
    const list = plannedRevenueInputList;
    list[index].item[indexs].planIncome = Number(value);
    let money = 0;
    list[index].item.forEach(value => {
      money += Number(value.planIncome || 0).toFixed(2);
    });
    list[index].money = Number(money).toFixed(2);
    setPlannedRevenueInputList([...list]);
  };
  //协助部门金额变化
  const cooperationDeptInputListMoneyChange = porps => {
    const { value, index, indexs, contentIndex } = porps;
    const list = cooperationDeptInputList;
    list[index].content[contentIndex].item[indexs].money = value;
    let money = 0;
    list[index].content[contentIndex].item.forEach(value => {
      money += Number(Number(value.money).toFixed(2));
    });
    list[index].content[contentIndex].money = Number(money).toFixed(2);
    setCooperationDeptInputList([...list]);
  };
  //协助部门
  const cooperationDeptInputListChange = porps => {
    const { value, index, indexs, contentIndex } = porps;
    const list = cooperationDeptInputList;
    list[index].content[contentIndex].item[indexs].proof = value;
    setCooperationDeptInputList([...list]);
  };

  const deptInputListChange = porps => {
    const { value, index, indexs } = porps;
    const list = deptInputList;
    list[index].item[indexs].proof = value;
    setDeptInputList([...list]);
  };
  const datePickerChange = value => {
    const { date, deteString, index, indexs, key, contentIndex } = value;
    if (key === 'plannedRevenueInputList') {
      const list = plannedRevenueInputList;
      if (indexs === -1) {
        list[index].year = deteString;
      } else {
        const list = plannedRevenueInputList;
        list[index].item[indexs].planTime = deteString;
      }
      setPlannedRevenueInputList([...list]);
    }
    if (key === 'deptInputList') {
      const list = deptInputList;
      list[index].year = deteString;
      setDeptInputList([...list]);
    }
    if (key === 'cooperationDeptInputList') {
      const list = cooperationDeptInputList;
      if (indexs === -1) {
        list[index].content[contentIndex].year = deteString;
      }
      setCooperationDeptInputList([...list]);
    }
  };

  const deptInputListMoneyChange = porps => {
    const { value, index, indexs } = porps;
    const list = deptInputList;
    list[index].item[indexs].planCost = Number(value);
    let money = 0;
    list[index].item.forEach(value => {
      money += Number(Number(value.planCost || 0).toFixed(2));
    });
    list[index].money = money;
    setDeptInputList([...list]);
  };
  /*
   **部门预算
   */
  const [deptInputList, setDeptInputList] = useState([
    {
      year: '',
      money: 0,
      item: [{ type: '', money: 0, proof: '' }],
    },
  ]);
  //协作部门预算
  const [cooperationDeptInputList, setCooperationDeptInputList] = useState([
    {
      name: '协作部门',
      id: '',
      content: [
        {
          year: '',
          money: 0,
          item: [{ type: '', money: 0, proof: '' }],
        },
      ],
    },
  ]);
  const addCooperationInputItem = value => {
    const { dept, contentIndex, indexs } = value;
    if (indexs === -1) {
      const list = cooperationDeptInputList;
      list[dept].content.push({
        year: '',
        money: 0,
        item: [{ type: '', money: 0, proof: '' }],
      });
      setCooperationDeptInputList([...list]);
    } else {
      const list = cooperationDeptInputList;
      list[dept].content[contentIndex].item.push({
        type: '',
        money: 0,
        proof: '',
      });
      setCooperationDeptInputList([...list]);
    }
  };

  const deleteCooperationInputItem = value => {
    const { dept, contentIndex, indexs } = value;
    if (indexs === -1) {
      const list = cooperationDeptInputList;
      list[dept].content.splice(contentIndex, 1);
      setCooperationDeptInputList([...list]);
    } else {
      const list = cooperationDeptInputList;
      const totleMoney = list[dept].content[contentIndex].item[indexs].money;
      list[dept].content[contentIndex].item.splice(indexs, 1);
      list[dept].content[contentIndex].money -= totleMoney;
      setCooperationDeptInputList([...list]);
    }
  };

  //添加列表
  const addInputItem = ({ dept, type, index }) => {
    if (dept === '计划收入') {
      if (type === 'year') {
        setPlannedRevenueInputList([
          ...plannedRevenueInputList,
          {
            year: moment(new Date(), 'YYYY'),
            money: 0,
            item: [{ planTime: '', planIncome: '' }],
          },
        ]);
      }
      if (type === 'inComeType') {
        let list = plannedRevenueInputList;
        list[index].item.push({ planTime: '', planIncome: '' });
        setPlannedRevenueInputList([...list]);
      }
    }
    if (dept === '立项部门') {
      //增加年度
      if (type === 'year') {
        const year = {
          year: '',
          money: 0,
          item: [{ type: '', money: 0, proof: '' }],
        };
        setDeptInputList([...deptInputList, year]);
      }
      //增加费用
      if (type === 'inComeType') {
        let list = deptInputList;
        list[index].item.push({ type: '', money: 0, proof: '' });
        setDeptInputList([...list]);
      }
    }
    if (dept === '协作部门') {
      //增加年度
      if (type === 'year') {
        const year = {
          year: moment(new Date(), 'YYYY'),
          money: 0,
          item: [{ type: '', money: 0, proof: '' }],
        };
        setCooperationDeptInputList([...cooperationDeptInputList, year]);
      }
      //增加费用
      if (type === 'inComeType') {
        let list = cooperationDeptInputList;
        list[index].item.push({ type: '', money: 0, proof: '' });
        setCooperationDeptInputList([...list]);
      }
    }
  };

  //费用类别改变
  const itemCostTypeChange = porpos => {
    const { value, index, indexs, type, contentIndex } = porpos;
    if (type === 'deptInputList') {
      const list = deptInputList;
      const indexOf = value.indexOf('@');
      list[index].item[indexs].planContent = value.substring(0, indexOf);
      list[index].item[indexs].planContentSubject = value.substring(
        indexOf + 1,
      );
      setDeptInputList([...list]);
    } else if (type === 'cooperationDeptInputList') {
      const list = cooperationDeptInputList;
      const indexOf = value.indexOf('@');
      list[index].content[contentIndex].item[indexs].itemDept =
        list[index].name;
      list[index].content[contentIndex].item[indexs].deptId = list[index].id;
      list[index].content[contentIndex].item[
        indexs
      ].planContent = value.substring(0, indexOf);
      list[index].content[contentIndex].item[
        indexs
      ].planContentSubject = value.substring(indexOf + 1);
      setCooperationDeptInputList([...list]);
    }
  };

  const deleteItem = value => {
    const { type, index, indexs } = value;
    if (type === '计划收入') {
      if (indexs === -1) {
        let list = plannedRevenueInputList;
        list.splice(index, 1);
        setPlannedRevenueInputList([...list]);
      } else {
        let list = plannedRevenueInputList;
        list[index].item.splice(indexs, 1);
        setPlannedRevenueInputList([...list]);
      }
    }
    if (type === '立项部门') {
      if (indexs === -1) {
        let list = deptInputList;
        list.splice(index, 1);
        setDeptInputList([...list]);
      } else {
        let list = deptInputList;
        let totleMoney = Number(list[index].item[indexs].planCost);
        list[index].item.splice(indexs, 1);
        list[index].money -= totleMoney;
        setDeptInputList([...list]);
      }
    }
  };

  useEffect(() => {
    let money = 0;
    plannedRevenueInputList.forEach(item => {
      if (item.item.length > 0) {
        item.item.forEach(value => {
          money += Number(value.planIncome);
        });
      }
    });
    setItemPlanIncome(money);
  }, [plannedRevenueInputList]);

  const [itemCostBudgeting, setItemCostBudgeting] = useState(0); //预算总计

  useEffect(() => {
    let money = 0;
    deptInputList.forEach(item => {
      money += Number(Number(item.money).toFixed(2));
    });
    cooperationDeptInputList.forEach(item => {
      item.content.forEach(value => {
        money += Number(Number(value.money).toFixed(2));
      });
    });
    setItemCostBudgeting(money);
  }, [deptInputList, cooperationDeptInputList]);

  //计算利润率
  useEffect(() => {
    const num =
      ((Number(itemPlanIncome) - Number(itemCostBudgeting)) /
        Number(itemPlanIncome)) *
      100;
    form.setFieldValue('profitRate', num ? num.toFixed(2) : null);
  }, [itemCostBudgeting, itemPlanIncome]);
  const onFinishFormData = e => {
    confirm({
      title: '是否提交审核?',
      // icon: <ExclamationCircleOutlined />,
      okText: '确定',
      cancelText: '取消',
      onOk() {
        saveData(1);
      },
      onCancel() {},
    });
  };
  const saveSubmit = () => {
    confirm({
      title: '是否保存?',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        saveData(0);
      },
      onCancel() {},
    });
  };
  const saveData = type => {
    const formData = form.getFieldValue();
    let needTime = false;
    let needType = false;
    const { managerFull, deptFull } = formData;
    if (formData.planTimes) {
      formData.startTime = moment(formData.planTimes[0]).format('YYYY-MM-DD');
      formData.endTime = moment(formData.planTimes[1]).format('YYYY-MM-DD');
    }
    if (managerFull && managerFull.indexOf('@') !== -1) {
      const idIndex = managerFull.indexOf('@');
      formData.manager = managerFull.substring(0, idIndex);
      formData.managerIdCard = managerFull.substring(idIndex + 1);
    }
    if (deptFull && deptFull.indexOf('@') !== -1) {
      const idIndex = deptFull.indexOf('@');
      formData.dept = deptFull.substring(0, idIndex);
      formData.deptId = deptFull.substring(idIndex + 1);
    }
    if (itemPlanIncome !== 0) {
      const itemPlan = [];
      plannedRevenueInputList.forEach(item => {
        item.item.forEach(value => {
          if (value.planIncome && !value.planTime) {
            needTime = true;
          }
          itemPlan.push(value);
        });
      });
      formData.itemPlan = itemPlan;
      formData.itemPlanIncome = itemPlanIncome;
    }
    const itemCost = [];
    if (deptInputList[0] && deptInputList[0].money !== 0) {
      const list = deptInputList;
      list.forEach(items => {
        items.item.forEach(value => {
          if (value.planContent && !items.year) {
            needTime = true;
          }
          if (value.planCost && !value.planContent) {
            needType = true;
          }
          if (value.planContent && items.year) {
            value.deptId = formData.deptId;
            value.money = items.money;
            value.itemDept = formData.dept;
            value.planTime = `${items.year}-01-01`;
            itemCost.push(value);
          }
        });
      });
    }
    if (deptInputList[0] && itemCostBudgeting - deptInputList[0].money !== 0) {
      const list = cooperationDeptInputList;
      list.forEach(item => {
        item.content.forEach(contentItem => {
          contentItem.item.forEach(lastItem => {
            if (contentItem.money && !contentItem.year) {
              needTime = true;
            }
            if (lastItem.planCost && !lastItem.planContent) {
              needType = true;
            }
            if (contentItem.year) {
              lastItem.deptId = item.id || item.deptId;
              lastItem.itemDept = item.name || item.itemDept;
              lastItem.planTime = `${contentItem.year}-01-01`;
              lastItem.planCost = lastItem.money;
              itemCost.push(lastItem);
            }
          });
        });
      });
    }
    formData.ifStart = ifStart ? 'Y' : 'N';
    formData.isDeptCooperation = isCross ? 'Y' : 'N';
    formData.cooperation = cooperationDeptList;
    formData.itemMember = dataSource;
    formData.itemSchedule = planData;
    formData.itemCost = [...itemCost];

    if (riskList && riskList.length > 0) {
      formData.itemRisk = [...riskList];
    }
    if (!formData.itemNum) {
      formData.itemNum = '';
    }

    if (type == 1) {
      let planDataIsEmpty = false;
      planData.forEach(item => {
        if (!item.planEndTime) {
          planDataIsEmpty = true;
        }
      });
      if (planDataIsEmpty) {
        warning('计划里程碑未填写完');
      } else if (
        ifStart &&
        riskList.length < 1 &&
        !(location.query.type === 'update')
      ) {
        warning('勾选合同签署前开始实施,请填写项目风险');
      } else if (needTime) {
        warning('金额的时间必须填写');
      } else if (needType) {
        warning('费用类型必须填写');
      } else {
        if (
          location.query.type === 'update' ||
          location.query.type === 'updateDraft'
        ) {
          if (location.query.type === 'update') {
            formData.id = 0;
          }
          createProjectSave({ formData, type: type + 2 });
        } else {
          createProjectSave({ formData, type });
        }
      }
    } else {
      if (
        location.query.type === 'update' ||
        location.query.type === 'updateDraft'
      ) {
        if (location.query.type === 'update') {
          formData.id = 0;
        }
        createProjectSave({ formData, type: type + 2 });
      } else {
        createProjectSave({ formData, type });
      }
    }
  };

  const createProjectSave = async formData => {
    setDisabled(true);
    const { data, code } = await inProjectStand(formData);
    if (code === 200) {
      message.success({
        content: '成功!',
        key: 'createProjectSave',
        duration: 2,
      });
      history.replace('/workbench');
    } else {
      message.error({
        content: '失败!',
        key: 'createProjectSave',
        duration: 2,
      });
      setDisabled(false);
    }
  };
  const [yearMoneyList, setYearMoneyList] = useState([]);
  useEffect(() => {
    const planList = plannedRevenueInputList[0].item;
    if (planList.length > 0) {
      let tempArr = [];
      let afterData = [];
      for (let i = 0; i < planList.length; i++) {
        if (tempArr.indexOf(planList[i].planTime.substring(0, 4)) === -1) {
          afterData.push({
            year: planList[i].planTime.substring(0, 4),
            money: Number(planList[i].planIncome),
          });
          tempArr.push(planList[i].planTime.substring(0, 4));
        } else {
          for (let j = 0; j < afterData.length; j++) {
            if (afterData[j].year == planList[i].planTime.substring(0, 4)) {
              afterData[j].money += Number(planList[i].planIncome);
              break;
            }
          }
        }
      }
      setYearMoneyList(afterData);
    }
  }, [plannedRevenueInputList]);

  function warning(value) {
    Modal.warning({
      title: value,
    });
  }

  const [showAnchor, setShowAnchor] = useState(true);
  //项目分类提示信息切换
  const [manageTypeTooltip, setManageTypeTooltip] = useState('');
  useEffect(() => {
    if (manageType === 'manage') {
      setManageTypeTooltip(
        <div>
          1、Ⅰ-A级：预计利润在100万元以上项目（不含系统连接及运维服务类项目）
          <br />
          2、Ⅰ-B级：航信战略重点任务
          <br />
          3、Ⅰ-C级：系统连接及运维服务类项目及其他项目
          <br />
        </div>,
      );
    } else {
      setManageTypeTooltip(
        <div>
          1、Ⅱ-A级：年度一号文内容
          <br />
          2、Ⅱ-B级：年度一号文以外公司认为的重点任务
          <br />
          3、Ⅱ-C级：部门职责范围内分管领导认为的重点工作任务
          <br />
        </div>,
      );
    }
    setManageTypeTooltip;
  }, [manageType]);

  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');

  const handleDatePickerChange = (type, date) => {
    if (type === 'startTime') {
      setStartTime(date);
    } else {
      setEndTime(date);
    }
  };

  const [disabledLook, setDisabledLook] = useState(false);
  useEffect(() => {
    if (
      location.query.type === 'look' ||
      location.query.type === 'updateDraft' ||
      location.query.type === 'update'
    ) {
      setDisabledLook(true);
    } else {
      setDisabledLook(false);
    }
  }, [location.query.type]);
  return (
    <div id="scrollDIV">
      <div>
        <Anchor
          className={styles.anchor}
          getContainer={() => document.querySelector('#content')}
          targetOffset={200}
          affix={true}
        >
          <Link>
            {showAnchor ? (
              <UpCircleOutlined onClick={() => setShowAnchor(!showAnchor)} />
            ) : (
              <DownCircleOutlined onClick={() => setShowAnchor(!showAnchor)} />
            )}
          </Link>
          {showAnchor && (
            <>
              {location.query.type == 'update' && (
                <Link href="#27" title="项目变更" />
              )}
              <Link href="#22" title="项目基本信息" />
              <Link href="#23" title="协作部门信息" />
              <Link href="#24" title="项目成员" />
              <Link href="#25" title="计划里程碑" />
              <Link href="#26" title="项目风险" />
              <Link href="#32" title="项目计划收入(税后)" />
              <Link href="#33" title="项目成本预算" />
              <Link href="#44" title="项目利润" />
            </>
          )}
        </Anchor>
      </div>
      <Form
        className={styles.forms}
        // initialValues={fromData}
        colon={false}
        // layout="vertical"
        form={form}
        labelAlign="right"
        name="nest-messages"
        scrollToFirstError={true}
        onFinish={onFinishFormData}
        labelCol={{ span: 7 }}
        wrapperCol={{ span: 13 }}
      >
        <div id={'exportFormContent'}>
          {location.query.type == 'update' && (
            <Collapse
              expandIconPosition="right"
              defaultActiveKey={['1']}
              ghost={true}
            >
              <Panel
                header={<div className={styles.titles}>项目变更</div>}
                key="1"
                className={classNames(styles.card)}
                id="27"
              >
                <div>
                  <div className={styles.content}>
                    <Form.Item
                      label="变更原因"
                      name="changeReason"
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                    >
                      <TextArea
                        placeholder="请输入变更原因：如变更起因，由谁提出等"
                        autoSize={{ minRows: 2, maxRows: 6 }}
                      />
                    </Form.Item>
                    <Form.Item
                      label="变更内容"
                      name="changeContent"
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                    >
                      <TextArea
                        placeholder="请输入变更变更内容：如需求，设计，代码等"
                        autoSize={{ minRows: 2, maxRows: 6 }}
                      />
                    </Form.Item>
                    <Form.Item
                      label="变更影响"
                      name="changeInfluence"
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                    >
                      <TextArea
                        placeholder="请输入变更影响：如进度，收益，质量等"
                        autoSize={{ minRows: 2, maxRows: 6 }}
                      />
                    </Form.Item>
                    <Form.Item label="项目经理" name="managerFull">
                      <Select
                        showSearch
                        defaultActiveFirstOption={false}
                        placeholder="选择项目经理"
                        showArrow={false}
                        className={styles.selects}
                        filterOption={false}
                        onSearch={handleSearch}
                        notFoundContent={null}
                      >
                        {nameList.length > 0 &&
                          nameList.map((item, index) => (
                            <Option
                              value={`${item.employee_name}-${item.deptName}@${item.username}`}
                              key={index}
                            >
                              {item.employee_name}-{item.deptName}
                            </Option>
                          ))}
                      </Select>
                    </Form.Item>
                  </div>
                </div>
              </Panel>
            </Collapse>
          )}
          <Collapse
            defaultActiveKey={['1']}
            expandIconPosition="right"
            ghost={true}
          >
            <Panel
              header={<div className={styles.titles}>项目基本信息</div>}
              key="1"
              id="22"
              className={classNames(styles.card)}
            >
              <div>
                <div className={styles.content}>
                  {type === 'update' && (
                    <Form.Item label="项目编号" name="itemNum">
                      <Input disabled={disabledLook} />
                    </Form.Item>
                  )}
                  {type === 'update' && (
                    <Form.Item label="立项审批单编号" name="itemGroupNum">
                      <Input disabled={disabledLook} />
                    </Form.Item>
                  )}
                  <Form.Item
                    label="项目名称"
                    name="name"
                    rules={[
                      {
                        required: type !== 'update' && type !== 'updateDraft',
                      },
                    ]}
                  >
                    <Input
                      placeholder="请输入项目名称"
                      disabled={disabledLook}
                    />
                  </Form.Item>
                  <Form.Item label="项目客户" name="projectClient">
                    <Input
                      placeholder="请输入客户名称"
                      disabled={disabledLook}
                    />
                  </Form.Item>
                  <Form.Item
                    label="立项部门"
                    name="deptFull"
                    rules={[
                      {
                        required: type !== 'update' && type !== 'updateDraft',
                      },
                    ]}
                  >
                    <Select
                      showSearch
                      defaultActiveFirstOption={false}
                      showArrow={false}
                      placeholder="选择立项部门"
                      disabled={disabledLook}
                      filterOption={false}
                      onChange={value => {
                        nameChange(value);
                      }}
                      // onSearch={handleSearch}
                      className={styles.selects}
                      notFoundContent={null}
                      filterOption={(input, option) =>
                        option.children
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      {deptList.map(item => (
                        <Option
                          value={`${item.deptFullName}@${item.id}`}
                          key={item.id}
                        >
                          {item.deptFullName}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                  <Form.Item
                    label="项目经理"
                    name="managerFull"
                    rules={[
                      {
                        required: type !== 'update' && type !== 'updateDraft',
                      },
                    ]}
                  >
                    <Select
                      showSearch
                      defaultActiveFirstOption={false}
                      placeholder="选择项目经理"
                      disabled={disabledLook}
                      showArrow={false}
                      className={styles.selects}
                      filterOption={false}
                      onSearch={handleSearch}
                      notFoundContent={null}
                    >
                      {nameList.length > 0 &&
                        nameList.map((item, index) => (
                          <Option
                            value={`${item.employee_name}-${item.deptName}@${item.username}`}
                            key={index}
                          >
                            {item.employee_name}-{item.deptName}
                          </Option>
                        ))}
                    </Select>
                  </Form.Item>
                  <Form.Item
                    label="项目把握度"
                    name="itemGrasp"
                    rules={[
                      {
                        required: type !== 'update' && type !== 'updateDraft',
                      },
                    ]}
                  >
                    <Radio.Group
                      // onChange={changeRadio}
                      value={form.itemGrasp}
                      className={styles.grasp_Box}
                      disabled={disabledLook}
                    >
                      <Radio value={'high_grasp'}>
                        高<span className={styles.grasp_info}>(81%-100%)</span>
                      </Radio>
                      <Radio value={'higher_grasp'}>
                        较高
                        <span className={styles.grasp_info}>(51%-80%)</span>
                      </Radio>
                      <Radio value={'lower_grasp'}>
                        较低
                        <span className={styles.grasp_info}>(31%-50%)</span>
                      </Radio>
                      <Radio value={'lowz_grasp'}>
                        低<span className={styles.grasp_info}>(0%-30%)</span>
                      </Radio>
                    </Radio.Group>
                  </Form.Item>
                  <Form.Item
                    label="项目经理职责"
                    name="managerDutie"
                    rules={[
                      {
                        required: type !== 'update' && type !== 'updateDraft',
                      },
                    ]}
                  >
                    <TextArea
                      placeholder="请填写项目经理职责"
                      rows={4}
                      disabled={disabledLook}
                    />
                  </Form.Item>
                  <Form.Item
                    label="项目计划时间"
                    name="planTimes"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  >
                    <RangePicker
                      className={styles.selects}
                      suffixIcon={<ClockCircleOutlined />}
                      format="YYYY/MM/DD"
                      disabled={type === 'look'}
                    />
                  </Form.Item>
                  <Form.Item
                    label="项目概述"
                    name="projectOverview"
                    rules={[
                      {
                        required: type !== 'update' && type !== 'updateDraft',
                      },
                    ]}
                  >
                    <TextArea
                      placeholder="请填写项目背景及立项目的"
                      rows={4}
                      disabled={disabledLook}
                    />
                  </Form.Item>
                </div>
                <div className={styles.bottom_checkboxs}>
                  <Form.Item name="ifStart">
                    <Checkbox
                      checked={ifStart}
                      onChange={e => ifStartCheck(e.target.checked)}
                      disabled={disabledLook}
                    >
                      合同签署前开始实施
                    </Checkbox>
                  </Form.Item>
                  <Form.Item name="isDeptCooperation">
                    <Checkbox
                      checked={isCross}
                      onChange={deptCheck}
                      disabled={disabledLook}
                    >
                      跨部门协作
                    </Checkbox>
                  </Form.Item>
                </div>
              </div>
            </Panel>
          </Collapse>
          {isCross && (
            <Collapse
              defaultActiveKey={['1']}
              expandIconPosition="right"
              ghost={true}
            >
              <Panel
                header={
                  <div className={styles.titles}>
                    协作部门信息
                    <div>
                      {type !== 'update' &&
                        type !== 'updateDraft' &&
                        type !== 'look' && (
                          <Button onClick={e => addCooperationDept(e)}>
                            <PlusOutlined className={styles.hide_icon} />
                            添加
                          </Button>
                        )}
                    </div>
                  </div>
                }
                key="1"
                className={classNames(styles.card)}
                id="23"
              >
                <div className={styles.scroll_x}>
                  <div
                    className={styles.cooperation_dept_boxs}
                    style={{
                      width: `${
                        cooperationDeptList.length > 1
                          ? cooperationDeptList.length * 60
                          : 100
                      }%`,
                    }}
                  >
                    {cooperationDeptList.map((item, index) => (
                      <div
                        key={index}
                        style={{
                          width: `${
                            cooperationDeptList.length > 1
                              ? cooperationDeptList.length * 50 * 0.6
                              : 60
                          }%`,
                        }}
                        className={
                          cooperationDeptList.length > 1
                            ? styles.margin_right
                            : styles.margin_auto
                        }
                      >
                        <Form.Item
                          label="协作部门"
                          name={'departmentCooperation' + index}
                          rules={[
                            {
                              required:
                                type !== 'update' && type !== 'updateDraft',
                            },
                          ]}
                          wrapperCol={{ span: 17 }}
                        >
                          <Select
                            showSearch
                            defaultActiveFirstOption={false}
                            placeholder="请选择协作部门"
                            disabled={disabledLook}
                            showArrow={false}
                            filterOption={false}
                            className={styles.selects}
                            notFoundContent={null}
                            value={item.departmentCooperation}
                            filterOption={(input, option) =>
                              option.children
                                .toLowerCase()
                                .indexOf(input.toLowerCase()) >= 0
                            }
                            onChange={value =>
                              cooperationDeptListChange({
                                value,
                                index,
                                key: 'departmentCooperation',
                              })
                            }
                          >
                            {deptList.map(item => (
                              <Option
                                value={`${item.deptFullName}@${item.id}`}
                                key={`${item.deptCode}@${item.id}`}
                              >
                                {item.deptFullName}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                        <Form.Item
                          label="协作部门职责"
                          name={'cooperationDutie' + index}
                          rules={[
                            {
                              required:
                                type !== 'update' && type !== 'updateDraft',
                            },
                          ]}
                          wrapperCol={{ span: 17 }}
                        >
                          <TextArea
                            placeholder="跨部门协作时需填写"
                            disabled={disabledLook}
                            onChange={e =>
                              cooperationDeptListChange({
                                value: e.target.value,
                                index,
                                key: 'cooperationDutie',
                              })
                            }
                            value={item.cooperationDutie}
                            rows={4}
                          />
                        </Form.Item>
                        <Form.Item
                          label="协作项目经理"
                          wrapperCol={{ span: 17 }}
                          name={'cooperationManager' + index}
                          rules={[
                            {
                              required:
                                type !== 'update' && type !== 'updateDraft',
                            },
                          ]}
                        >
                          <Select
                            showSearch
                            defaultActiveFirstOption={false}
                            placeholder="选择协作项目经理"
                            disabled={disabledLook}
                            showArrow={false}
                            className={styles.selects}
                            onChange={value =>
                              cooperationDeptListChange({
                                value,
                                index,
                                key: 'cooperationManager',
                              })
                            }
                            filterOption={false}
                            onSearch={handleSearch}
                            notFoundContent={null}
                          >
                            {nameList.length > 0 &&
                              nameList.map((item, index) => (
                                <Option
                                  value={`${item.employee_name}-${item.deptName}@${item.username}`}
                                  key={index}
                                >
                                  {item.employee_name}-{item.deptName}
                                </Option>
                              ))}
                          </Select>
                        </Form.Item>
                        <Form.Item
                          label="协作项目经理职责"
                          wrapperCol={{ span: 17 }}
                          name={'cooperationManagerDutie' + index}
                          rules={[
                            {
                              required:
                                type !== 'update' && type !== 'updateDraft',
                            },
                          ]}
                        >
                          <TextArea
                            placeholder="跨部门协作时需填写"
                            disabled={disabledLook}
                            onChange={e =>
                              cooperationDeptListChange({
                                value: e.target.value,
                                index,
                                key: 'cooperationManagerDutie',
                              })
                            }
                            rows={4}
                          />
                        </Form.Item>
                        <div className={styles.delete_cooperation_dept}>
                          {type !== 'update' &&
                            type !== 'updateDraft' &&
                            type !== 'look' && (
                              <Popconfirm
                                title="是否删除？"
                                okText="是"
                                cancelText="否"
                                onConfirm={() => deleteCooperationDept(index)}
                              >
                                <Button>删除</Button>
                              </Popconfirm>
                            )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </Panel>
            </Collapse>
          )}
          <Collapse
            defaultActiveKey={['1']}
            expandIconPosition="right"
            ghost={true}
          >
            <Panel
              header={<div className={styles.titles}>项目分类</div>}
              key="1"
              className={classNames(styles.card)}
            >
              <div>
                <div>
                  <Form.Item
                    label="项目分类"
                    name="type"
                    rules={[
                      {
                        required: type !== 'update' && type !== 'updateDraft',
                      },
                    ]}
                  >
                    <Radio.Group
                      onChange={e => changeManageType(e.target.value)}
                      disabled={disabledLook}
                    >
                      <Radio value={'manage'}>经营类</Radio>
                      <Radio value={'no_manage'}>非经营类</Radio>
                      <Radio value={'product_category'}>产品类</Radio>
                    </Radio.Group>
                  </Form.Item>
                  {(manageType === 'manage' || manageType === 'no_manage') && (
                    <Form.Item
                      label={
                        <div>
                          项目级别
                          <Tooltip title={manageTypeTooltip}>
                            <QuestionCircleOutlined style={{ width: 20 }} />
                          </Tooltip>
                        </div>
                      }
                      name="level"
                      rules={[
                        {
                          required: type !== 'update' && type !== 'updateDraft',
                        },
                      ]}
                    >
                      <Select
                        showSearch
                        style={{ width: '80%' }}
                        defaultActiveFirstOption={false}
                        placeholder="选择项目级别"
                        disabled={disabledLook}
                        showArrow={false}
                        filterOption={false}
                        // onSearch={handleSearch}
                        notFoundContent={null}
                        className={styles.selects}
                      >
                        {newLevelList.length > 0 &&
                          newLevelList.map((item, index) => (
                            <Option value={item.code} key={item.code}>
                              {item.code}
                            </Option>
                          ))}
                      </Select>
                    </Form.Item>
                  )}
                  {(manageType === 'manage' ||
                    manageType === 'product_category') && (
                    <Form.Item
                      label={
                        <div>
                          收入分类
                          <Tooltip
                            title={
                              <div>
                                1、一类项目：系统连接及运维服务
                                <br />
                                2、非一类项目：系统集成服务、产品和解决方案服务、软件外包开发服务
                                <br />
                              </div>
                            }
                          >
                            <QuestionCircleOutlined style={{ width: 20 }} />
                          </Tooltip>
                        </div>
                      }
                      name="inComeType"
                      rules={[
                        {
                          required: type !== 'update' && type !== 'updateDraft',
                        },
                      ]}
                    >
                      <Select
                        showSearch
                        defaultActiveFirstOption={false}
                        showArrow={false}
                        disabled={disabledLook}
                        filterOption={false}
                        style={{ width: '80%' }}
                        placeholder="选择收入分类"
                        // onSearch={handleSearch}
                        onChange={InComeTypeChange}
                        notFoundContent={null}
                        className={styles.selects}
                      >
                        {revenueTypeList.length > 0 &&
                          revenueTypeList.map((item, index) => (
                            <Option value={item.code} key={item.name}>
                              {item.name}
                            </Option>
                          ))}
                      </Select>
                    </Form.Item>
                  )}
                </div>
              </div>
            </Panel>
          </Collapse>
          <Collapse
            defaultActiveKey={['1']}
            expandIconPosition="right"
            ghost={true}
          >
            <Panel
              header={
                <div className={styles.titles}>
                  项目成员
                  <div>
                    {type !== 'update' &&
                      type !== 'updateDraft' &&
                      type !== 'look' && (
                        <Button
                          type="primary"
                          onClick={e =>
                            showMoald({ title: '新增团队成员', item: '', e })
                          }
                        >
                          新增
                        </Button>
                      )}
                  </div>
                </div>
              }
              key="1"
              className={classNames(styles.card)}
              id="24"
            >
              <div>
                <div>
                  <Table
                    columns={columns}
                    dataSource={dataSource}
                    // scroll={{ x: 1000 }}
                    pagination={false}
                    // rowSelection={{ columnWidth: 20, ...rowSelection }}
                    // loading={loading}
                    size="middle"
                    className={styles.anTdTable}
                  />
                </div>
              </div>
            </Panel>
          </Collapse>
          <Collapse
            defaultActiveKey={['1']}
            expandIconPosition="right"
            ghost={true}
          >
            <Panel
              header={
                <div className={styles.titles}>
                  <div>
                    计划里程碑
                    <Tooltip
                      title={
                        <div>
                          鼠标移到菱形图标查看概述
                          <br />
                        </div>
                      }
                    >
                      <QuestionCircleOutlined style={{ width: 20 }} />
                    </Tooltip>
                  </div>
                </div>
              }
              key="1"
              className={classNames(styles.card)}
              id="25"
            >
              <div>
                <div className={styles.typeBox}>
                  <div>计划里程碑</div>
                  {planData &&
                    planData.map((item, index) => (
                      <div className={styles.toneItems} key={index}>
                        <div>
                          <Tooltip
                            title={
                              item.facts && (
                                <div>
                                  <div>{item.itemStageName}</div>
                                  <div>
                                    <p>概述：{item.facts}</p>
                                  </div>
                                </div>
                              )
                            }
                          >
                            <img
                              src={hollow_icon}
                              alt=""
                              className={styles.hoverImg}
                              onClick={() =>
                                type !== 'look' && editMilesTone(index, item)
                              }
                            />
                          </Tooltip>
                          <div className={styles.iconLine}></div>
                        </div>
                        <div>
                          <div>
                            {item.itemStageName}
                            {milestoneInfo.map(value => {
                              if (item.itemStageType === value.itemStageType) {
                                return (
                                  <Tooltip
                                    title={
                                      <div>
                                        {value.info}
                                        <br />
                                      </div>
                                    }
                                  >
                                    <QuestionCircleOutlined
                                      style={{ marginLeft: 5 }}
                                    />
                                  </Tooltip>
                                );
                              }
                            })}
                          </div>
                          {item.planEndTime ? (
                            <p className={styles.margin_bottom0}>
                              {moment(item.planEndTime).format('YYYY-MM-DD')}
                            </p>
                          ) : (
                            <p
                              className={styles.hoverP}
                              onClick={() => editMilesTone(index)}
                            >
                              点此设置
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </Panel>
          </Collapse>
          <Collapse
            expandIconPosition="right"
            defaultActiveKey={['1']}
            ghost={true}
          >
            <Panel
              header={
                <div className={styles.titles}>
                  <div>
                    项目风险
                    {riskList.length < 1 && ifStart && type !== 'update' && (
                      <Tooltip
                        title={
                          <span>合同签署前开始实施。请填写项目风险！</span>
                        }
                      >
                        <InfoCircleOutlined
                          style={{ width: 20 }}
                          className={styles.tips}
                        />
                      </Tooltip>
                    )}
                  </div>
                  <div onClick={e => addRiskList(e)}>
                    {type !== 'update' &&
                      type !== 'updateDraft' &&
                      type !== 'look' && <Button type="primary">新增</Button>}
                  </div>
                </div>
              }
              key="1"
              className={classNames(styles.card)}
              id="26"
            >
              <div>
                <div className={styles.problemBox}>
                  {riskList && riskList.length > 0 ? (
                    riskList.map((item, index) => (
                      <div key={index}>
                        <div>
                          <div className={styles.boxTitle}>
                            <div>项目风险{index + 1}</div>
                            {type !== 'update' &&
                              type !== 'updateDraft' &&
                              type !== 'look' && (
                                <div>
                                  <>
                                    <span
                                      className={styles.editBottun}
                                      style={{
                                        marginRight: 8,
                                        cursor: 'pointer',
                                      }}
                                      onClick={() => {
                                        editRiskItem(item, index);
                                      }}
                                    >
                                      <img
                                        src={edit_icon}
                                        alt=""
                                        style={{ cursor: 'pointer' }}
                                      />{' '}
                                      编辑
                                    </span>
                                    <Popconfirm
                                      title="是否删除？"
                                      okText="是"
                                      cancelText="否"
                                      onConfirm={() => deleteRiskItem(index)}
                                    >
                                      <span
                                        className={styles.deleteBottun}
                                        style={{ cursor: 'pointer' }}
                                      >
                                        <img src={delete_icon} alt="" /> 删除
                                      </span>
                                    </Popconfirm>
                                  </>
                                </div>
                              )}
                          </div>
                          <div className={styles.boxContent}>
                            <div>
                              <div>问题及风险</div>
                              <div className={styles.boxContentOver}>
                                {item.risk}
                              </div>
                            </div>
                            <div>
                              <div>应对措施</div>
                              <div className={styles.boxContentOver}>
                                {item.solutions}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div></div>
                      </div>
                    ))
                  ) : (
                    <p style={{ textAlign: 'center', width: '100%' }}>
                      暂无数据
                    </p>
                  )}
                </div>
                <div></div>
              </div>
            </Panel>
          </Collapse>

          <Collapse
            defaultActiveKey={['1']}
            expandIconPosition="right"
            ghost={true}
          >
            <Panel
              header={
                <div className={styles.money_title} id="32">
                  <div>项目计划收入(税后)</div>
                  <div>收入总计：{Number(itemPlanIncome).toFixed(2)}万</div>
                </div>
              }
              key="1"
              showArrow={false}
              className={classNames(styles.card)}
              id="3"
            >
              <div>
                <div>
                  <div className={styles.project_cost}>
                    <div
                      className={styles.project_cost_left}
                      style={{ margin: ' 0 auto' }}
                    >
                      <div className={styles.money_title}>
                        <div>计划收入</div>
                      </div>
                      <div>
                        {yearMoneyList &&
                          yearMoneyList.length > 0 &&
                          yearMoneyList.map(item => {
                            return (
                              item.year &&
                              item.money !== 0 && (
                                <div>
                                  {item.year}年计划收入:
                                  {Number(item.money).toFixed(2)}万
                                </div>
                              )
                            );
                          })}
                      </div>
                      {plannedRevenueInputList &&
                        plannedRevenueInputList.length > 0 &&
                        plannedRevenueInputList.map((item, index) => (
                          <>
                            <div
                              className={classNames(styles.income_year)}
                              key={index}
                            >
                              <div></div>
                            </div>
                            {item.item.map((items, indexs) => (
                              <div
                                className={classNames(
                                  styles.income_year,
                                  index < plannedRevenueInputList.length - 1 &&
                                    plannedRevenueInputList.length > 1 &&
                                    indexs === item.item.length - 1 &&
                                    styles.border,
                                )}
                                key={indexs}
                              >
                                {type !== 'look' && (
                                  <div>
                                    {indexs === item.item.length - 1 ? (
                                      <PlusOutlined
                                        className={styles.icons}
                                        onClick={() =>
                                          addInputItem({
                                            dept: '计划收入',
                                            type: 'inComeType',
                                            index,
                                          })
                                        }
                                      />
                                    ) : (
                                      <PlusOutlined
                                        className={styles.hide_icon}
                                      />
                                    )}
                                    <div>
                                      <div>{indexs + 1}</div>
                                    </div>
                                  </div>
                                )}
                                <div>
                                  <DatePicker
                                    suffixIcon={<ClockCircleOutlined />}
                                    placeholder="选择时间"
                                    format="YYYY/MM/DD"
                                    disabled={type === 'look'}
                                    value={
                                      (items.planTime &&
                                        moment(items.planTime)) ||
                                      ''
                                    }
                                    onChange={(date, deteString) =>
                                      datePickerChange({
                                        date,
                                        deteString,
                                        index,
                                        indexs,
                                        key: 'plannedRevenueInputList',
                                      })
                                    }
                                    className={styles.selects}
                                  />
                                </div>
                                <div className={styles.last_div}>
                                  <InputNumber
                                    min={0}
                                    className={styles.selects}
                                    value={items.planIncome}
                                    precision={2}
                                    placeholder="输入金额"
                                    disabled={type === 'look'}
                                    onChange={value =>
                                      plannedRevenueInputListMoneyChange({
                                        value: value,
                                        index,
                                        indexs,
                                      })
                                    }
                                    style={{ width: '100%' }}
                                  />
                                  {type !== 'look' && (
                                    <div>
                                      {item.item.length !== 1 ? (
                                        <Popconfirm
                                          title="是否删除？"
                                          okText="是"
                                          cancelText="否"
                                          onConfirm={() =>
                                            deleteItem({
                                              type: '计划收入',
                                              index,
                                              indexs,
                                            })
                                          }
                                        >
                                          <CloseOutlined
                                            className={styles.icons}
                                          />
                                        </Popconfirm>
                                      ) : (
                                        <CloseOutlined
                                          className={styles.hide_icon}
                                        />
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </>
                        ))}
                    </div>
                  </div>
                </div>
              </div>
            </Panel>
          </Collapse>
          <Collapse
            defaultActiveKey={['1']}
            expandIconPosition="right"
            ghost={true}
          >
            <Panel
              header={
                <div className={styles.money_title}>
                  <div>项目成本预算</div>
                  <div>预算总计：{Number(itemCostBudgeting).toFixed(2)}万</div>
                </div>
              }
              key="1"
              showArrow={false}
              className={classNames(styles.card)}
              id="33"
            >
              <div>
                <div>
                  <div className={styles.scroll_x}>
                    <div
                      className={styles.project_cost}
                      style={{
                        width: `${
                          cooperationDeptInputList.length > 1
                            ? (cooperationDeptInputList.length + 1) * 50
                            : 100
                        }%`,
                      }}
                    >
                      <div
                        className={classNames(
                          styles.project_cost_left,
                          cooperationDeptInputList.length > 1 && isCross
                            ? styles.margin_right
                            : styles.margin_auto,
                        )}
                      >
                        <div>
                          {projectName && projectName.indexOf('@') !== -1
                            ? projectName.substring(0, projectName.indexOf('@'))
                            : projectName}
                        </div>
                        {deptInputList.length > 0 &&
                          deptInputList.map((item, index) => (
                            <>
                              <div className={styles.income_year} key={index}>
                                <div>
                                  {type !== 'look' && (
                                    <PlusOutlined
                                      className={styles.icons}
                                      onClick={() =>
                                        addInputItem({
                                          dept: '立项部门',
                                          type: 'year',
                                        })
                                      }
                                    />
                                  )}
                                  <div>
                                    部门年度预算：
                                    {Number(item.money).toFixed(2)}万
                                  </div>
                                </div>
                                <div className={styles.last_div}>
                                  <DatePicker
                                    suffixIcon={<ClockCircleOutlined />}
                                    value={
                                      (item.year && moment(item.year)) || ''
                                    }
                                    disabled={type === 'look'}
                                    className={styles.selects}
                                    onChange={(date, deteString) =>
                                      datePickerChange({
                                        date,
                                        deteString,
                                        index,
                                        indexs: -1,
                                        key: 'deptInputList',
                                      })
                                    }
                                    picker="year"
                                  />
                                  {type !== 'look' && (
                                    <div>
                                      {deptInputList.length !== 1 ? (
                                        <Popconfirm
                                          title="是否删除？"
                                          okText="是"
                                          cancelText="否"
                                          onConfirm={() =>
                                            deleteItem({
                                              type: '立项部门',
                                              index,
                                              indexs: -1,
                                            })
                                          }
                                        >
                                          <CloseOutlined
                                            className={styles.icons}
                                          />
                                        </Popconfirm>
                                      ) : (
                                        <CloseOutlined
                                          className={styles.hide_icon}
                                        />
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                              {item.item.map((items, indexs) => (
                                <div
                                  className={classNames(
                                    styles.income_year,
                                    index < deptInputList.length - 1 &&
                                      deptInputList.length > 1 &&
                                      indexs === item.item.length - 1 &&
                                      styles.border,
                                  )}
                                  key={indexs}
                                >
                                  <div>
                                    {type !== 'look' && (
                                      <div>
                                        {indexs === item.item.length - 1 ? (
                                          <PlusOutlined
                                            className={styles.icons}
                                            onClick={() =>
                                              addInputItem({
                                                dept: '立项部门',
                                                type: 'inComeType',
                                                index,
                                              })
                                            }
                                          />
                                        ) : (
                                          <PlusOutlined
                                            className={styles.hide_icon}
                                          />
                                        )}
                                      </div>
                                    )}
                                    <div>
                                      <Select
                                        style={{ width: 100 }}
                                        className={styles.selects}
                                        placeholder="费用类别"
                                        showSearch
                                        value={items.planContent}
                                        disabled={type === 'look'}
                                        onChange={value =>
                                          itemCostTypeChange({
                                            value,
                                            index,
                                            indexs,
                                            type: 'deptInputList',
                                          })
                                        }
                                      >
                                        {inComeTypeList.length > 0 &&
                                          inComeTypeList.map(
                                            (item, itemIndex) => (
                                              <Option
                                                value={`${item.name}@${item.code}`}
                                                key={itemIndex}
                                              >
                                                {item.name}
                                              </Option>
                                            ),
                                          )}
                                      </Select>
                                    </div>
                                  </div>
                                  <div>
                                    <InputNumber
                                      min={0}
                                      className={styles.selects}
                                      value={items.planCost}
                                      placeholder="输入金额"
                                      precision={2}
                                      disabled={type === 'look'}
                                      // type='number'
                                      onChange={value =>
                                        deptInputListMoneyChange({
                                          value: value,
                                          index,
                                          indexs,
                                        })
                                      }
                                      suffix="万元"
                                    />
                                  </div>
                                  <div className={styles.last_div}>
                                    <Input
                                      min={0}
                                      className={styles.selects}
                                      value={items.proof}
                                      disabled={type === 'look'}
                                      onChange={e =>
                                        deptInputListChange({
                                          value: e.target.value,
                                          index,
                                          indexs,
                                        })
                                      }
                                      placeholder="备注:预算依据"
                                    />
                                    {type !== 'look' && (
                                      <div>
                                        {item.item.length !== 1 ? (
                                          <Popconfirm
                                            title="是否删除？"
                                            okText="是"
                                            cancelText="否"
                                            onConfirm={() =>
                                              deleteItem({
                                                type: '立项部门',
                                                index,
                                                indexs,
                                              })
                                            }
                                          >
                                            <CloseOutlined
                                              className={styles.icons}
                                            />
                                          </Popconfirm>
                                        ) : (
                                          <CloseOutlined
                                            className={styles.hide_icon}
                                          />
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </>
                          ))}
                      </div>

                      {isCross &&
                        cooperationDeptInputList.length > 0 &&
                        cooperationDeptInputList.map((item, index) => {
                          const { content } = item;
                          return (
                            <div
                              className={classNames(
                                styles.project_cost_right,
                                styles.margin_right,
                              )}
                            >
                              <div>{item.name}</div>
                              {content.length > 0 &&
                                content.map((contentItem, contentIndex) => {
                                  return (
                                    <>
                                      <div
                                        className={styles.income_year}
                                        key={contentIndex}
                                      >
                                        {type !== 'look' && (
                                          <div>
                                            <PlusOutlined
                                              className={styles.icons}
                                              onClick={() =>
                                                addCooperationInputItem({
                                                  dept: index,
                                                  type: 'year',
                                                  contentIndex,
                                                  indexs: -1,
                                                })
                                              }
                                            />
                                            <div>
                                              部门年度预算：
                                              {Number(
                                                contentItem.money,
                                              ).toFixed(2)}
                                              万
                                            </div>
                                          </div>
                                        )}
                                        <div className={styles.last_div}>
                                          <DatePicker
                                            suffixIcon={<ClockCircleOutlined />}
                                            value={
                                              (contentItem.year &&
                                                moment(contentItem.year)) ||
                                              ''
                                            }
                                            className={styles.selects}
                                            disabled={type === 'look'}
                                            onChange={(date, deteString) =>
                                              datePickerChange({
                                                date,
                                                deteString,
                                                index,
                                                indexs: -1,
                                                key: 'cooperationDeptInputList',
                                                contentIndex,
                                              })
                                            }
                                            picker="year"
                                          />
                                          {type !== 'look' && (
                                            <div>
                                              {content.length !== 1 ? (
                                                <Popconfirm
                                                  title="是否删除？"
                                                  okText="是"
                                                  cancelText="否"
                                                  onConfirm={() =>
                                                    deleteCooperationInputItem({
                                                      dept: index,
                                                      contentIndex,
                                                      indexs: -1,
                                                    })
                                                  }
                                                >
                                                  <CloseOutlined
                                                    className={styles.icons}
                                                  />
                                                </Popconfirm>
                                              ) : (
                                                <CloseOutlined
                                                  className={styles.hide_icon}
                                                />
                                              )}
                                            </div>
                                          )}
                                        </div>
                                      </div>

                                      {contentItem.item.map((items, indexs) => {
                                        return (
                                          <div
                                            className={classNames(
                                              styles.income_year,
                                              index < content.length - 1 &&
                                                content.length > 1 &&
                                                indexs ===
                                                  contentItem.item.length - 1 &&
                                                styles.border,
                                            )}
                                            key={indexs}
                                          >
                                            <div>
                                              {type !== 'look' && (
                                                <div>
                                                  {indexs ===
                                                  contentItem.item.length -
                                                    1 ? (
                                                    <PlusOutlined
                                                      className={styles.icons}
                                                      onClick={() =>
                                                        addCooperationInputItem(
                                                          {
                                                            dept: index,
                                                            type: 'money',
                                                            contentIndex,
                                                            indexs,
                                                          },
                                                        )
                                                      }
                                                    />
                                                  ) : (
                                                    <PlusOutlined
                                                      className={
                                                        styles.hide_icon
                                                      }
                                                    />
                                                  )}
                                                </div>
                                              )}
                                              <div>
                                                <Select
                                                  style={{ width: 100 }}
                                                  value={items.planContent}
                                                  className={styles.selects}
                                                  disabled={type === 'look'}
                                                  onChange={value =>
                                                    itemCostTypeChange({
                                                      value,
                                                      index,
                                                      indexs,
                                                      type:
                                                        'cooperationDeptInputList',
                                                      contentIndex,
                                                    })
                                                  }
                                                  placeholder="费用类别"
                                                  showSearch
                                                >
                                                  {inComeTypeList.length > 0 &&
                                                    inComeTypeList.map(
                                                      (item, index) => (
                                                        <Option
                                                          value={`${item.name}@${item.code}`}
                                                          disabled={
                                                            type === 'look'
                                                          }
                                                          key={index}
                                                        >
                                                          {item.name}
                                                        </Option>
                                                      ),
                                                    )}
                                                </Select>
                                              </div>
                                            </div>
                                            <div>
                                              <InputNumber
                                                min={0}
                                                className={styles.selects}
                                                placeholder="输入金额"
                                                precision={2}
                                                value={items.money}
                                                disabled={type === 'look'}
                                                onChange={value =>
                                                  cooperationDeptInputListMoneyChange(
                                                    {
                                                      value,
                                                      contentIndex,
                                                      index,
                                                      indexs,
                                                    },
                                                  )
                                                }
                                                // suffix="万元"
                                              />
                                            </div>
                                            <div className={styles.last_div}>
                                              <Input
                                                min={0}
                                                value={items.proof}
                                                className={styles.selects}
                                                disabled={type === 'look'}
                                                onChange={e =>
                                                  cooperationDeptInputListChange(
                                                    {
                                                      value: e.target.value,
                                                      contentIndex,
                                                      index,
                                                      indexs,
                                                    },
                                                  )
                                                }
                                                placeholder="备注:预算依据"
                                              />
                                              {type !== 'look' && (
                                                <div>
                                                  {contentItem.item.length !==
                                                  1 ? (
                                                    <Popconfirm
                                                      title="是否删除？"
                                                      okText="是"
                                                      cancelText="否"
                                                      onConfirm={() =>
                                                        deleteCooperationInputItem(
                                                          {
                                                            contentIndex,
                                                            dept: index,
                                                            indexs,
                                                          },
                                                        )
                                                      }
                                                    >
                                                      <CloseOutlined
                                                        className={styles.icons}
                                                      />
                                                    </Popconfirm>
                                                  ) : (
                                                    <CloseOutlined
                                                      className={
                                                        styles.hide_icon
                                                      }
                                                    />
                                                  )}
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        );
                                      })}
                                    </>
                                  );
                                })}
                            </div>
                          );
                        })}
                    </div>
                  </div>
                </div>
              </div>
            </Panel>
          </Collapse>
          <Collapse
            defaultActiveKey={['1']}
            expandIconPosition="right"
            ghost={true}
          >
            <Panel
              header={
                <div className={styles.money_title}>
                  <div>项目利润</div>
                  <div>
                    预估利润：
                    {(
                      Number(itemPlanIncome) - Number(itemCostBudgeting)
                    ).toFixed(2)}
                    万
                  </div>
                </div>
              }
              key="1"
              showArrow={false}
              className={classNames(styles.card, styles.card_last)}
              id="44"
            >
              <Form.Item
                label="利润率"
                name="profitRate"
                rules={[
                  {
                    required: type !== 'update' && type !== 'updateDraft',
                  },
                ]}
              >
                <InputNumber addonAfter="%" controls={false} precision={2} />
              </Form.Item>
            </Panel>
          </Collapse>
        </div>
        <div className={styles.submit_boxs}>
          <div>
            <div>
              {type !== 'look' && (
                <>
                  <Button type="primary" htmlType="submit" disabled={disabled}>
                    提交
                  </Button>
                  <Button onClick={() => saveSubmit()} disabled={disabled}>
                    保存
                  </Button>
                  {/*<Button*/}
                  {/*  onClick={() => {*/}
                  {/*    htmlToPDF(*/}
                  {/*      'exportFormContent',*/}
                  {/*      form.getFieldValue('itemNum'),*/}
                  {/*    );*/}
                  {/*  }}*/}
                  {/*  disabled={disabled}*/}
                  {/*>*/}
                  {/*  导出为pdf（仅测试）*/}
                  {/*</Button>*/}
                </>
              )}
              {type === 'look' && (
                <>
                  <Button disabled={disabled} type="primary">
                    编辑中
                  </Button>
                  <Button onClick={() => window.history.go(-1)}>返回</Button>{' '}
                </>
              )}
              {/* <Button onClick={() => savePDF()}>导出为pdf</Button> */}
            </div>
          </div>
        </div>
      </Form>

      <Modal
        title={`${modalTitle}`}
        visible={visibleModal}
        onOk={handleOk}
        onCancel={handleCancel}
        maskClosable={false}
        footer={null}
      >
        <Form
          layout="vertical"
          form={modalFrom}
          name="nest-messages"
          onFinish={onFinishMoadlFormData}
        >
          {moaldType === 'milesTone' && (
            <>
              <Form.Item
                label="计划完成时间"
                name="planEndTime"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <DatePicker
                  suffixIcon={<ClockCircleOutlined />}
                  className={styles.selects}
                />
              </Form.Item>
              <Form.Item label="概述" name="facts">
                <TextArea className={styles.selects} rows={4} />
              </Form.Item>
            </>
          )}
          {moaldType === 'risk' && (
            <>
              <Form.Item
                label="风险描述"
                name="risk"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Input className={styles.selects} />
              </Form.Item>
              <Form.Item
                label="应对措施"
                name="solutions"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Input className={styles.selects} />
              </Form.Item>
            </>
          )}

          {moaldType === 'team' && (
            <>
              <Form.Item
                label="姓名"
                name="name"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  showSearch
                  defaultActiveFirstOption={false}
                  showArrow={false}
                  filterOption={false}
                  disabled={modalTitle === '编辑团队成员'}
                  className={styles.selects}
                  onSearch={handleSearch}
                  notFoundContent={null}
                >
                  {nameList.length > 0 &&
                    nameList.map((item, index) => (
                      <Option
                        value={`${item.employee_name}-${item.username}@${item.deptName}+${item.dept_id}`}
                        key={index}
                      >
                        {item.employee_name}-{item.deptName}
                      </Option>
                    ))}
                </Select>
              </Form.Item>
              <Form.Item
                label="担任角色"
                name="character"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Input className={styles.selects} />
              </Form.Item>
              <Form.Item
                label="主要职责"
                name="dutie"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Input className={styles.selects} />
              </Form.Item>
              <Form.Item
                label="进入项目组时间"
                name="startTime"
                style={{ float: 'left', marginRight: 50 }}
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <DatePicker
                  onChange={(date, dateString) =>
                    handleDatePickerChange('startTime', dateString)
                  }
                  format={dateFormat}
                  suffixIcon={<ClockCircleOutlined />}
                  disabledDate={current => {
                    return endTime && current > moment(endTime);
                  }}
                  className={styles.rangePicker}
                />
              </Form.Item>
              <Form.Item label="出项目组时间" name="endTime">
                <DatePicker
                  onChange={(date, dateString) =>
                    handleDatePickerChange('endTime', dateString)
                  }
                  format={dateFormat}
                  suffixIcon={<ClockCircleOutlined />}
                  disabledDate={current => {
                    return startTime && current < moment(startTime);
                  }}
                  className={styles.rangePicker}
                />
              </Form.Item>
            </>
          )}
          <Form.Item style={{ marginBottom: 0 }}>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
