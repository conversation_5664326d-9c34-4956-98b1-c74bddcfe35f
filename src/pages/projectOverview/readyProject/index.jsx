import React, { useState, useEffect } from 'react';
import {
  getSearchListInfo,
  getDeptListInfo,
  getInComeType,
  delProjectListPage,
  addIntendStandItem,
  intendItemToStandItem,
  queryProjectListById,
  revokeProjectStandItem,
  getDynamicSearchInfo,
  downProjectListFile,
  uploadFileExcel,
  getButtonObject,
  getItemTrial,
} from './service';
import {
  Input,
  DatePicker,
  message,
  Button,
  Tooltip,
  Pagination,
  Select,
  Radio,
  InputNumber,
  Modal,
  Form,
  Row,
  Col,
  Popconfirm,
  Upload,
} from 'antd';
import {
  DownOutlined,
  ClockCircleOutlined,
  UpOutlined,
  SearchOutlined,
  PlusOutlined,
  ExclamationCircleOutlined,
  DownloadOutlined,
} from '@ant-design/icons';

import { Link } from 'umi';
import styles from './index.less';
import moment from 'moment';
import ProTable from '@ant-design/pro-table';
import { MyContext } from '../../home';
import Procedure from '@/pages/puview/procedure';
import { BASE_URl } from "../../../utils/constant";
const { Option } = Select;

export default () => {
  const [form] = Form.useForm();
  const { RangePicker } = DatePicker;
  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  const [flushNum, setflushNum] = useState(0);
  const [addIsLook, setAddIsLook] = useState(true);
  const [addIsLooks, setAddIsLooks] = useState(true);
  const [allMOney, setallMOney] = useState(0);
  const [allMOneys, setallMOneys] = useState(0);
  const userInfo = JSON.parse(localStorage.getItem('userInfo'));

  const [processData, setProcessData] = useState();
  const [showProcedureModal, setShowProcedureModal] = useState(false);

  useEffect(() => {
    setButtonList(context);
  }, [context]);

  const [uploadProps, serUploadProps] = useState({
    action: BASE_URl + '/projectIntendStand/upLoadProjectIntendStand',
    headers: {
      isToken: false,
      Authorization: `Bearer ${userInfo.access_token}`,
    },
    accept: '.xls',
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`上传成功`);
        getlist(parmas, tab);
      } else if (info.file.status === 'error') {
        message.error({
          content: `上传失败!${info.file.response.msg}`,
          key: 'error',
          duration: 3,
        });
      }
    },
  });

  //判断计划收入和计划预算是否有数据
  const [itemPlanIsLook, setItemPlanIsLook] = useState(false);
  const [itemCostLooks, setItemCostIsLooks] = useState(false);

  //费用类分类
  const [inComeTypeList, setInComeTypeList] = useState([]);

  const [visibleModaledit, setvisibleModaledit] = useState(false);

  const [projectName, setProjectName] = useState('立项部门');

  //部门列表
  const [deptList, setDeptList] = useState([]);

  const [formParam, setformParam] = useState({
    title: '新增预立项项目',
    width: '1100px',
    fromType: 'auto',
  });

  //编辑数据回显
  const [editDate, setEditDate] = useState({});

  //数据状态-0新增，1编辑，2查看
  const [dataState, setDataState] = useState(0);

  //计划收入列
  const [plannedRevenueInputList, setPlannedRevenueInputList] = useState([
    {
      year: '',
      money: 0,
      item: [{ planTime: '', planIncome: '' }],
    },
  ]);

  //计划预算
  const [plannedInputList, setplannedInputList] = useState([
    {
      year: '',
      years: '',
      money: '',
      item: [{ type: '', money: '' }],
    },
  ]);

  const [yearMoneyList, setYearMoneyList] = useState([]);

  const [revenueTypeList, setRevenueTypeList] = useState([]);

  const downloadExcel = async () => {
    let resp = await uploadFileExcel();
    const blob = new Blob([resp], {
      type: 'application/vnd.ms-excel;charset=uft-8',
    });
    const blobUrl = window.URL.createObjectURL(blob);
    downloadExcels(blobUrl);
  };

  //默认导出全部数据
  const downloadCustomAll = async () => {
    let sendParam = {
      itemName: '',
      itemNum: '',
    };
    if (openStatus) {
      sendParam.itemName = searchParmas.itemName;
      sendParam.itemNum = searchParmas.itemNum;
    } else {
      sendParam = searchParmas;
    }
    let resp = await downProjectListFile({ ...sendParam });
    const blob = new Blob([resp], {
      type: 'application/vnd.ms-excel;charset=uft-8',
    });
    const blobUrl = window.URL.createObjectURL(blob);
    download(blobUrl);
  };

  function download(blobUrl) {
    const a = document.createElement('a');
    a.download = '预立项项目列表';
    a.href = blobUrl;
    a.click();
  }
  function downloadExcels(blobUrl) {
    const a = document.createElement('a');
    a.download = '预立项项目导入模板';
    a.href = blobUrl;
    a.click();
  }

  const btnReset = () => {
    setParmas({ ...parmas, page: 1 });
    setSearchParmas({
      itemName: '',
      itemNum: '',
      year: getThisYear(),
    });
    setSearchParmasBind({
      itemName: '',
      itemNum: '',
      costEnd: null,
      costStart: null,
      deptName: [],
      endTime: '',
      incomeEnd: '',
      incomeStart: null,
      standState: null,
      startTime: null,
      state: null,
      type: [],
      year: getThisYear(),
      projectClient: '',
    });

    gerlist({ ...parmas, year: getThisYear() });
  };

  const btnSerch = () => {
    gerlist({ ...parmas, ...searchParmas, page: 1 });
    setParmas({ ...parmas, page: 1 });
  };

  //数据列表操作--删除
  const dataDel = async value => {
    Modal.confirm({
      title: '提示',
      icon: <ExclamationCircleOutlined />,
      content: '你将删除该数据，是否继续?',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        let sendParam = {
          id: value.id,
        };
        let resp = await delProjectListPage(sendParam);
        if (resp.code == 200) {
          message.success('删除成功');
          gerlist({ ...parmas, ...searchParmas });
        } else {
          message.error('操作失败');
        }
      },
    });
  };

  //数据列表操作--撤销
  const dataRevoke = async value => {
    Modal.confirm({
      title: '提示',
      icon: <ExclamationCircleOutlined />,
      content: '确定撤销此项目立项吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        let sendParam = {
          id: value.id,
        };
        let resp = await revokeProjectStandItem(sendParam);
        if (resp.code == 200) {
          message.success('撤销成功');
          gerlist({ ...parmas, ...searchParmas });
        } else {
          message.error('撤销失败');
        }
      },
    });
  };

  //数据列表操作--立项
  const datalX = async value => {
    Modal.confirm({
      title: '提示',
      content: '确定要立项该项目？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        let sendParam = {
          id: value.id,
        };
        let resp = await intendItemToStandItem(sendParam);
        if (resp.code == 200) {
          message.success('立项成功');
          gerlist({ ...parmas, ...searchParmas });
        } else {
          message.error('立项失败');
        }
      },
    });
  };

  //获取费用类分类
  const getInComeTypes = async () => {
    const { data, code } = await getInComeType();
    if (code === 200) {
      setInComeTypeList(data);
    }
  };

  //获取部门下拉
  const getDeptListInfos = async value => {
    const { code, data } = await getDeptListInfo(value);
    if (code === 200) {
      const addDeptList = new Set();
      data.forEach(item => {
        let repeat = false;
        addDeptList.forEach(value => {
          if (value.deptFullName === item.deptFullName) {
            repeat = true;
          }
        });
        if (!repeat) {
          item.deptFullName && addDeptList.add(item);
        }
      });
      setDeptList(addDeptList);
    }
  };

  //添加列表
  const addInputItem = ({ type, index }) => {
    //增加年度
    if (type === 'inComeType') {
      const year = {
        year: '',
        money: 0,
        item: [{ type: '', money: 0 }],
      };
      setPlannedRevenueInputList([...plannedRevenueInputList, year]);
    } else if (type === 'allOut') {
      const items = {
        year: '',
        years: '',
        money: '',
        item: [{ type: '', money: '' }],
      };
      setplannedInputList([...plannedInputList, items]);
    } else if (type === 'Out') {
      let list = plannedInputList;
      const items = { type: '', money: '' };
      list[index].item.push(items);
      setplannedInputList([...list]);
    }
  };

  const datePickerChange = value => {
    const { date, deteString, index, indexs, key, contentIndex } = value;
    if (key === 'plannedRevenueInputList') {
      const list = plannedRevenueInputList;
      if (indexs === 0) {
        list[index].year = deteString;
      }
      list[index].item[0].planTime = date;
      setPlannedRevenueInputList([...list]);
    } else {
      let list = plannedInputList;
      list[index].year = date;
      list[index].years = deteString;
      setplannedInputList([...list]);
    }
  };

  const opens = value => {
    if (value.type == 'information') {
      setAddIsLook(!addIsLook);
    } else {
      setAddIsLooks(!addIsLooks);
    }
  };

  //费用类别改变
  const itemCostTypeChange = porpos => {
    const { value, index, indexs } = porpos;
    const list = plannedInputList;
    list[index].item[indexs].type = value;
    setplannedInputList([...list]);
  };

  const deptInputListMoneyChange = porps => {
    const { value, index, indexs } = porps;
    const list = plannedInputList;
    list[index].item[indexs].money = Number(value);
    let money = 0;
    list[index].item.forEach(value => {
      money += Number(Number(value.money || 0).toFixed(2));
    });
    list[index].money = money;
    setplannedInputList([...list]);
  };

  const plannedRevenueInputListMoneyChange = porps => {
    const { value, index, indexs } = porps;
    const list = plannedRevenueInputList;
    list[index].item[indexs].planIncome = Number(value);
    let money = 0;
    list[index].item.forEach(value => {
      money += Number(value.planIncome || 0).toFixed(2);
    });
    list[index].money = Number(money).toFixed(2);
    setPlannedRevenueInputList([...list]);
  };

  const deleteItem = value => {
    const { index, indexs, type } = value;
    if (type === 'income') {
      let list = plannedRevenueInputList;
      list.splice(index, 1);
      setPlannedRevenueInputList([...list]);
    } else if (type === 'itemAll') {
      let list = plannedInputList;
      list.splice(indexs, 1);
      setplannedInputList([...list]);
    } else {
      let list = plannedInputList;
      list[index].item.splice(indexs, 1);
      setplannedInputList([...list]);
    }
  };

  useEffect(() => {
    getInComeTypes();
  }, []);

  function getThisYear() {
    const date = new Date();
    let years = date.getFullYear().toString();
    return years;
  }

  useEffect(() => {
    let newplannedInputList = plannedInputList;
    newplannedInputList.forEach(item => {
      item.money = 0;
      item.item.forEach(items => {
        item.money = strip(item.money + Number(items.money));
      });
    });
    let number = flushNum + 1;
    setflushNum(number);
    setplannedInputList(newplannedInputList);
    let moneys = 0;
    if (newplannedInputList.length > 0) {
      newplannedInputList.forEach(item => {
        moneys = moneys + item.money;
      });
      setallMOneys(strip(Number(moneys)));
    }
  }, [plannedInputList]);

  const nameChange = value => {
    setProjectName(value);
  };

  useEffect(() => {
    const planList = plannedRevenueInputList;
    if (planList.length > 0) {
      let tempArr = [];
      let afterData = [];
      for (let i = 0; i < planList.length; i++) {
        if (tempArr.indexOf(planList[i].year.substring(0, 4)) === -1) {
          afterData.push({
            year: planList[i].year.substring(0, 4),
            money: Number(planList[i].money),
          });
          tempArr.push(planList[i].year.substring(0, 4));
        } else {
          for (let j = 0; j < afterData.length; j++) {
            if (afterData[j].year == planList[i].year.substring(0, 4)) {
              afterData[j].money += Number(planList[i].money);
              break;
            }
          }
        }
      }
      setYearMoneyList(afterData);
      let moneys = 0;
      if (afterData.length > 0) {
        afterData.forEach(item => {
          moneys = moneys + item.money;
        });
        setallMOney(strip(moneys));
      }
    }
  }, [plannedRevenueInputList]);

  //浮点数溢出问题
  function strip(num, precision = 12) {
    return +parseFloat(num.toPrecision(precision));
  }

  const { TextArea } = Input;

  //存储选择查询结果集
  const [selectItemRturn, setSelectItemRturn] = useState({});

  function detailCoDept(value) {
    let reset = '无';
    if (value) {
      if (value.length > 0) {
        reset = value[0].departmentCooperation;
      }
    }

    return reset;
  }

  const onFinish = async values => {
    let costParam = [];
    let incomeParam = [];
    plannedInputList.forEach((item, index) => {
      item.item.forEach((items, indexs) => {
        if (items.type && item.money) {
          let newParam = {
            planTime: moment(item.year).format('YYYY-MM-DD'),
            planCost: items.money,
            money: item.money,
            planContent: items.type.split('@')[0],
            planContentSubject: items.type.split('@')[1],
            proof: '',
            type: '',
            deptId: '',
            itemDept: '',
          };
          costParam.push(newParam);
        }
      });
    });
    plannedRevenueInputList.forEach(item => {
      if (item.money && item.year) {
        let newpaaram = {
          planTime: item.year,
          planIncome: item.money,
        };
        incomeParam.push(newpaaram);
      }
    });

    let cooperations = [];
    if (values.departmentCooperation) {
      cooperations = [
        {
          cooperationDutie: '',
          cooperationManager: '',
          cooperationManagerDutie: '',
          departmentCooperation: values.departmentCooperation.split('@')[0],
          deptId: values.departmentCooperation.split('@')[1],
        },
      ];
    }

    let lastSendParam = {
      standYear: moment(values.year).format('YYYY'),
      dept: values.deptFull.split('@')[0],
      deptFull: values.deptFull,
      deptId: values.deptFull.split('@')[1],
      planTimes: values.planTimes,
      endTime: moment(values.planTimes[1]).format('YYYY-MM-DD'),
      startTime: moment(values.planTimes[0]).format('YYYY-MM-DD'),
      name: values.name,
      projectClient: values.projectClient,
      projectOverview: values.projectOverview,
      inComeType: values.type,
      itemPlan: incomeParam,
      itemCost: costParam,
      cooperation: cooperations,
    };

    if (dataState === 1) {
      lastSendParam.id = editDate.id;
    }

    let resp = await addIntendStandItem(lastSendParam);

    if (resp.code == 200) {
      if (dataState === 1) {
        message.success('修改成功');
      } else {
        message.success('新增成功');
      }
      setVisibleModal(false);
      setplannedInputList([
        {
          year: '',
          years: '',
          money: '',
          item: [{ type: '', money: '' }],
        },
      ]);
      setPlannedRevenueInputList([
        {
          year: '',
          money: 0,
          item: [{ planTime: '', planIncome: '' }],
        },
      ]);

      form.setFieldsValue({
        departmentCooperation: '',
        deptFull: '',
        name: '',
        planTimes: null,
        projectClient: '',
        projectOverview: '',
        type: '',
        year: null,
      });

      gerlist({ ...parmas, ...searchParmas });
    } else {
      message.error('新增数据失败');
    }
  };

  //查询展开收起状态
  const [openStatus, setOpenStatus] = useState(true);

  useEffect(() => {
    setButtonList(context);
  }, [context]);
  useEffect(() => {
    setColumns(totalColumns);
  }, [buttonList, deptList]);

  //解决数据同步问题
  const [numFlush, setNumFlush] = useState(1);

  const dateFormat = 'YYYY/MM/DD';
  const [data, setData] = useState([]);
  const [dataTotal, setDataTotal] = useState(0);
  // 关注列表id
  const [keys, setKeys] = useState([]);
  const [recordParams, setRecordParams] = useState([]);

  //表下拉框查询
  const [dorwnList, setDorwnList] = useState([]);

  const [columns, setColumns] = useState(totalColumns);

  const [visibleModal, setVisibleModal] = useState(false);

  const [visibleModalUpdate, setVisibleModalUpdate] = useState(false);

  //多维查询数组数据--选择查询列表--查询可选字段
  const [selectList, setSelectList] = useState([
    {
      name: '立项部门',
      value: '1',
      isSelected: false,
      key: null,
    },
    {
      name: '协作部门',
      value: '2',
      isSelected: false,
      key: null,
    },
    {
      name: '项目经理',
      value: '3',
      isSelected: false,
      key: null,
    },
    {
      name: '项目团队人员',
      value: '4',
      isSelected: false,
      key: null,
    },
    {
      name: '项目状态',
      value: '5',
      isSelected: false,
      key: null,
    },
    {
      name: '项目类型',
      value: '6',
      isSelected: false,
      key: null,
    },
    {
      name: '项目级别',
      value: '7',
      isSelected: false,
      key: null,
    },
    {
      name: '收入分类',
      value: '8',
      isSelected: false,
      key: null,
    },
    {
      name: '项目把握度',
      value: '9',
      isSelected: false,
      key: null,
    },
    {
      name: '当前里程碑',
      value: '10',
      isSelected: false,
      key: null,
    },
    {
      name: '项目进度红绿灯',
      value: '11',
      isSelected: false,
      key: null,
    },
    {
      name: '项目收入红绿灯',
      value: '12',
      isSelected: false,
      key: null,
    },
  ]);

  //多维查询数组数据--区间查询列表--查询可选字段
  const [selectListCoped, setSelectListCoped] = useState([
    {
      name: '计划收入',
      value: '11',
      isSelected: false,
      key: null,
      type: 'number',
    },
    {
      name: '已确收入占比',
      value: '21',
      isSelected: false,
      key: null,
      type: 'number',
    },
    {
      name: '确收入占比',
      value: '31',
      isSelected: false,
      key: null,
      type: 'number',
    },
    {
      name: '计划成本',
      value: '41',
      isSelected: false,
      key: null,
      type: 'number',
    },
    {
      name: '已核成本',
      value: '51',
      isSelected: false,
      key: null,
      type: 'number',
    },
    {
      name: '已核成本占比',
      value: '61',
      isSelected: false,
      key: null,
      type: 'double',
    },
    {
      name: '当前里程碑进度百分比',
      value: '71',
      isSelected: false,
      key: null,
      type: 'double',
    },
    {
      name: '项目进度百分比',
      value: '81',
      isSelected: false,
      key: null,
      type: 'double',
    },
    {
      name: '完工率',
      value: '91',
      isSelected: false,
      key: null,
      type: 'double',
    },
  ]);

  const handleOnSearchTJ = () => {
    setParmas({ ...parmas, page: 1 });
    gerlist({ ...parmas, ...searchParmas, page: 1 });
  };

  //自定义模板方法
  const downloadCustom = () => {
    setVisibleModal(true);
    setformParam({ ...formParam, title: '新增预立项项目' });
    setDataState(0);
    setplannedInputList([
      {
        year: '',
        years: '',
        money: '',
        item: [{ type: '', money: '' }],
      },
    ]);
    setPlannedRevenueInputList([
      {
        year: '',
        money: 0,
        item: [{ planTime: '', planIncome: '' }],
      },
    ]);

    form.setFieldsValue({
      departmentCooperation: '',
      deptFull: '',
      name: '',
      planTimes: null,
      projectClient: '',
      projectOverview: '',
      type: '',
      year: moment(new Date().getFullYear().toString()),
    });
  };

  const handleOk = async () => {
    // let resp = await downProjectListFile(checkedLists);
    // console.log('123', resp);
    setVisibleModal(false);
  };

  const handleCancel = () => {
    setVisibleModal(false);
  };

  const handleCancellook = () => {
    setvisibleModaledit(false);
  };

  const gotoeditlook = () => {
    setvisibleModaledit(false);
    datalEdit(editDate);
  };

  const datalEdit = async value => {
    setDataState(1);
    let sendParam = { id: value.id };
    let { code, data } = await queryProjectListById(sendParam);
    if (code == 200) {
      const formData = { ...data };
      formData.name = data.name;
      formData.managerFull = data.manager;
      formData.deptFull = data.dept + '@' + data.deptId;
      formData.projectClient = data.projectClient;
      formData.projectOverview = data.projectOverview;
      formData.type = data.inComeType;
      if (data.standYear) {
        formData.year = moment(data.standYear.toString());
        let year = '';
        let myyear = new Date();
        year = myyear.getFullYear();
        console.log('data.standYear.toString()', year);
      }

      if (data.startTime && data.endTime) {
        formData.planTimes = [moment(data.startTime), moment(data.endTime)];
      }
      if (data.cooperation) {
        formData.departmentCooperation =
          data.cooperation[0].departmentCooperation +
          '@' +
          data.cooperation[0].deptId;
      }
      if (data.itemCost) {
        if (data.itemCost.length > 0) {
          let costItem = [];
          data.itemCost.forEach(item => {
            let labelArr = [];
            costItem.forEach(labelarrs => {
              labelArr.push(labelarrs.years);
            });
            if (labelArr.includes(item.planTime.substring(0, 4))) {
              let newITems = {
                type: item.planContent + '@' + item.planContentSubject,
                money: item.planCost,
              };
              costItem.forEach(costItemS => {
                if (costItemS.years == item.planTime.substring(0, 4)) {
                  costItemS.item.push(newITems);
                  costItemS.money =
                    Number(costItemS.money) + Number(item.planCost);
                }
              });
            } else {
              let items = {
                money: Number(item.planCost),
                years: item.planTime.substring(0, 4),
                year: moment(item.planTime),
                item: [
                  {
                    type: item.planContent + '@' + item.planContentSubject,
                    money: item.planCost,
                  },
                ],
              };
              costItem.push(items);
            }
          });

          setplannedInputList(costItem);
        }
      }
      if (data.itemPlan) {
        if (data.itemPlan.length > 0) {
          let plantArr = [];
          data.itemPlan.forEach(items => {
            let param = {
              money: items.planIncome,
              year: items.planTime,
              item: [
                {
                  planTime: moment(items.planTime),
                  planIncome: items.planIncome,
                },
              ],
            };
            plantArr.push(param);
          });
          setPlannedRevenueInputList(plantArr);
        }
      }

      form.setFieldsValue({ ...formData });
      setEditDate(data);
      setDataState(1);
      setformParam({ ...formParam, title: '编辑预立项项目' });
      setVisibleModal(true);
    }
  };

  const datalLook = async value => {
    setDataState(2);
    let sendParam = { id: value.id };
    let { code, data } = await queryProjectListById(sendParam);
    if (code == 200) {
      setEditDate(data);
      if (data.itemCost) {
        if (data.itemCost.length > 0) {
          setItemCostIsLooks(true);
          let costItem = [];
          data.itemCost.forEach(item => {
            let labelArr = [];
            costItem.forEach(labelarrs => {
              labelArr.push(labelarrs.years);
            });
            if (labelArr.includes(item.planTime.substring(0, 4))) {
              let newITems = {
                type: item.planContent + '@' + item.planContentSubject,
                money: item.planCost,
              };
              costItem.forEach(costItemS => {
                if (costItemS.years == item.planTime.substring(0, 4)) {
                  costItemS.item.push(newITems);
                  costItemS.money =
                    Number(costItemS.money) + Number(item.planCost);
                }
              });
            } else {
              let items = {
                money: Number(item.planCost),
                years: item.planTime.substring(0, 4),
                year: moment(item.planTime),
                item: [
                  {
                    type: item.planContent + '@' + item.planContentSubject,
                    money: item.planCost,
                  },
                ],
              };
              costItem.push(items);
            }
          });
          setplannedInputList(costItem);
        }
      } else {
        setItemCostIsLooks(false);
      }
      if (data.itemPlan) {
        if (data.itemPlan.length > 0) {
          setItemPlanIsLook(true);
          let plantArr = [];
          data.itemPlan.forEach(items => {
            let param = {
              money: items.planIncome,
              year: items.planTime,
              item: [
                {
                  planTime: moment(items.planTime),
                  planIncome: items.planIncome,
                },
              ],
            };
            plantArr.push(param);
          });

          setPlannedRevenueInputList(plantArr);
        }
      } else {
        setItemPlanIsLook(false);
      }

      setvisibleModaledit(true);
    }
  };

  const optionsList = [
    '-',
    '立项部门',
    '项目名称',
    '项目编号',
    '项目状态',
    '项目类型',
    '项目级别',
    '收入分类',
    '项目把握度',
    '计划收入',
    '已确认收入',
    '计划成本',
    '计划起止时间',
    '协作部门',
    '项目经理',
  ];
  const initParmas = {
    page: 1,
    limit: 10,
    // isAsc: 'true',
  };

  const [loading, setLoading] = useState(true);
  const [parmas, setParmas] = useState(initParmas);

  // 点击按钮查询
  const [searchParmas, setSearchParmas] = useState({
    itemName: '',
    itemNum: '',
    year: getThisYear(),
  });

  const showProcess = async item => {
    const { data, code } = await getItemTrial(item.id);
    if (code === 200) {
      const nodes = [];
      const edges = [];
      data[0].TrialInfo.forEach(item => {
        nodes.push({
          id: String(item.trial_id),
          label: String(item.name),
          status: String(item.is_status),
          trace: item.trace,
          update_time: item.update_time,
          username: item.username,
          ShUserName: item.ShUserName,
        });
      });
      data[1].TrialCom.forEach(item => {
        edges.push({
          source: String(item.trial_id),
          target: String(item.lower_id),
        });
      });
      setProcessData({ nodes: nodes, edges: edges });
      setShowProcedureModal(true);
    }
  };

  // 点击按钮查询
  const [searchParmasBind, setSearchParmasBind] = useState({
    itemName: '',
    itemNum: '',
    costEnd: null,
    costStart: null,
    deptName: [],
    endTime: '',
    incomeEnd: '',
    incomeStart: null,
    standState: null,
    startTime: null,
    state: null,
    type: [],
    year: getThisYear(),
    projectClient: '',
  });

  const operation = {
    title: '操作',
    key: 'operation',
    align: 'center',
    fixed: 'right',
    width: 200,
    render: (text, record) => {
      return (
        <div className={styles.btnStyles}>
          {record.state == 0 ? (
            <>
              {buttonList.includes('admin') && (
                <span
                  onClick={() => datalLook(record)}
                  className={styles.linkStyleYes}
                >
                  查看
                </span>
              )}
              {(buttonList.includes('/ready/edit') ||
                buttonList.includes('admin')) && (
                <span
                  onClick={() => datalEdit(record)}
                  className={styles.linkStyleYes}
                >
                  编辑
                </span>
              )}

              {(buttonList.includes('/ready/del') ||
                buttonList.includes('admin')) && (
                <span
                  className={styles.linkStyleYes}
                  onClick={() => dataDel(record)}
                >
                  删除
                </span>
              )}
              {(buttonList.includes('/ready/create') ||
                buttonList.includes('admin')) && (
                <Link
                  to={{
                    pathname: '/createReadyProject',
                    query: {
                      id: record.id,
                    },
                  }}
                >
                  立项
                </Link>
              )}
            </>
          ) : record.state == 2 ? (
            <>
              {(buttonList.includes('/projectList/track') ||
                buttonList.includes('admin')) && (
                <Link
                  to={{
                    pathname: '/projectList/track',
                    query: {
                      itemNo: record.itemNum,
                      id: record.id,
                    },
                  }}
                >
                  跟踪
                </Link>
              )}
            </>
          ) : record.state == 1 ? (
            <>
              <span
                onClick={() => showProcess(record)}
                className={styles.linkStyleYes}
              >
                流程
              </span>
              {buttonList.includes('admin') && (
                <span
                  onClick={() => datalLook(record)}
                  className={styles.linkStyleYes}
                >
                  查看
                </span>
              )}
              {buttonList.includes('admin') && (
                <span
                  className={styles.linkStyleYes}
                  onClick={() => dataRevoke(record)}
                >
                  撤销
                </span>
              )}
            </>
          ) : (
            <></>
          )}
        </div>
      );
    },
  };

  const gerlist = async value => {
    const resp = await getDynamicSearchInfo(value);
    if (resp.code === 200) {
      setLoading(false);
      setKeys([]);
      setData(resp.data.records);
      setDataTotal(resp.data.total);
    }
  };

  useEffect(() => {
    setKeys([]);
    setLoading(true);
    gerlist({ ...parmas, ...searchParmas });
  }, [parmas]);

  // 获取表列的所有种类
  const getTotalDorpList = async () => {
    const { data, code } = await getSearchListInfo();
    if (code === 200) {
      let revenueTypeList = [];
      data.forEach(item => {
        if (item.type === 'in_come_type') {
          revenueTypeList.push(item);
        }
      });
      setRevenueTypeList([...revenueTypeList]);
    }
  };

  useEffect(() => {
    getTotalDorpList();
    getDeptListInfos();
  }, []);

  const onChangePageNumber = (value, size) => {
    setParmas({ ...parmas, page: value, limit: size });
  };

  //必须等有list才能显示columns
  useEffect(() => {
    setColumns(totalColumns);
  }, [dorwnList]);

  const {
    item_level,
    item_type,
    in_come_type,
    item_stage_type,
    item_state,
    item_grasp,
    dept_name,
  } = dorwnList;

  // 搜索icon
  const handleGetIcon = filtered => {
    return (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    );
  };
  //  表列所有下拉框
  const radioGrouop = (key, list) => {
    return (
      <div>
        <Radio.Group
          style={{ padding: '5px 10px' }}
          onChange={handleSelectVal.bind(this, key)}
        >
          <Radio
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
            value=""
          >
            所有
          </Radio>
          {list
            ? list.map(item => {
                return (
                  <Radio
                    key={item.value}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                    value={item.value}
                  >
                    {item.label}
                  </Radio>
                );
              })
            : null}
        </Radio.Group>
      </div>
    );
  };
  //   表列下拉框改变值
  const handleSelectVal = (key, e) => {
    setParmas(parmas => {
      return { ...parmas, [key]: e.target.value };
    });
  };
  //动态表头
  const totalColumns = [
    {
      title: '年度',
      ellipsis: true,
      width: 80,
      fixed: 'left',
      dataIndex: 'standYear',
      key: 'standYear',
      // render: createTime => {
      //   return <span>{createTime.props.children.substring(0, 4)}</span>;
      // },
    },
    {
      title: '项目客户',
      ellipsis: true,
      width: 100,
      fixed: 'left',
      dataIndex: 'projectClient',
      key: 'projectClient',
    },
    {
      title: '项目名称',
      ellipsis: true,
      width: 100,
      fixed: 'left',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '项目编号',
      dataIndex: 'itemNum',
      ellipsis: true,
      key: '2',
      width: 140,
      align: 'left',
      render: itemNum => (
        <Tooltip placement="topLeft" title={itemNum}>
          {itemNum}
        </Tooltip>
      ),
    },
    {
      title: '立项部门',
      align: 'left',
      dataIndex: 'deptName',
      ellipsis: true,
      key: '3',
      width: 100,
      key: 'deptName',
    },
    {
      title: '协作部门',
      dataIndex: 'coDept',
      key: 'coDept',
      align: 'left',
      width: 100,
      ellipsis: {
        showTitle: false,
      },
      render: coDept => (
        <Tooltip placement="topLeft" title={coDept}>
          {coDept}
        </Tooltip>
      ),
    },

    {
      title: '项目类型',
      dataIndex: 'type',
      ellipsis: true,
      key: '5',
      width: 100,
      align: 'left',
    },

    {
      title: '计划收入(w)',
      dataIndex: 'itemPlanIncome',
      sorter: (a, b) => a.itemPlanIncome - b.itemPlanIncome,
      key: '9',
      align: 'left',
      render: itemPlanIncome => <span>{itemPlanIncome}W</span>,
      width: 100,
    },

    {
      title: '计划成本(w)',
      dataIndex: 'itemCostBudgeting',
      key: '12',
      align: 'left',
      sorter: (a, b) => a.itemCostBudgeting - b.itemCostBudgeting,
      render: itemCostBudgeting => <span>{itemCostBudgeting}W</span>,
      width: 100,
    },
    {
      title: '预算状态',
      dataIndex: 'standState',
      key: '13',
      align: 'left',
      width: 80,
      render: standState =>
        standState == 0 ? (
          <span>计划内</span>
        ) : standState == 1 ? (
          <span>计划外</span>
        ) : (
          <span>-</span>
        ),
    },
    {
      title: '项目状态',
      dataIndex: 'state',
      key: '13',
      align: 'left',
      width: 80,
      render: state =>
        state == 2 ? (
          <span>已立项</span>
        ) : state == 1 ? (
          <span>立项中</span>
        ) : state == 0 ? (
          <span>未立项</span>
        ) : (
          <span>-</span>
        ),
    },

    {
      title: '计划开始时间',
      dataIndex: 'startTime',
      key: '13',
      width: 100,
      align: 'left',
    },
    {
      title: '计划结束时间',
      dataIndex: 'endTime',
      key: '13',
      width: 100,
      align: 'left',
    },
    {
      title: '是否跨年',
      key: '13',
      width: 80,
      align: 'center',
      render: render => isSameYear(render.startTime, render.endTime),
    },

    operation,
  ];

  const isSameYear = (startTime, endTime) => {
    let isYear = '-';
    if (startTime && endTime) {
      if (startTime.substring(0, 4) === endTime.substring(0, 4)) {
        isYear = '否';
      } else {
        isYear = '是';
      }
    }
    return isYear;
  };

  const isSameYears = (startTime, endTime) => {
    let isYear = '-';
    if (startTime && endTime) {
      if (startTime.substring(0, 4) === endTime.substring(0, 4)) {
        isYear = '否';
      } else {
        isYear = '是';
      }
    }
    return isYear;
  };

  const rowSelection = {
    // 设置key值单选框才能被选中
    onChange: (selectedRowKeys, selectedRows) => {
      const idArr = [];
      selectedRows.map(item => {
        idArr.push(item.id);
      });
      setRecordParams(recordParams => {
        return { ...recordParams, data: idArr };
      });
      setKeys(selectedRowKeys);
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      const idList = [];
      const currentList = selected ? selectedRows : changeRows;
      currentList.map(item => {
        idList.push(item.id);
      });
      setRecordParams(setRecordParams => {
        return { ...setRecordParams, data: idList, selected };
      });
    },
    selectedRowKeys: keys,
  };

  /**
   * 搜索值变化
   */
  const handleSearchParams = values => {
    let { key, value } = values;
    if (key === 'type' || key === 'deptName') {
      console.log('deptName', values);
      setSearchParmas({ ...searchParmas, [key]: value.join(',') });
    } else {
      setSearchParmas({ ...searchParmas, [key]: value });
    }
    setSearchParmasBind({ ...searchParmasBind, [key]: value });
  };

  /**
   * 搜索值变化
   */
  const handleSearchParamsNew = values => {
    let { key, value } = values;
    setSearchParmasNew({ ...searchParmasNew, [key]: value });
  };

  //改变每页显示条数
  const pageSizeChange = value => {
    setParmas({ ...parmas, limit: value });
  };
  const { limit, page, type, isFollow } = parmas;

  const [projectNameList, setProjectNameList] = useState([]);

  const changeOpen = value => {
    if (value == 'close') {
      setOpenStatus(true);
    } else {
      setOpenStatus(false);
    }
  };

  return (
    <div className="content">
      <div className={styles.card}>
        <div className={styles.searchInput}>
          {openStatus && (
            <>
              <div className={styles.searchBox}>
                <div className={styles.searchItems}>
                  <div className={styles.searchText}>项目名称</div>
                  <div className={styles.searchInput}>
                    <Input
                      value={searchParmasBind.itemName}
                      onChange={e =>
                        handleSearchParams({
                          key: 'itemName',
                          value: e.target.value,
                        })
                      }
                      placeholder="请输入"
                      className={styles.selectSerch}
                    ></Input>
                  </div>
                </div>
                <div className={styles.searchItems}>
                  <div className={styles.searchText}>项目编号</div>
                  <div className={styles.searchInput}>
                    <Input
                      value={searchParmasBind.itemNum}
                      onChange={e =>
                        handleSearchParams({
                          key: 'itemNum',
                          value: e.target.value,
                        })
                      }
                      className={styles.selectSerch}
                      placeholder="请输入"
                    ></Input>
                  </div>
                </div>
                <div className={styles.searchItems}>
                  <Button onClick={handleOnSearchTJ} type="primary">
                    查询
                  </Button>
                  <span
                    onClick={() => changeOpen('open')}
                    className={styles.openModal}
                  >
                    展开
                    <svg
                      t="1629343479823"
                      class="icon"
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      p-id="1190"
                      width="16"
                      height="16"
                    >
                      <path
                        d="M512 672c-6.4 0-14.933333-2.133333-21.333333-6.4L157.866667 392.533333c-10.666667-8.533333-10.666667-23.466667 0-34.133333 10.666667-8.533333 29.866667-8.533333 40.533333 0l313.6 256 313.6-256c10.666667-8.533333 29.866667-8.533333 40.533333 0s10.666667 23.466667 0 34.133333L533.333333 665.6c-6.4 4.266667-14.933333 6.4-21.333333 6.4z"
                        fill="#1296db"
                        p-id="1191"
                      ></path>
                    </svg>
                  </span>
                </div>
              </div>
            </>
          )}
          {!openStatus && (
            <>
              <div className={styles.searchBox}>
                <div className={styles.searchItems}>
                  <div className={styles.searchText}>项目名称</div>
                  <div className={styles.searchInput}>
                    <Input
                      onChange={e =>
                        handleSearchParams({
                          key: 'itemName',
                          value: e.target.value,
                        })
                      }
                      value={searchParmasBind.itemName}
                      placeholder="请输入"
                      className={styles.selectSerch}
                    ></Input>
                  </div>
                </div>
                <div className={styles.searchItems}>
                  <div className={styles.searchText}>项目编号</div>
                  <div className={styles.searchInput}>
                    <Input
                      onChange={e =>
                        handleSearchParams({
                          key: 'itemNum',
                          value: e.target.value,
                        })
                      }
                      value={searchParmasBind.itemNum}
                      className={styles.selectSerch}
                      placeholder="请输入"
                    ></Input>
                  </div>
                </div>
                <div className={styles.searchItems}>
                  <div className={styles.searchText}>项目客户</div>
                  <div className={styles.searchInput}>
                    <Input
                      onChange={e =>
                        handleSearchParams({
                          key: 'projectClient',
                          value: e.target.value,
                        })
                      }
                      value={searchParmasBind.projectClient}
                      placeholder="请输入"
                      className={styles.selectSerch}
                    ></Input>
                  </div>
                </div>

                <div className={styles.searchItems}>
                  <div className={styles.searchText}>年份</div>
                  <div className={styles.searchInput}>
                    <DatePicker
                      className={styles.selectSerch}
                      allowClear
                      picker="year"
                      value={
                        (searchParmasBind.year &&
                          moment(searchParmasBind.year)) ||
                        ''
                      }
                      onChange={(date, dateString) =>
                        handleSearchParams({ key: 'year', value: dateString })
                      }
                      suffixIcon={<ClockCircleOutlined />}
                    />
                  </div>
                </div>
              </div>
              <div className={styles.searchBox}>
                <div className={styles.searchItems}>
                  <div className={styles.searchText}>预算状态</div>
                  <div className={styles.searchInput}>
                    <Select
                      showSearch
                      className={styles.selectSerch}
                      allowClear={true}
                      placeholder="请选择"
                      value={searchParmasBind.standState}
                      onChange={value =>
                        handleSearchParams({ key: 'standState', value: value })
                      }
                      filterOption={(input, option) =>
                        option.children
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      <Option value={1}>计划外</Option>
                      <Option value={0}>计划内</Option>
                    </Select>
                  </div>
                </div>
                <div className={styles.searchItems}>
                  <div className={styles.searchText}>项目状态</div>
                  <div className={styles.searchInput}>
                    <Select
                      showSearch
                      className={styles.selectSerch}
                      allowClear={true}
                      placeholder="请选择"
                      onChange={value =>
                        handleSearchParams({ key: 'state', value: value })
                      }
                      value={searchParmasBind.state}
                      filterOption={(input, option) =>
                        option.children
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      <Option value={2}>已立项</Option>
                      <Option value={1}>立项中</Option>
                      <Option value={0}>未立项</Option>
                    </Select>
                  </div>
                </div>

                <div className={styles.searchItems}>
                  <div className={styles.searchText}>计划收入</div>
                  <div className={styles.searchInput}>
                    <InputNumber
                      className={styles.InputNumberStyle}
                      min={0}
                      formatter={function(value) {
                        let values = '';
                        if (value == 0 || !value) {
                          values = value;
                        } else {
                          values = `${value}W`;
                        }
                        return values;
                      }}
                      precision={2}
                      parser={value => value.replace('W', '')}
                      value={searchParmasBind.incomeStart}
                      onChange={value =>
                        handleSearchParams({
                          value,
                          key: 'incomeStart',
                        })
                      }
                    />
                    <span>-</span>
                    <InputNumber
                      className={styles.InputNumberStyle}
                      min={0}
                      formatter={function(value) {
                        let values = '';
                        if (value == 0 || !value) {
                          values = value;
                        } else {
                          values = `${value}W`;
                        }
                        return values;
                      }}
                      precision={2}
                      parser={value => value.replace('W', '')}
                      value={searchParmasBind.incomeEnd}
                      onChange={value =>
                        handleSearchParams({
                          value,
                          key: 'incomeEnd',
                        })
                      }
                    />
                  </div>
                </div>
                <div className={styles.searchItems}>
                  <div className={styles.searchText}>计划成本</div>
                  <div className={styles.searchInput}>
                    <InputNumber
                      className={styles.InputNumberStyle}
                      min={0}
                      value={searchParmasBind.costStart}
                      formatter={function(value) {
                        let values = '';
                        if (value == 0 || !value) {
                          values = value;
                        } else {
                          values = `${value}W`;
                        }
                        return values;
                      }}
                      precision={2}
                      parser={value => value.replace('W', '')}
                      onChange={value =>
                        handleSearchParams({
                          value,
                          key: 'costStart',
                        })
                      }
                    />
                    <span>-</span>
                    <InputNumber
                      className={styles.InputNumberStyle}
                      min={0}
                      precision={2}
                      value={searchParmasBind.costEnd}
                      formatter={function(value) {
                        let values = '';
                        if (value == 0 || !value) {
                          values = value;
                        } else {
                          values = `${value}W`;
                        }
                        return values;
                      }}
                      parser={value => value.replace('W', '')}
                      onChange={value =>
                        handleSearchParams({
                          value,
                          key: 'costEnd',
                        })
                      }
                    />
                  </div>
                </div>
              </div>

              <div className={styles.searchBox}>
                <div className={styles.searchItems}>
                  <div className={styles.searchText}>项目类型</div>
                  <div className={styles.searchInput}>
                    <Select
                      showSearch
                      mode="multiple"
                      value={searchParmasBind.type}
                      maxTagCount={1}
                      className={styles.selectSerch}
                      allowClear={true}
                      placeholder="请选择"
                      onChange={value =>
                        handleSearchParams({ key: 'type', value: value })
                      }
                      filterOption={(input, option) =>
                        option.children
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      {revenueTypeList.length > 0 &&
                        revenueTypeList.map((item, index) => (
                          <Option value={item.code} key={item.code}>
                            {item.name}
                          </Option>
                        ))}
                    </Select>
                  </div>
                </div>
                <div className={styles.searchItems}>
                  <div className={styles.searchText}>立项部门</div>
                  <div className={styles.searchInput}>
                    <Select
                      showSearch
                      maxTagCount={1}
                      mode="multiple"
                      className={styles.selectSerch}
                      allowClear={true}
                      placeholder="请选择"
                      value={searchParmasBind.deptName}
                      onChange={value =>
                        handleSearchParams({ key: 'deptName', value })
                      }
                      filterOption={(input, option) =>
                        option.children
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      {deptList.map(item => (
                        <Option value={item.deptFullName} key={item.id}>
                          {item.deptFullName}
                        </Option>
                      ))}
                    </Select>
                  </div>
                </div>

                <div className={styles.searchItems}>
                  <div className={styles.searchText}>项目开始时间</div>
                  <div className={styles.searchInput}>
                    <DatePicker
                      allowClear
                      className={styles.selectSerch}
                      value={
                        (searchParmasBind.startTime &&
                          moment(searchParmasBind.startTime)) ||
                        ''
                      }
                      onChange={(date, dateString) =>
                        handleSearchParams({
                          value: dateString,
                          key: 'startTime',
                        })
                      }
                      format={dateFormat}
                      suffixIcon={<ClockCircleOutlined />}
                      disabledDate={current => {
                        return (
                          searchParmas.endTime &&
                          current > moment(searchParmas.endTime)
                        );
                      }}
                    />
                  </div>
                </div>

                <div className={styles.searchItems}>
                  <div className={styles.searchText}>项目结束时间</div>
                  <div className={styles.searchInput}>
                    <DatePicker
                      className={styles.selectSerch}
                      allowClear
                      value={
                        (searchParmasBind.endTime &&
                          moment(searchParmasBind.endTime)) ||
                        ''
                      }
                      onChange={(date, dateString) =>
                        handleSearchParams({
                          value: dateString,
                          key: 'endTime',
                        })
                      }
                      format={dateFormat}
                      suffixIcon={<ClockCircleOutlined />}
                      disabledDate={current => {
                        return (
                          searchParmas.startTime &&
                          current < moment(searchParmas.startTime)
                        );
                      }}
                    />
                  </div>
                </div>
              </div>

              <div className={styles.searchBox}>
                <div className={styles.searchItems}>
                  <div className={styles.searchText}>是否跨年</div>
                  <div className={styles.searchInput}>
                    <Select
                      showSearch
                      className={styles.selectSerch}
                      allowClear={true}
                      placeholder="请选择"
                      value={searchParmasBind.tooYear}
                      onChange={value =>
                        handleSearchParams({ key: 'tooYear', value })
                      }
                      filterOption={(input, option) =>
                        option.children
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      <Option value={'是'}>是</Option>
                      <Option value={'否'}>否</Option>
                    </Select>
                  </div>
                </div>

                <div className={styles.searchItems}></div>
                <div className={styles.searchItems}></div>
                <div className={styles.searchItems}>
                  <Button onClick={btnReset}>重置</Button>
                  <Button onClick={btnSerch} type="primary">
                    查询
                  </Button>
                  <span
                    onClick={() => changeOpen('close')}
                    className={styles.openModal}
                  >
                    收起
                    <svg
                      t="1629451343926"
                      class="icon"
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      p-id="2617"
                      width="16"
                      height="16"
                    >
                      <path
                        d="M818.393225 712.230324c12.824073 14.09502 34.658358 15.126512 48.752354 2.303462 14.09502-12.843516 15.126512-34.678824 2.302439-48.752354l-332.676845-364.835266c-12.844539-14.114462-34.659381-15.127536-48.753377-2.302439-0.815575 0.733711-1.588171 1.486864-2.302439 2.302439l-0.080841 0.078795-0.13917 0.13917L153.018046 665.780409c-12.824073 14.074553-11.791557 35.909861 2.302439 48.752354 14.09502 12.824073 35.930327 11.792581 48.753377-2.303462l307.168891-336.845795 307.149449 336.845795L818.393225 712.230324 818.393225 712.230324z"
                        p-id="2618"
                        fill="#1296db"
                      ></path>
                    </svg>
                  </span>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      <div className={styles.card}>
        <ProTable
          columns={columns}
          dataSource={data}
          scroll={{ x: 14 * 110 }}
          pagination={false}
          loading={loading}
          className={styles.anTdTable}
          search={false}
          filters={true}
          options={{
            fullScreen: false,
            reload: false,
            setting: false,
            density: false,
          }}
          toolBarRender={() => [
            <div className={styles.focus}>
              <Button
                onClick={() => downloadExcel()}
                icon={<DownloadOutlined />}
                className={isFollow === '0' ? styles.active : null}
                style={{ marginRight: '5px' }}
              >
                下载模板
              </Button>

              <Upload
                showUploadList={false}
                {...uploadProps}
                withCredentials={true}
              >
                <Button
                  type="file"
                  style={{ marginRight: '5px' }}
                  className={isFollow === '0' ? styles.active : null}
                >
                  导入
                </Button>
              </Upload>

              <Button
                className={isFollow === '0' ? styles.active : null}
                style={{ marginRight: '5px' }}
                onClick={() => downloadCustomAll()}
              >
                导出
              </Button>

              {(buttonList.includes('/ready/add') ||
                buttonList.includes('admin')) && (
                <Button
                  className={isFollow === '0' ? styles.active : null}
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={downloadCustom}
                >
                  新增
                </Button>
              )}
            </div>,
          ]}
        />
        <div className={styles.splitPigination}>
          <div>
            <Select
              defaultValue="10"
              style={{ width: 150 }}
              className={styles.selects}
              onChange={pageSizeChange}
            >
              <Option value="10">显示结果：10条</Option>
              <Option value="20">显示结果：20条</Option>
              <Option value="50">显示结果：50条</Option>
            </Select>
            <span className={styles.total}>共{dataTotal}条</span>
          </div>
          <Pagination
            total={dataTotal || 0}
            pageSize={parmas.limit}
            showSizeChanger={false}
            current={page}
            key={67}
            onChange={onChangePageNumber}
          />
        </div>
      </div>
      <Modal
        width={formParam.width}
        title={formParam.title}
        visible={visibleModal}
        onOk={handleOk}
        maskClosable={false}
        onCancel={handleCancel}
        footer={null}
      >
        <Form
          form={form}
          name="basic"
          onFinish={onFinish}
          labelCol={{ span: 10 }}
          wrapperCol={{ span: 14 }}
        >
          <div className={styles.formBOXS}>
            <div className={styles.items}>
              <div className={styles.itemsTop}>
                项目基本信息
                <span
                  onClick={() =>
                    opens({
                      type: 'information',
                    })
                  }
                  className={styles.opensIcons}
                >
                  {addIsLook ? <DownOutlined /> : <UpOutlined />}
                </span>
              </div>

              {addIsLook ? (
                <div className={styles.itemsMain}>
                  <Row gutter={32}>
                    <Col span={24}>
                      <Form.Item
                        labelCol={{ span: 3 }}
                        wrapperCol={{ span: 21 }}
                        label="项目计划时间"
                        name="planTimes"
                        rules={[
                          {
                            required: true,
                          },
                        ]}
                      >
                        <RangePicker
                          allowClear
                          suffixIcon={<ClockCircleOutlined />}
                          format="YYYY/MM/DD"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="年度"
                        name="year"
                        rules={[
                          {
                            required: true,
                            message: '请选择年度',
                          },
                        ]}
                      >
                        <DatePicker
                          allowClear
                          style={{ width: '100%' }}
                          picker="year"
                          suffixIcon={<ClockCircleOutlined />}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="项目名称"
                        name="name"
                        rules={[
                          {
                            required: true,
                          },
                        ]}
                      >
                        <Input placeholder="请输入项目名称" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="项目客户" name="projectClient">
                        <Input placeholder="请输入客户名称" />
                      </Form.Item>
                    </Col>

                    <Col span={8}>
                      <Form.Item
                        label="立项部门"
                        name="deptFull"
                        rules={[
                          {
                            required: true,
                            message: '请选择立项部门',
                          },
                        ]}
                      >
                        <Select
                          showSearch
                          allowClear
                          defaultActiveFirstOption={false}
                          showArrow={false}
                          placeholder="选择立项部门"
                          filterOption={false}
                          onChange={value => {
                            nameChange(value);
                          }}
                          notFoundContent={null}
                          filterOption={(input, option) =>
                            option.children
                              .toLowerCase()
                              .indexOf(input.toLowerCase()) >= 0
                          }
                        >
                          {deptList.map(item => (
                            <Option
                              value={`${item.deptFullName}@${item.id}`}
                              key={`${item.deptFullName}@${item.id}`}
                            >
                              {item.deptFullName}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>

                    <Col span={8}>
                      <Form.Item
                        label="协作部门"
                        allowClear
                        name="departmentCooperation"
                      >
                        <Select
                          allowClear
                          showSearch
                          defaultActiveFirstOption={false}
                          placeholder="请选择协作部门"
                          showArrow={false}
                          filterOption={false}
                          notFoundContent={null}
                          filterOption={(input, option) =>
                            option.children
                              .toLowerCase()
                              .indexOf(input.toLowerCase()) >= 0
                          }
                        >
                          {deptList.map(item => (
                            <Option
                              value={`${item.deptFullName}@${item.id}`}
                              key={`${item.deptFullName}@${item.id}`}
                            >
                              {item.deptFullName}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>

                    <Col span={8}>
                      <Form.Item
                        label="项目类型"
                        name="type"
                        rules={[
                          {
                            required: true,
                          },
                        ]}
                      >
                        <Select
                          showArrow={false}
                          filterOption={false}
                          allowClear
                        >
                          {revenueTypeList.length > 0 &&
                            revenueTypeList.map((item, index) => (
                              <Option value={item.code} key={item.code}>
                                {item.name}
                              </Option>
                            ))}
                        </Select>
                      </Form.Item>
                    </Col>

                    <Col span={24}>
                      <Form.Item
                        label="项目备注"
                        name="projectOverview"
                        allowClear
                        labelCol={{ span: 3 }}
                        wrapperCol={{ span: 21 }}
                        rules={[
                          {
                            required: true,
                          },
                        ]}
                      >
                        <TextArea
                          placeholder="请填写项目背景及立项目的"
                          rows={2}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              ) : (
                <div></div>
              )}
            </div>
            <div className={styles.items}>
              <div className={styles.itemsTop}>
                项目财务信息
                <span
                  onClick={() =>
                    opens({
                      type: 'finance',
                    })
                  }
                  className={styles.opensIcons}
                >
                  {addIsLooks ? <DownOutlined /> : <UpOutlined />}
                </span>
              </div>
              {addIsLooks ? (
                <div className={styles.itemsMain}>
                  <div className={styles.itemsMainBox}>
                    <div className={styles.itemsMainBoxTop}>
                      <div className={styles.itemsMainBoxTitles}>计划收入</div>
                      <div className={styles.itemsMainBoxNumber}>
                        收入总额:{allMOney}万
                      </div>
                    </div>
                    <div className={styles.itemsMainBoxMain}>
                      <div className={styles.itemsMainLarge}>
                        {plannedRevenueInputList &&
                          plannedRevenueInputList.length > 0 &&
                          plannedRevenueInputList.map((item, index) => (
                            <>
                              {item.item.map((items, indexs) => (
                                <div className={styles.addItems}>
                                  {plannedRevenueInputList.length ==
                                  index + 1 ? (
                                    <span
                                      onClick={() =>
                                        addInputItem({
                                          type: 'inComeType',
                                          index,
                                        })
                                      }
                                      className={styles.icons}
                                    >
                                      +
                                    </span>
                                  ) : (
                                    <span className={styles.iconsNo}>+</span>
                                  )}
                                  <div>
                                    <DatePicker
                                      suffixIcon={<ClockCircleOutlined />}
                                      placeholder="选择时间"
                                      format="YYYY/MM/DD"
                                      value={
                                        (items.planTime &&
                                          moment(items.planTime)) ||
                                        ''
                                      }
                                      onChange={(date, deteString) =>
                                        datePickerChange({
                                          date,
                                          deteString,
                                          index,
                                          indexs,
                                          key: 'plannedRevenueInputList',
                                        })
                                      }
                                      className={styles.selects}
                                    />
                                  </div>

                                  <InputNumber
                                    min={0}
                                    className={styles.selects}
                                    value={items.planIncome}
                                    precision={2}
                                    placeholder="输入金额"
                                    disabled={type === 'look'}
                                    onChange={value =>
                                      plannedRevenueInputListMoneyChange({
                                        value: value,
                                        index,
                                        indexs,
                                      })
                                    }
                                    style={{ width: '100%' }}
                                  />

                                  <div>
                                    {index !== 0 ? (
                                      <Popconfirm
                                        title="是否删除？"
                                        okText="是"
                                        cancelText="否"
                                        onConfirm={() =>
                                          deleteItem({
                                            index,
                                            indexs,
                                            type: 'income',
                                          })
                                        }
                                      >
                                        <span className={styles.icons}>x</span>
                                      </Popconfirm>
                                    ) : (
                                      <span className={styles.icons}></span>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </>
                          ))}
                      </div>
                      <div className={styles.itemsMainSmall}>
                        {yearMoneyList &&
                          yearMoneyList.length > 0 &&
                          yearMoneyList.map(item => {
                            return (
                              item.year &&
                              item.money !== 0 && (
                                <div className={styles.yearItems}>
                                  <span>{item.year}年：</span>
                                  {Number(item.money).toFixed(2)}万
                                </div>
                              )
                            );
                          })}
                      </div>
                    </div>
                  </div>

                  <div className={styles.itemsMainBox}>
                    <div className={styles.itemsMainBoxTop}>
                      <div className={styles.itemsMainBoxTitles}>计划成本</div>
                      <div className={styles.itemsMainBoxNumber}>
                        预算总额:{allMOneys}万
                      </div>
                    </div>

                    <div className={styles.itemsMainBoxContent}>
                      {plannedInputList.length > 0 &&
                        plannedInputList.map((item, index) => (
                          <>
                            <div className={styles.itemsMainBoxContentItem}>
                              {item.years != '' && item.money > 0 && (
                                <div
                                  className={styles.itemsMainBoxContentItemTop}
                                >
                                  {item.years}年:{strip(item.money)}万
                                </div>
                              )}
                              <div
                                className={styles.itemsMainBoxContentItemMain}
                              >
                                <div className={styles.itemsfinanceSmall}>
                                  {index > 0 ? (
                                    <Popconfirm
                                      title="是否删除？"
                                      okText="是"
                                      cancelText="否"
                                      onConfirm={() =>
                                        deleteItem({
                                          type: 'itemAll',
                                          index,
                                          indexs: -1,
                                        })
                                      }
                                    >
                                      <span className={styles.itemsfinanIcons}>
                                        x
                                      </span>
                                    </Popconfirm>
                                  ) : (
                                    <span
                                      className={styles.itemsfinanIcons}
                                    ></span>
                                  )}
                                  <span
                                    onClick={() =>
                                      addInputItem({
                                        type: 'allOut',
                                        index,
                                      })
                                    }
                                    className={styles.itemsfinanIcons}
                                  >
                                    +
                                  </span>
                                  <DatePicker
                                    suffixIcon={<ClockCircleOutlined />}
                                    value={
                                      (item.year && moment(item.year)) || ''
                                    }
                                    className={styles.selects}
                                    onChange={(date, deteString) =>
                                      datePickerChange({
                                        date,
                                        deteString,
                                        index,
                                        indexs: -1,
                                        key: 'deptInputList',
                                      })
                                    }
                                    picker="year"
                                  />
                                </div>
                                <div className={styles.itemsfinanceLarge}>
                                  {item.item.map((items, indexs) => (
                                    <div className={styles.itemsStyles}>
                                      <Select
                                        style={{ width: 100 }}
                                        className={styles.selectsNew}
                                        placeholder="费用类别"
                                        showSearch
                                        value={items.type}
                                        onChange={value =>
                                          itemCostTypeChange({
                                            value,
                                            index,
                                            indexs,
                                          })
                                        }
                                      >
                                        {inComeTypeList.length > 0 &&
                                          inComeTypeList.map((item, index) => (
                                            <Option
                                              value={`${item.name}@${item.code}`}
                                              disabled={type === 'look'}
                                              key={index}
                                            >
                                              {item.name}
                                            </Option>
                                          ))}
                                      </Select>

                                      <InputNumber
                                        min={0}
                                        className={styles.selectsNew}
                                        value={items.money}
                                        placeholder="输入金额"
                                        precision={2}
                                        // type='number'
                                        onChange={value =>
                                          deptInputListMoneyChange({
                                            value: value,
                                            index,
                                            indexs,
                                          })
                                        }
                                        suffix="万元"
                                      />

                                      {indexs === item.item.length - 1 ? (
                                        <span
                                          className={styles.iconsYesS}
                                          onClick={() =>
                                            addInputItem({
                                              type: 'Out',
                                              index,
                                            })
                                          }
                                        >
                                          +
                                        </span>
                                      ) : (
                                        <span> </span>
                                      )}
                                      {indexs > 0 ? (
                                        <Popconfirm
                                          title="是否删除？"
                                          okText="是"
                                          cancelText="否"
                                          onConfirm={() =>
                                            deleteItem({
                                              type: 'items',
                                              index,
                                              indexs,
                                            })
                                          }
                                        >
                                          <span
                                            className={styles.itemsfinanIcons}
                                          >
                                            x
                                          </span>
                                        </Popconfirm>
                                      ) : (
                                        <span> </span>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </>
                        ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div></div>
              )}
            </div>
          </div>
          <div className={styles.formFooter}>
            <Button htmlType="button" onClick={handleCancel}>
              取消
            </Button>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </div>
        </Form>
      </Modal>

      <Modal
        width="900px"
        title="数据详情"
        visible={visibleModaledit}
        maskClosable={false}
        onCancel={handleCancellook}
        footer={null}
      >
        <div className={styles.formBOXS}>
          <div className={styles.items}>
            <div className={styles.itemsTop}>
              项目基本信息
              <span
                onClick={() =>
                  opens({
                    type: 'information',
                  })
                }
                className={styles.opensIcons}
              >
                {addIsLook ? <DownOutlined /> : <UpOutlined />}
              </span>
            </div>

            {addIsLook ? (
              <div className={styles.itemsMain}>
                <Row gutter={64} className={styles.itemsRow}>
                  <Col span={8}>
                    <span className={styles.editLable}>年度:</span>
                    <span className={styles.editLables}>
                      {editDate.standYear ? editDate.standYear + '年' : '无'}
                    </span>
                  </Col>

                  <Col span={8}>
                    <span className={styles.editLable}>项目名称:</span>
                    <span className={styles.editLables}>{editDate.name}</span>
                  </Col>
                  <Col span={8}>
                    <span className={styles.editLable}>项目客户:</span>
                    <span className={styles.editLables}>
                      {editDate.projectClient ? editDate.projectClient : '无'}
                    </span>
                  </Col>

                  <Col span={8}>
                    <span className={styles.editLable}>立项部门:</span>
                    <span className={styles.editLables}>
                      {editDate.dept ? editDate.dept : '无'}
                    </span>
                  </Col>

                  <Col span={8}>
                    <span className={styles.editLable}>协作部门:</span>
                    <span className={styles.editLables}>
                      {detailCoDept(editDate.cooperation)}
                    </span>
                  </Col>

                  <Col span={8}>
                    <span className={styles.editLable}>项目类型:</span>
                    <span className={styles.editLables}>
                      {editDate.inComeTypeName ? editDate.inComeTypeName : '无'}
                    </span>
                  </Col>

                  <Col span={8}>
                    <span className={styles.editLable}>预算状态:</span>
                    <span className={styles.editLables}>
                      {editDate.standState == 1
                        ? '计划外'
                        : editDate.standState == 0
                        ? '计划内'
                        : '无'}
                    </span>
                  </Col>

                  <Col span={8}>
                    <span className={styles.editLable}>项目状态:</span>
                    <span className={styles.editLables}>
                      {editDate.state ? editDate.state : '无'}
                    </span>
                  </Col>

                  <Col span={8}>
                    <span className={styles.editLable}>是否跨年:</span>
                    <span className={styles.editLables}>
                      {isSameYears(
                        moment(editDate.startTime).format('YYYY'),
                        moment(editDate.endTime).format('YYYY'),
                      )}
                    </span>
                  </Col>

                  <Col span={24}>
                    <span className={styles.editLable}>项目计划时间:</span>
                    <span className={styles.editLables}>
                      {moment(editDate.startTime).format('YYYY/MM/DD')}至
                      {moment(editDate.endTime).format('YYYY/MM/DD')}
                    </span>
                  </Col>
                  <Col span={24}>
                    <span className={styles.editLable}>项目备注:</span>
                    <span className={styles.editLables}>
                      {editDate.projectOverview
                        ? editDate.projectOverview
                        : '无'}
                    </span>
                  </Col>
                </Row>
              </div>
            ) : (
              <div></div>
            )}
          </div>
          <div className={styles.items}>
            <div className={styles.itemsTop}>
              项目财务信息
              <span
                onClick={() =>
                  opens({
                    type: 'finance',
                  })
                }
                className={styles.opensIcons}
              >
                {addIsLooks ? <DownOutlined /> : <UpOutlined />}
              </span>
            </div>
            {addIsLooks ? (
              <div className={styles.itemsMain}>
                <div className={styles.itemsMainBox}>
                  <div className={styles.itemsMainBoxTop}>
                    <div className={styles.itemsMainBoxTitles}>计划收入</div>
                    <div className={styles.itemsMainBoxNumber}>
                      收入总额:{allMOney}万
                    </div>
                  </div>
                  {itemPlanIsLook ? (
                    <div className={styles.itemsMainBoxMain}>
                      <div className={styles.itemsMainLarge}>
                        {plannedRevenueInputList &&
                          plannedRevenueInputList.length > 0 &&
                          plannedRevenueInputList.map((item, index) => (
                            <>
                              {item.item.map((items, indexs) => (
                                <div className={styles.addItemsLook}>
                                  <span>
                                    {moment(items.planTime).format('YYYY')}年:
                                  </span>
                                  <span className={styles.lookIcons}>
                                    {items.planIncome}
                                  </span>
                                  <div></div>
                                </div>
                              ))}
                            </>
                          ))}
                      </div>
                      <div className={styles.itemsMainSmall}>
                        {yearMoneyList &&
                          yearMoneyList.length > 0 &&
                          yearMoneyList.map(item => {
                            return (
                              item.year &&
                              item.money !== 0 && (
                                <div className={styles.yearItemslook}>
                                  <span>{item.year}年：</span>
                                  {Number(item.money).toFixed(2)}万
                                </div>
                              )
                            );
                          })}
                      </div>
                    </div>
                  ) : (
                    <div className={styles.itemsMainBoxMainNOdate}>无数据</div>
                  )}
                </div>

                <div className={styles.itemsMainBox}>
                  <div className={styles.itemsMainBoxTop}>
                    <div className={styles.itemsMainBoxTitles}>计划预算</div>
                    <div className={styles.itemsMainBoxNumber}>
                      预算总额:{allMOneys}万
                    </div>
                  </div>
                  {itemCostLooks ? (
                    <div className={styles.itemsMainBoxContentLook}>
                      {plannedInputList.length > 0 &&
                        plannedInputList.map((item, index) => (
                          <>
                            <div className={styles.itemsMainBoxContentItem}>
                              {item.years != '' && item.money > 0 && (
                                <div
                                  className={styles.itemsMainBoxContentItemTop}
                                >
                                  {item.years}年:{item.money}万
                                </div>
                              )}
                              <div
                                className={styles.itemsMainBoxContentItemMain}
                              >
                                <div className={styles.itemsfinanceSmall}>
                                  <span>
                                    {moment(item.year).format('YYYY')}年
                                  </span>
                                </div>
                                <div className={styles.itemsfinanceLarge}>
                                  {item.item.map((items, indexs) => (
                                    <div className={styles.itemsStyles}>
                                      <span>{items.type.split('@')[0]}:</span>
                                      <span>{items.money}万</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </>
                        ))}
                    </div>
                  ) : (
                    <div className={styles.itemsMainBoxMainNOdate}>无数据</div>
                  )}
                </div>
              </div>
            ) : (
              <div></div>
            )}
            <div className={styles.formFooter}>
              <Button onClick={handleCancellook}>关闭</Button>
              <Button onClick={gotoeditlook} type="primary">
                编辑
              </Button>
            </div>
          </div>
        </div>
      </Modal>

      <Modal
        // title="Basic Modal"
        visible={showProcedureModal}
        onOk={() => setShowProcedureModal(false)}
        onCancel={() => setShowProcedureModal(false)}
        width={1100}
      >
        <Procedure processData={processData} />
      </Modal>
    </div>
  );
};
