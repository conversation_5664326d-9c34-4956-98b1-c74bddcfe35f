import request from '@/utils/request';
import { BASE_URl } from '../../../utils/constant';

//获取list
export function getProjectListPage(params) {
  return request(`${BASE_URl}/projectIntendStand/getProjectIntendList`, {
    method: 'POST',
    data: params,
  });
}
//新增信息
export function addIntendStandItem(params) {
  return request(`${BASE_URl}/projectIntendStand/addIntendStandItem`, {
    method: 'POST',
    data: params,
  });
}

//费用类型下拉框信息
export function getInComeType() {
  return request(`${BASE_URl}/Finance/getInComeType`, {
    method: 'post',
  });
}

//获取姓名下拉框数据
export function getNameListInfo(params) {
  return request(`${BASE_URl}/user/selectSysUserPageData`, {
    method: 'POST',
    data: params,
  });
}

//删除数据
export function delProjectListPage(params) {
  return request(
    `${BASE_URl}/projectIntendStand/delIntendStandItem?id=${params.id}`,
    {
      method: 'DELETE',
      data: params,
    },
  );
}

//根据id查询项目立项信息
export function queryProjectListById(params) {
  return request(
    `${BASE_URl}/projectIntendStand/getIntendStandItemInfo?id=${params.id}`,
    {
      method: 'POST',
      data: params,
    },
  );
}

//把把预立项项目立项
export function intendItemToStandItem(params) {
  return request(
    `${BASE_URl}/projectIntendStand/intendItemToStandItem?id=${params.id}`,
    {
      method: 'POST',
      data: params,
    },
  );
}

//项目撤销
export function revokeProjectStandItem(params) {
  return request(
    `${BASE_URl}/projectIntendStand/revokeProject?id=${params.id}`,
    {
      method: 'POST',
      data: params,
    },
  );
}

//  关注/取消关注项目 0关注 -1取消
export function updateFollowState(params) {
  const { item_id, state } = params;
  return request(
    `${BASE_URl}/projectList/updateFollowState?item_id=${item_id}&state=${state}`,
    {
      method: 'POST',
    },
  );
}
//  项目列表标题行下拉框信息
export function getSearchListInfo() {
  return request(`${BASE_URl}/projectList/getSearchListInfo`, {
    method: 'POST',
  });
}

//  查询项目列表名称
export function getItemNameList(params) {
  const { name } = params;
  return request(`${BASE_URl}/projectList/getItemNameList?name=${name}`, {
    method: 'POST',
  });
}

//获取部门下拉框数据
export function getDeptListInfo() {
  return request(`${BASE_URl}/dept/SWCARESdeptAll`, {
    method: 'POST',
  });
}

//获取项目列表的列
export function getSearchFieldInfo() {
  return request(`${BASE_URl}/projectList/getSearchFieldInfo`, {
    method: 'POST',
  });
}

//动态获取项目列表的查询条件
export function getDynamicSearchInfo(params) {
  return request(`${BASE_URl}/projectIntendStand/getProjectIntendList`, {
    method: 'POST',
    data: params,
  });
}

//下载项目列表数据
export function downProjectListFile(params) {
  return request(`${BASE_URl}/projectIntendStand/downProjectIntendListFile`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      Accept: '*/*',
    },
    responseType: 'blob',
  });
}

//下载预立项目导入模板Excel
export function uploadFileExcel() {
  return request(`${BASE_URl}/projectIntendStand/downTemplateFile`, {
    method: 'GET',
    responseType: 'blob',
  });
}

//上传预立项项目数据
export function uploadFile() {
  return request(`/projectIntendStand/upLoadProjectIntendStand`, {
    method: 'POST',
  });
}

//权限
export function getButtonObject() {
  return request(`${BASE_URl}/access/getItemAccess`, {
    method: 'POST',
  });
}

//查询项目审批流程图信息
export function getItemTrial(id) {
  return request(`${BASE_URl}/projectStand/getItemTrial?item_id=${id}`, {
    method: 'post',
  });
}
