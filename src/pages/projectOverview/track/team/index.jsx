import React, { useState, useEffect } from 'react';
import styles from './index.less';
import classNames from 'classnames';
import {
  Button,
  Modal,
  Upload,
  Form,
  Input,
  Select,
  Table,
  Pagination,
  DatePicker,
  message,
  Popconfirm,
  Tooltip,
} from 'antd';
import team_icon from '@/assets/team_icon.svg';
import {
  getProjectTeam,
  getDeptListInfo,
  getFileUrl,
  getNameListInfo,
  AddProjectTeamUser,
  delProjectTeamUser,
  UpdateProjectTeamUser,
} from './service';
import {
  InfoCircleOutlined,
  UserOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
const { Option } = Select;
import moment from 'moment';
import { BASE_URl } from "../../../../utils/constant";

const { Search } = Input;

const Team = props => {
  const { id, itemNumber, over, memberType } = props;
  const dateFormat = 'YYYY/MM/DD';
  const [dataSource, setDataSource] = useState([]);
  const [name, setName] = useState('');
  const [params, setParams] = useState({
    page: 1,
    limit: 10,
    name: '',
  });
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [dataTotal, setDataTotal] = useState(0);
  const [deptList, setDeptList] = useState([]);
  // const [roleList, setRoleList] = useState([]);
  const [nameList, setNameList] = useState([]);
  const [dept, setDept] = useState([]);

  //弹窗标题
  const [modalTitle, setModalTitle] = useState('');

  const [visibleModal, setVisibleModal] = useState(false);
  const [fromData, setFromData] = useState({});
  const [updateID, setUpdateID] = useState({});
  const [updateItem, setUpdateItem] = useState({});

  const columns = [
    {
      title: '名称',
      fixed: 'left',
      align: 'left',
      dataIndex: 'name',
      key: 'name',
      ellipsis: {
        showTitle: false,
      },
      render: name => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    {
      title: '部门',
      dataIndex: 'dept',
      fixed: 'left',
      align: 'left',
      key: 'dept',
      ellipsis: {
        showTitle: false,
      },
      render: name => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    {
      title: '角色',
      dataIndex: 'character',
      fixed: 'left',
      align: 'left',
      key: 'character',
      ellipsis: {
        showTitle: false,
      },
      render: name => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    ,
    {
      title: '职责',
      dataIndex: 'dutie',
      fixed: 'left',
      align: 'left',
      key: 'dutie',
      ellipsis: {
        showTitle: false,
      },
      render: name => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    {
      title: '进入项目组时间',
      dataIndex: 'start_time',
      key: 'start_time',
    },
    {
      title: '出项目组时间',
      dataIndex: 'end_time',
      key: 'end_time',
    },
    {
      title: '操作',
      key: '操作',
      render: item =>
        item && (
          <div>
            {item.del_flag === '0' && over !== 'over' && memberType !== '1' && (
              <a
                onClick={() => showMoald({ title: '编辑团队成员', item: item })}
              >
                编辑
              </a>
            )}
            {item.del_flag === '0'
              ? over !== 'over' &&
                memberType !== '1' && (
                  <Popconfirm
                    title="是否删除？"
                    okText="是"
                    cancelText="否"
                    onConfirm={() => deleteClick(item.id)}
                  >
                    <a className={styles.delete}>删除</a>
                  </Popconfirm>
                )
              : item.del_flag !== '0' && (
                  <span className={styles.history}>历史成员</span>
                )}
          </div>
        ),
    },
  ];

  //获取信息
  const getTeamList = async value => {
    const { code, data } = await getProjectTeam(value);
    if (code === 200) {
      setLoading(false);
      setDataSource(data.records);
      data.records.forEach(item => {
        item.key = `${item.id}`;
      });
      const { pages, total } = data;
      setDataTotal(total);
    }
  };
  //获取部门下拉
  const getDeptListInfos = async value => {
    const { code, data } = await getDeptListInfo(value);
    if (code === 200) {
      const deptList = new Set();
      data.forEach(item => {
        item.deptFullName && deptList.add(item.deptFullName);
      });
      setDeptList(deptList);
    }
  };
  // //获取部门下拉
  // const getRoleListInfos = async value => {
  //   const { code, data } = await getRoleListInfo(value);
  //   if (code === 200) {
  //     console.log(data);
  //     // const roleList = new Set()
  //     // data.forEach(item => {
  //     //   item.deptFullName && deptList.add(item.deptFullName)
  //     // })
  //     // setRoleList(roleList)
  //   }
  // }
  useEffect(() => {
    getList();
  }, [id, params]);

  const getList = () => {
    const { page, limit, name } = params;
    getTeamList({
      page,
      limit,
      item_id: id,
      name: name,
    });
  };
  useEffect(() => {
    getDeptListInfos();
    getFileUrlList();
  }, []);
  //输入名字
  const nameChange = e => {
    setName(e.target.value);
  };
  //搜索
  const searchName = () => {
    setParams({ ...params, name: name });
  };
  //条数改变
  const onChangePageNumber = (value, size) => {
    setParams({ ...params, page: value });
  };
  //翻页
  const pageSizeChange = value => {
    setParams({ ...params, limit: value });
  };

  const showMoald = data => {
    const { title, item } = data;
    setVisibleModal(true);
    form.setFieldsValue({
      itemID: '',
      name: '',
      dept: '',
      deptId: '',
      nameIdCard: '',
      character: '',
      dutie: '',
      startTime: '',
      endTime: '',
    });
    if (title === '编辑团队成员') {
      const name = `${item.name}-${item.dept}`;
      setFromData({ ...item, name });
      setUpdateID(item.id);
      setUpdateItem(item);
      const startTime = (item.start_time && moment(item.start_time)) || '';
      const endTime = (item.end_time && moment(item.end_time)) || '';
      form.setFieldsValue({ ...item, name, endTime, startTime });
    }
    setModalTitle(title);
    setVisibleModal(true);
  };

  const handleOk = () => {
    setVisibleModal(false);
    form.setFieldsValue({});
  };

  const handleCancel = () => {
    setVisibleModal(false);
    form.setFieldsValue({
      name: '',
      character: '',
      dutie: '',
    });
  };

  const deleteClick = id => {
    delTeamUsers(id);
  };
  const delTeamUsers = async value => {
    const { code, data } = await delProjectTeamUser(value);
    if (code === 200) {
      message.success({
        content: '删除成功!',
        key: 'delTeamUsers',
        duration: 2,
      });
      getList();
    }
  };
  //表单提交
  const onFinishFormData = value => {
    const index = value.name.indexOf('-');
    const indexDept = value.name.indexOf('@');
    const indexDeptId = value.name.indexOf('+');
    const name = value.name.substring(0, index);
    const nameIdCard = value.name.substring(index + 1, indexDept);
    const dept = value.name.substring(indexDept + 1, indexDeptId);
    const deptId = value.name.substring(indexDeptId + 1);
    if (modalTitle === '编辑团队成员') {
      UpdateTeamUser({
        id: updateID,
        name: updateItem.name,
        nameIdCard: updateItem.name_id_card,
        deptId: updateItem.dept_id,
        dept: updateItem.dept,
        character: value.character,
        dutie: value.dutie,
        startTime: moment(value.startTime).format('YYYY-MM-DD'),
        endTime:
          (value.endTime && moment(value.endTime).format('YYYY-MM-DD')) || '',
      });
    } else {
      AddProjectTeamUsers({
        itemID: id,
        name: name,
        dept: dept,
        deptId: deptId,
        nameIdCard: nameIdCard,
        character: value.character,
        dutie: value.dutie,
        startTime: moment(value.startTime).format('YYYY-MM-DD'),
        endTime:
          (value.endTime && moment(value.endTime).format('YYYY-MM-DD')) || '',
      });
    }
  };
  const AddProjectTeamUsers = async value => {
    const { code, data } = await AddProjectTeamUser(value);
    if (code === 200) {
      setVisibleModal(false);
      message.success({
        content: '成员添加成功!',
        key: 'AddProjectTeamUsers',
        duration: 2,
      });
      getList();
    }
  };
  const UpdateTeamUser = async value => {
    const { code, data } = await UpdateProjectTeamUser(value);
    if (code === 200) {
      setVisibleModal(false);
      message.success({
        content: '成员编辑成功!',
        key: 'UpdateProjectTeamUser',
        duration: 2,
      });
      getList();
    }
  };

  let timeout;
  function fetch(value, callback) {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    function fake() {
      const getNameListInfos = async value => {
        const { code, data } = await getNameListInfo({
          page: 1,
          limit: 10,
          employeeName: value,
        });
        if (code === 200) {
          callback(data.records);
        }
      };
      getNameListInfos(value);
    }
    timeout = setTimeout(fake, 300);
  }

  const handleSearch = value => {
    if (value) {
      fetch(value, data => setNameList(data));
    }
  };

  const [itemMemberUrl, setItemMemberUrl] = useState('');

  const getFileUrlList = async () => {
    const { code, data } = await getFileUrl();
    if (data) {
      data.forEach(item => {
        if (item.common_name === 'item_member_url') {
          setItemMemberUrl(item.common_info);
        }
      });
    }
  };
  const userInfo = JSON.parse(localStorage.getItem('userInfo'));
  //导入
  const [uploadProps, setUploadProps] = useState({
    action: BASE_URl+ '/File/uploadMemberInfo',
    headers: {
      isToken: false,
      Authorization: `Bearer ${userInfo.access_token}`,
    },
    data: {
      item_id: id,
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`上传成功`);
        getList();
      } else if (info.file.status === 'error') {
        message.error({
          content: `上传失败!${info.file.response.msg}`,
          key: 'error',
          duration: 3,
        });
      }
    },
  });
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');

  const handleDatePickerChange = (type, date) => {
    if (type === 'startTime') {
      setStartTime(date);
    } else {
      setEndTime(date);
    }
  };
  return (
    <div className={styles.team}>
      <div className={styles.title}>
        <div>
          <img src={team_icon} alt="" />
          <span className={styles.name}>团队</span>
        </div>
        <div>
          {over !== 'over' && memberType !== '1' && (
            <Button>
              <a href={itemMemberUrl}>下载导入模板</a>
            </Button>
          )}
          {over !== 'over' && memberType !== '1' && (
            <Upload
              showUploadList={false}
              {...uploadProps}
              withCredentials={true}
            >
              <Button type="file" style={{ margin: '0 10px' }}>
                导入成员
              </Button>
            </Upload>
          )}
          <Search
            placeholder="成员名称"
            prefix={<UserOutlined className="site-form-item-icon" />}
            style={{ width: 180 }}
            className={styles.selects}
            enterButton
            onSearch={searchName}
            onChange={nameChange}
            onPressEnter={searchName}
          />
          {over !== 'over' && memberType !== '1' && (
            <Button
              type="primary"
              onClick={() => showMoald({ title: '新增团队成员', item: '' })}
            >
              新增
            </Button>
          )}
        </div>
      </div>
      <Table
        columns={columns}
        dataSource={dataSource}
        // scroll={{ x: 1000 }}
        pagination={false}
        // rowSelection={{ columnWidth: 20, ...rowSelection }}
        loading={loading}
        size="middle"
        className={styles.anTdTable}
      />
      <div className={styles.pagination}>
        <div>
          <Select
            defaultValue="10"
            style={{ width: 150 }}
            className={styles.selects}
            onChange={pageSizeChange}
          >
            <Option value="10">显示结果：10条</Option>
            <Option value="20">显示结果：20条</Option>
            <Option value="50">显示结果：50条</Option>
          </Select>
          <span className={styles.total}>共{dataTotal}条</span>
        </div>
        <Pagination
          total={dataTotal || 0}
          pageSize={params.limit}
          showSizeChanger={false}
          onChange={onChangePageNumber}
          className={styles.current}
        />
      </div>

      <Modal
        title={`${modalTitle}`}
        visible={visibleModal}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
      >
        <Form
          initialValues={fromData}
          layout="vertical"
          form={form}
          name="nest-messages"
          onFinish={onFinishFormData}
        >
          <Form.Item
            label="姓名"
            name="name"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Select
              showSearch
              defaultActiveFirstOption={false}
              showArrow={false}
              filterOption={false}
              disabled={modalTitle === '编辑团队成员'}
              className={styles.selects}
              onSearch={handleSearch}
              notFoundContent={null}
            >
              {nameList.length > 0 &&
                nameList.map((item, index) => (
                  <Option
                    value={`${item.employee_name}-${item.employee_number}@${item.deptName}+${item.dept_id}`}
                    key={index}
                  >
                    {item.employee_name}-{item.deptName}
                  </Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item
            label="担任角色"
            name="character"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="主要职责"
            name="dutie"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="进入项目组时间"
            name="startTime"
            style={{ float: 'left', marginRight: 50 }}
            rules={[
              {
                required: true,
              },
            ]}
          >
            <DatePicker
              onChange={(date, dateString) =>
                handleDatePickerChange('startTime', dateString)
              }
              format={dateFormat}
              suffixIcon={<ClockCircleOutlined />}
              disabledDate={current => {
                return endTime && current > moment(endTime);
              }}
              className={styles.rangePicker}
            />
          </Form.Item>
          <Form.Item label="出项目组时间" name="endTime">
            <DatePicker
              onChange={(date, dateString) =>
                handleDatePickerChange('endTime', dateString)
              }
              format={dateFormat}
              suffixIcon={<ClockCircleOutlined />}
              disabledDate={current => {
                return startTime && current < moment(startTime);
              }}
              className={styles.rangePicker}
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Team;
