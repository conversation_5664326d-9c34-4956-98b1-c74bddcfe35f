.team {
  width: 1024px;
  margin: 0 auto;
  background: #ffffff;
  border: 1px solid #edeff2;
  box-sizing: border-box;
  box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);

  padding: 16px 24px;

  input,
  button {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
  }

  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    line-height: 32px;

    .name {
      font-family: OPPOSans;
      font-style: normal;
      font-weight: bold;
      font-size: 16px;
      line-height: 24px;
      color: #333333;
      margin-left: 5px;
    }

    .inputName {
      box-sizing: border-box;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      margin-right: 8px;
    }
  }

  .pagination {
    display: flex;
    justify-content: space-between;

    .pageSelect {
      border: 1px solid #edeff2;
      box-sizing: border-box;
      border-radius: 6px;
    }

    .total {
      font-style: normal;
      font-weight: 500;
      font-size: 12px;
      line-height: 20px;
      margin-left: 8px;
      color: #7a7a7a;
    }

    input,
    button,
    li {
      border: 1px solid #edeff2;
      box-sizing: border-box;
      border-radius: 6px;
    }
  }

  .delete {
    color: #e8684a;
    margin-left: 24px;
  }

  .anTdTable tr > th,
  .anTdTable tr > td {
    border-bottom: 0px;
    padding-left: 0 5px 0;
  }

  .anTdTable tr > th {
    font-size: 12px;
    line-height: 20px;
    color: #7a7a7a;

    > div > div {
      padding: 10px 16px;
    }
  }
}

.rangePicker {
  border: 1px solid #edeff2;
  box-sizing: border-box;
  border-radius: 6px;

  input {
    border: 0;
  }
}

.selects,
.selects > div {
  border-color: #edeff2 !important;
  box-sizing: border-box;
  border-radius: 6px !important;
}

.history {
  color: #7a7a7a;
  font-size: 12px;
}
