import React, { useState, useEffect } from 'react';
import edit_icon from '@/assets/edit_icon.svg';
import progress_icon from '@/assets/progress_icon.svg';
import file_icon from '@/assets/file_icon.svg';
import money_icon from '@/assets/money_icon.svg';
import tips_icon from '@/assets/tips_icon.svg';
import {
  Button,
  Modal,
  Form,
  Input,
  message,
  Tooltip,
  Progress,
  DatePicker,
  Radio,
  Tag,
} from 'antd';
import {
  InfoCircleOutlined,
  RightOutlined,
  StockOutlined,
  FundOutlined,
  DotChartOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import styles from '../index.less';

import { updateProjOverview, getProjectFacts, getHistory } from '../service';

const Survey = props => {
  const { itemNumber, changeTabs, over, memberType } = props;
  const [visibleModal, setVisibleModal] = useState(false);
  const [fromData, setFromData] = useState({});
  const [form] = Form.useForm();
  const [projectInfo, setProjectInfo] = useState({});
  //项目跟踪数据
  const [historyList, setHistoryList] = useState([]);

  useEffect(() => {
    getHistoryList();
  }, []);

  const getHistoryList = async () => {
    let resp = await getHistory(itemNumber);
    if (resp.code == 200) {
      setHistoryList(resp.data);
    }
  };

  //初始化数据
  useEffect(() => {
    getFacts(itemNumber);
  }, [itemNumber]);

  //获取项目信息
  const getFacts = async value => {
    const { code, data } = await getProjectFacts(value);
    if (code === 200) {
      setProjectInfo(data);
      console.log('129123', data);
    }
  };
  const showMoald = () => {
    setFromData({ project_overview: projectInfo.project_overview });

    form.setFieldsValue({
      project_overview: projectInfo.project_overview,
    });
    setVisibleModal(true);
  };

  const handleOk = () => {
    setVisibleModal(false);
  };

  const handleCancel = () => {
    setVisibleModal(false);
  };
  //表单提交
  const onFinishFormData = value => {
    if (fromData !== value) {
      updateOverview({
        project_overview: value.project_overview,
        item_num: itemNumber,
      });
    }
  };
  //更新
  const updateOverview = async value => {
    const { code, data } = await updateProjOverview(value);
    if (code === 200) {
      message.success({
        content: '编辑成功!',
        key: 'updateOverview',
        duration: 2,
      });
      getFacts(itemNumber);
    }
    setVisibleModal(false);
  };
  return (
    <div className={styles.content}>
      <div className={styles.formInfo}>
        <div className={styles.info}>
          <div className={styles.title}>
            <div className={styles.iconTitle}>
              {/* <img src={tips_icon} alt="" /> */}
              <FundOutlined style={{ marginRight: 5 }} />
              项目信息
            </div>
            {/* <div>
              <Button> <img src={edit_icon} alt="" /> 编辑</Button>
            </div> */}
          </div>
          <div>
            <div>项目经理</div>
            <p>{projectInfo.manager}</p>
          </div>
          <div>
            <div>计划收入</div>
            <p>{projectInfo.item_plan_income}万</p>
          </div>
          <div>
            <div>协作部门</div>
            <p>{projectInfo.department_cooperation}</p>
          </div>
          <div>
            <div>成本预算</div>
            <p>{projectInfo.item_cost_budgeting}万</p>
          </div>
          <div>
            <div>协作经理</div>
            <p>{projectInfo.cooperation_manager}</p>
          </div>
          <div>
            <div>预估利润</div>
            <p>{projectInfo.item_plan_profit}万</p>
          </div>
          <div>
            <div>项目客户</div>
            <p>{projectInfo.project_client}</p>
          </div>
          <div>
            <div>项目创建人</div>
            <p>{projectInfo.creator_id_card}</p>
          </div>
          <div>
            <div>立项时间</div>
            <p>{projectInfo.create_time}</p>
          </div>
          <div>
            <div>立项审批单编号</div>
            <p>{projectInfo.item_group_num}</p>
          </div>

          <div className={styles.project_overview}>
            <div>
              项目概述
              {over !== 'over' && memberType !== '1' && (
                <img
                  src={edit_icon}
                  alt=""
                  onClick={showMoald}
                  style={{ cursor: 'pointer', marginLeft: 5 }}
                />
              )}
            </div>
            <p>{projectInfo.project_overview}</p>
          </div>
        </div>
        <div className={styles.status}>
          <div className={styles.iconTitle}>
            {/* <img src={tips_icon} alt="" /> */}
            <DotChartOutlined style={{ marginRight: 5 }} />
            项目近况
            <RightOutlined
              style={{ float: 'right' }}
              onClick={() => changeTabs(5)}
            />
          </div>
          <div>
            <div>工作重点</div>
            <p>{projectInfo.current_week_content}</p>
          </div>
          <div>
            <div>工作计划</div>
            <p>{projectInfo.next_week_content}</p>
          </div>
        </div>
        {/*
        <div className={styles.info}>
          <div className={styles.title}>
            <div className={styles.iconTitle}>
              <FundOutlined style={{ marginRight: 5 }} />
              项目变更
            </div>
          </div>
          <div className={styles.project_changeContent}>
            <div className={styles.project_changeBox}>
              <div className={styles.project_changeBox_items}>
                <div className={styles.project_changeBox_title}>变更内容</div>
                <div className={styles.project_changeBox_name}>
                  休息休息变更内容休息休息变更内容,休息休息变更内容休息休息变更内容,
                  休息休息变更内容休息休息变更内容,休息休息变更内容休息休息变更内容
                </div>
              </div>
              <div className={styles.project_changeBox_items}>
                <div className={styles.project_changeBox_title}>变更原因</div>
                <div className={styles.project_changeBox_name}>休息休息</div>
              </div>

              <div className={styles.project_changeBox_items}>
                <div className={styles.project_changeBox_title}>变更影响</div>
                <div className={styles.project_changeBox_name}>休息休息</div>
              </div>
              <div className={styles.project_changeBox_items}>
                <div className={styles.project_changeBox_title}>变更时间</div>
                <div className={styles.project_changeBox_name}>
                  2021年10月21日 15：20
                </div>
              </div>
              <div className={styles.project_changeBox_items}>
                <div className={styles.project_changeBox_title}>变更人</div>
                <div className={styles.project_changeBox_name}>李某</div>
              </div>
            </div>

            <div className={styles.project_changeBox}></div>
            <div className={styles.project_changeBox}></div>
          </div>
        </div> */}
      </div>
      <div className={styles.progress}>
        <div>
          <div className={styles.title}>
            <StockOutlined style={{ marginRight: 5 }} />
            项目进度
          </div>
          <div className={styles.content}>
            <div>
              <Progress
                type="circle"
                percent={projectInfo.schedule || 0}
                width={80}
                status="normal"
                format={() => `${projectInfo.schedule || 0}%`}
              />
              <p className={styles.progressName}>项目整体</p>
              <div className={styles.describe}>
                <div>预计完成还有</div>
                <p>{projectInfo.objectEndDay}天</p>
              </div>
            </div>
            <div>
              <Progress
                type="circle"
                percent={projectInfo.stage_type_percent || 0}
                width={80}
                status="normal"
                format={() => `${projectInfo.stage_type_percent || 0}%`}
              />
              <p className={styles.progressName}>
                {projectInfo.item_stage_type}
              </p>
              <div className={styles.describe}>
                <div>预计{projectInfo.item_stage_type}完成还有</div>
                <p>{projectInfo.scheduleEndDay}天</p>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div className={styles.title}>
            {' '}
            <img src={money_icon} alt="" /> 项目收支
            <RightOutlined
              style={{ float: 'right' }}
              onClick={() => changeTabs(3)}
            />
          </div>
          <div className={styles.content}>
            <div>
              <Progress
                type="circle"
                percent={projectInfo.actual_billing_amountZb || 0}
                width={80}
                status="normal"
                format={() => `${projectInfo.actual_billing_amountZb || 0}%`}
              />
              <p className={styles.progressName}>确认收入占比</p>
              <div className={styles.describe}>
                <div>计划收入</div>
                <p>
                  {projectInfo.item_plan_income == '--'
                    ? projectInfo.item_plan_income
                    : (projectInfo.item_plan_income || 0) + '万'}
                </p>
                <div>已确认收入</div>
                <p>
                  {projectInfo.actual_billing_amount == '--'
                    ? projectInfo.actual_billing_amount
                    : projectInfo.actual_billing_amount + '万'}
                </p>
              </div>
            </div>
            <div>
              <Progress
                type="circle"
                percent={projectInfo.actual_costZb || 0}
                width={80}
                status="normal"
                format={() => `${projectInfo.actual_costZb || 0}%`}
              />
              <p className={styles.progressName}>已核成本占比</p>
              <div className={styles.describe}>
                <div>成本预算</div>
                <p>
                  {projectInfo.item_cost_budgeting == '--'
                    ? projectInfo.item_cost_budgeting
                    : projectInfo.item_cost_budgeting + '万'}
                </p>
                <div>已核成本</div>
                <p>
                  {projectInfo.actual_cost == '--'
                    ? projectInfo.actual_cost
                    : projectInfo.actual_cost + '万'}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.summary}>
          <div>
            {' '}
            <img src={file_icon} alt="" /> 待办汇总
          </div>
          <div>
            待解决问题数
            <p>{projectInfo.riskCount}</p>
          </div>
          <div>
            待完成里程碑
            <p>{projectInfo.scheduleCount}</p>
          </div>
          <div>
            待开票金额
            <p>{projectInfo.wait_amount}万</p>
          </div>
          <div>
            待回款金额
            <p>{projectInfo.wait_refund_number}万</p>
          </div>
        </div>

        <div className={styles.infoNew}>
          <div className={styles.titles}>
            <div className={styles.iconTitles}>
              <HistoryOutlined style={{ marginRight: 5 }} />
              项目跟踪
            </div>
          </div>
          <div className={styles.project_historyList}>
            {historyList.length > 0 &&
              historyList.map((item, index) => {
                return (
                  <div key={index} className={styles.project_historyitem}>
                    <div className={styles.project_historyitem_left}>
                      <span>{item.createTime}</span>
                    </div>
                    <div className={styles.project_historyitem_right}>
                      <Tooltip
                        overlayClassName={styles.TooltipClass}
                        placement="topLeft"
                        title={item.content}
                      >
                        {item.content}
                      </Tooltip>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      </div>
      <Modal
        title="修改项目概述"
        visible={visibleModal}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          name="nest-messages"
          onFinish={onFinishFormData}
        >
          <Form.Item
            label="项目概述"
            name="project_overview"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input.TextArea />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Survey;
