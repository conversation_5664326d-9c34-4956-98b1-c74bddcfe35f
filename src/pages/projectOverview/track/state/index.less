.state {
  display: flex;
  justify-content: space-between;
  width: 1024px;
  margin: 0 auto;
  font-family: OPPOSans;

  > div {
    width: 500px;
    background: #ffffff;
    border: 1px solid #edeff2;
    box-sizing: border-box;
    box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
    border-radius: 8px;
  }

  .left {
    margin-right: 24px;
  }

  .title {
    display: flex;
    justify-content: space-between;
    padding: 12px 24px;

    p {
      font-family: OPPOSans;
      font-style: normal;
      font-weight: bold;
      font-size: 16px;
      line-height: 24px;
      color: #333333;
    }

    span {
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #525252;
      margin-left: 3px;
    }
  }

  .inFoBox {
    padding-left: 172px;

    .inFoBoxTitle {
      font-weight: normal;
      font-size: 12px;
      line-height: 20px;
      color: #7a7a7a;

      img {
        padding-right: 13px;
        margin-left: -3px;
      }
    }

    .inFoBoxContent {
      border-left: 1px solid #c4c4c4;
      padding-left: 13px;

      > p:first-child {
        font-size: 16px;
        line-height: 24px;
        color: #333333;
        margin-bottom: 0px;
      }

      > p:last-child {
        font-style: normal;
        font-weight: normal;
        font-size: 14px;
        line-height: 22px;
        color: #7a7a7a;
        padding-bottom: 32px;
      }
    }

    .tag {
      background: #f7f8fa;
      border: 1px solid #e0e0e0;
      box-sizing: border-box;
      border-radius: 6px;
      padding: 1px 8px;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #3d7bf8;
      margin-left: 8px;
    }

    > div:last-child {
      .inFoBoxContent {
        border-left: 0px;
      }
    }
  }
}

.selects,
.selects > div {
  border-color: #edeff2 !important;
  box-sizing: border-box;
  border-radius: 6px !important;
}
