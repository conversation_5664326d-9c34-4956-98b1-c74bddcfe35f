.IncomeExpenditure {
  .content {
    display: flex;
    justify-content: space-between;
    width: 1024px;
    margin: 0 auto;
  }

  .formInfo {
    display: flex;
    flex-direction: column;
    width: 675px;

    > div {
      padding: 12px 24px;
      background: #ffffff;
      border: 1px solid #edeff2;
      box-sizing: border-box;
      box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
      border-radius: 8px;
    }

    .info {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: 16px;

      .headers {
        display: flex;
        justify-content: flex-end;

        .total_money {
          display: flex;
          align-items: center;

          font-family: OPPOSans;
          font-style: normal;
          font-weight: 500;
          font-size: 14px;
          color: #333333;
          // >span{
          //   font-size: 12px;
          // }
          margin-left: 30px;
        }

        .serch {
          width: auto;
          margin-left: 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }

      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        border-bottom: 0px;
        padding-left: 0 5px 0;
      }

      p {
        font-size: 14px;
        line-height: 22px;
        color: #333333;
      }

      > div {
        font-size: 12px;
        line-height: 20px;
        color: #7a7a7a;
      }

      .title {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        font-weight: bold;
        font-size: 16px;
        line-height: 32px;
        color: #333333;
      }

      > div {
        width: 50%;
      }

      > div:last-child {
        border-left: 1px solid #edeff2;
      }
    }
  }

  .progress {
    display: flex;
    flex-direction: column;
    width: 325px;

    .progressName {
      font-size: 12px;
      line-height: 20px;
      text-align: center;
      color: #7a7a7a;
      padding-top: 10px;
    }

    > div {
      background: #ffffff;
      border: 1px solid #edeff2;
      box-sizing: border-box;
      box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
      border-radius: 8px;
      margin-bottom: 16px;
    }

    .title {
      font-family: OPPOSans;
      font-style: normal;
      font-weight: bold;
      font-size: 16px;
      line-height: 24px;
      padding: 12px 16px;
      color: #333333;
    }

    .describe {
      text-align: left;
      padding-left: 16px;

      > div {
        margin-top: 12px;
        font-size: 12px;
        line-height: 20px;
        color: #7a7a7a;
      }

      p {
        margin-bottom: 0;
        font-size: 14px;
        line-height: 22px;
        color: #333333;
      }
    }
    .content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      text-align: center;

      > div:first-child {
        margin-bottom: 12px;
        border-right: 1px solid #edeff2;
      }

      > div {
        width: 50%;
      }
    }

    .summary {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      padding: 12px 16px;

      > div {
        width: 50%;
      }

      > div:first-of-type {
        width: 100%;
        font-family: OPPOSans;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 24px;
        color: #333333;
        padding-bottom: 24px;
      }
    }
  }

  .iconTitle {
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    color: #333333;
    padding-bottom: 16px;

    img {
      margin-right: 6.5px;
    }
  }

  .status {
    .headers {
      display: flex;
      justify-content: flex-end;

      .total_money {
        display: flex;
        align-items: center;

        font-family: OPPOSans;
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        // >span{
        //   font-size: 12px;
        // }
        margin-left: 30px;
      }

      .serch {
        width: auto;
        margin-left: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    > div {
      > div {
        font-size: 12px;
        line-height: 20px;
        color: #7a7a7a;
      }

      > p {
        font-size: 14px;
        line-height: 22px;
        color: #333333;
      }
    }
  }

  .constract {
    margin-top: 16px;
    margin-bottom: 16px;
  }

  .circle {
    height: 6px;
    width: 6px;
    border-radius: 50%;
    background-color: #34b682;
  }

  .anTdTable tr > th,
  .anTdTable tr > td {
    border-bottom: 0px;
    padding-left: 0 5px 0;
  }

  .anTdTable tr > th {
    font-size: 12px;
    line-height: 20px;
    color: #7a7a7a;

    > div > div {
      padding: 10px 16px;
    }
  }
}

.rangePicker {
  border: 1px solid #edeff2;
  box-sizing: border-box;
  border-radius: 6px;

  input {
    border: 0;
  }
}
