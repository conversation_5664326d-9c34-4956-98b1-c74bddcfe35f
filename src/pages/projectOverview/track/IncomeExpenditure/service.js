import request from '@/utils/request';
import { BASE_URl } from "../../../../utils/constant";

//查询里程碑信息
export function getProjectIncome(params) {
  return request(`${BASE_URl}/projectTrack/getProjectIncome?item_num=${params}`, {
    method: 'POST',
  });
}

//5.查询项目概况信息  GF-Q-015-2020
export function getProjectFacts(params) {
  return request(`${BASE_URl}/projectTrack/getProjectFacts?item_num=${params}`, {
    method: 'POST',
    data: params,
  });
}

//查询项目收支信息
export function serchProjectIncome(params) {
  return request(
    `${BASE_URl}/projectTrack/getProjectIncome?item_num=${params.item_num}&inYear=${params.inYear}&costYear=${params.costYear}&code=${params.code}`,
    {
      method: 'POST',
    },
  );
}

//费用类型下拉框信息
export function getInComeType() {
  return request(`${BASE_URl}/Finance/getInComeType`, {
    method: 'post',
  });
}
