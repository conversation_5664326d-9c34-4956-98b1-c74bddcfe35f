import React, { useEffect, useState } from 'react';
import styles from './index.less';
import echarts from 'echarts';
import hollow_icon from '@/assets/hollow_icon.svg';
import solid_icon from '@/assets/solid_icon.svg';
import moment from 'moment';
import reqwest from 'reqwest';
import {
  Button,
  Modal,
  Form,
  Upload,
  Input,
  Tooltip,
  InputNumber,
  Slider,
  Row,
  Popconfirm,
  Col,
  DatePicker,
  message,
} from 'antd';
const data = '1';

const Constract = props => {
  useEffect(() => {
    let chartDom = document.getElementById('main');
    let myChart = echarts.init(chartDom);
    let option = {
      tooltip: {
        trigger: 'axis',
      },
      color: ['#E8684A', '#F6BD16', '#3D7BF8'],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: [
          '2021-05',
          '2021-06',
          '2021-07',
          '2021-08',
          '2021-09',
          '2021-10',
          '2021-11',
        ],
      },
      yAxis: {
        type: 'category',
        axisLabel: {
          formatter: value => {
            let dates = '';
            if (value === 1) {
              dates = 'a';
            } else if (value === 2) {
              dates = 'b';
            } else if (value === 3) {
              dates = 'c';
            } else if (value === 4) {
              dates = 'd';
            }
            return dates;
          },
        },
      },
      series: [
        {
          name: '计划收入',
          type: 'line',
          stack: '总量',
          data: [1, 2, 2, 2, 3, 3, 4],
        },
        {
          name: '实际收入',
          type: 'line',
          stack: '总量',
          data: [1, 2, 3, 2, 3, 4, 4],
        },
        {
          name: '实际成本',
          type: 'line',
          stack: '总量',
          data: [1, 1, 2, 3, 3, 3, 4],
        },
      ],
    };
    myChart.setOption(option);
  }, []);

  return (
    <div className={styles.constract}>
      <div className={styles.constractMain} id="main"></div>
      <div className={styles.constractBottom}>
        <div className={styles.textItem}>
          <div
            className={styles.textItemColor}
            style={{ backgroundColor: 'aquamarine' }}
          ></div>
          <div className={styles.textItemText}>计划收入</div>
        </div>
        <div className={styles.textItem}>
          <div className={styles.textItemColor}></div>
          <div className={styles.textItemText}>实际收入</div>
        </div>
        <div className={styles.textItem}>
          <div className={styles.textItemColor}></div>
          <div className={styles.textItemText}>实际成本</div>
        </div>
      </div>
    </div>
  );
};

export default Constract;
