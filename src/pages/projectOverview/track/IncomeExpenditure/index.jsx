import React, { useState, useEffect } from 'react';
import edit_icon from '@/assets/edit_icon.svg';
import progress_icon from '@/assets/progress_icon.svg';
import file_icon from '@/assets/file_icon.svg';
import money_icon from '@/assets/money_icon.svg';
import tips_icon from '@/assets/tips_icon.svg';
import { getProjectIncome, serchProjectIncome, getInComeType } from './service';
import { SearchOutlined } from '@ant-design/icons';
import moment from 'moment';
import { Progress, Table, DatePicker, Button, Select, Radio } from 'antd';
import styles from './index.less';
import Constract from './constract';

const IncomeExpenditure = props => {
  //费用分类
  const [TypeData, setTypeData] = useState([]);
  //存放当前选择费用名称
  const [TypeTitle, setTypeTitle] = useState('');
  //项目成本费用总和s

  const [planCostCounts, setPlanCostCounts] = useState(0);
  const [filterDropdownVisible, setfilterDropdownVisible] = useState(true);
  const [Plancost, setPlancost] = useState(0);

  const { itemNumber, over, memberType } = props;
  const [incomeInfo, setIncomeInfo] = useState([]);
  const [oucomeInfo, setOucomeInfo] = useState([]);
  const [collectBrInfo, setCollectBrInfo] = useState({});
  const [passIncomeInfo, setPassIncomeInfo] = useState([]);

  //查询收入支出
  const [SearchParmas, setSearchParmas] = useState({
    item_num: '',
    inYear: '',
    costYear: '',
    code: '',
  });

  const [incomeCount, setIncomeCount] = useState();
  const [actualAmountCount, setActualAmountCount] = useState();
  // 搜索icon
  const handleGetIcon = filtered => {
    return (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    );
  };

  //获取费用类分类
  const getInComeTypes = async () => {
    const { data, code } = await getInComeType();
    if (code === 200) {
      data;
      const typeData = data.map(item => {
        let { ORDER_BY_DERIVED_0, name, code } = item;
        return {
          title: name.charAt(name.length - 1) == '费' ? name : name + '费',
          code: code,
          dataIndex: [code, 'actualCost'],
          key: 'calss' + ORDER_BY_DERIVED_0,
          align: 'center',
        };
      });
      setTypeData(typeData);
    }
  };
  useEffect(() => {
    getInComeTypes();
  }, []);

  // 表列下拉框改变值
  const handleSelectVal = async e => {
    const newTypeData = TypeData.filter(item => {
      if (item.code == e.target.value) {
        return item;
      }
    });
    if (e.target.value != '') {
      setTypeTitle(newTypeData[0].title);
    } else {
      setTypeTitle('');
    }

    setSearchParmas({ ...SearchParmas, code: e.target.value });
    const sendData = SearchParmas;
    sendData['code'] = e.target.value;

    const resp = await serchProjectIncome(sendData);
    if (resp.code === 200) {
      setPlanCostCounts(resp.data[1].planCostCount);
      setOucomeInfo(resp.data[1].OucomeInfo);
    }
  };

  //表列所有下拉框
  const radioGrouop = list => {
    return (
      <div style={{ maxHeight: '200px', overflow: 'auto' }}>
        <Radio.Group
          style={{ padding: '5px 10px', height: 'auto' }}
          onChange={handleSelectVal}
        >
          <Radio
            key="all"
            style={{
              display: 'flex',
              alignItems: 'left',
            }}
            value=""
          >
            全部费用
          </Radio>
          {list
            ? list.map(item => {
                return (
                  <Radio
                    key={list.code}
                    style={{
                      display: 'flex',
                      alignItems: 'left',
                    }}
                    value={item.code}
                  >
                    {item.title}
                  </Radio>
                );
              })
            : null}
        </Radio.Group>
      </div>
    );
  };

  //获取信息
  const getMilepostInfo = async value => {
    //将项目编号放进搜索队列
    setSearchParmas({
      ...SearchParmas,
      item_num: window.location.search.split('itemNo=')[1],
    });
    const { code, data } = await getProjectIncome(value);
    if (code === 200) {
      data[1].OucomeInfo.forEach(item => {
        item.key = `${item.plan_time}${item.plan_content}`;
      });
      data[0].IncomeInfo.forEach(item => {
        item.key = `${item.plan_time}${item.plan_billing_time}`;
      });
      let sum = 0;
      data[1].OucomeInfo.forEach(items => {
        sum = sum + items.plan_cost;
      });

      setPlancost(sum);

      setIncomeCount(data[0].IncomeCount);
      setActualAmountCount(data[3].actualAmountCount);
      setIncomeInfo(data[0].IncomeInfo);
      setOucomeInfo(data[1].OucomeInfo);
      setPassIncomeInfo(data[3].PassIncomeInfo);
      setCollectBrInfo(data[2].collectBrInfo);
    }
  };

  //查询-日期-收入
  const onChanges = async (date, dateString) => {
    const sendData = {
      item_num: window.location.search.split('itemNo=')[1],
      inYear: dateString,
      costYear: '',
      code: '',
    };
    const resp = await serchProjectIncome(sendData);
    if (resp.code === 200) {
      setIncomeInfo(resp.data[0].IncomeInfo);
      setPassIncomeInfo(resp.data[3].PassIncomeInfo);
    }
  };

  //查询-日期-成本
  const onChangesOut = async (date, dateString) => {
    const sendData = SearchParmas;
    sendData['costYear'] = dateString;
    setSearchParmas({ ...SearchParmas, costYear: dateString });
    const resp = await serchProjectIncome(sendData);
    if (resp.code === 200) {
      setOucomeInfo(resp.data[1].OucomeInfo);
    }
  };
  const onFilterDropdownVisibleChange = v => {};

  useEffect(() => {
    getMilepostInfo(itemNumber);
  }, [itemNumber]);
  const oucomeColumns = [
    {
      title: '年度',
      dataIndex: 'plan_time',
    },
    {
      title: '费用类别',
      dataIndex: 'plan_content',
      filterIcon: filtered => handleGetIcon(filtered),
      filterDropdown: radioGrouop(TypeData),
    },
    {
      title: '部门',
      dataIndex: 'dept_name',
    },

    {
      title: '年度预算',
      dataIndex: 'plan_cost',
      render: item => item && <span>{item}W</span>,
    },
    {
      title: '已核成本',
      dataIndex: 'actual_cost',

      render: item => item && <span>{item}W</span>,
    },
    {
      title: '剩余预算',
      dataIndex: 'keep',

      render: item => item && <span>{item}W</span>,
    },
  ];

  const incomeColumns1 = [
    {
      title: '计划开票时间',
      dataIndex: 'plan_time',
      sorter: {
        compare: (a, b) =>
          moment(a.plan_time).format('YYYYMM') -
          moment(b.plan_time).format('YYYYMM'),
      },
    },
    {
      title: '计划收入金额',
      dataIndex: 'plan_income',
      sorter: {
        compare: (a, b) => a.plan_income - b.plan_income,
      },
      render: item => item && <span>{item}W</span>,
    },
  ];

  const incomeColumns2 = [
    {
      title: '收入确认时间',
      dataIndex: 'plan_billing_time',
      sorter: {
        compare: (a, b) =>
          moment(a.plan_time).format('YYYYMM') -
          moment(b.plan_time).format('YYYYMM'),
      },
    },
    {
      title: '实际收入金额',
      dataIndex: 'actual_billing_amount',
      sorter: {
        compare: (a, b) => a.chinese - b.chinese,
      },
      render: item => item && <span>{item}W</span>,
    },
  ];

  return (
    <div className={styles.IncomeExpenditure}>
      <div className={styles.content}>
        <div className={styles.formInfo}>
          <div className={styles.info}>
            <div className={styles.title}>
              <div>项目收入</div>
              <div className={styles.headers}>
                <div className={styles.total_money}>
                  计划开票总和：
                  <span>{Number(incomeCount).toFixed(2) || 0}W</span>
                </div>
                <div className={styles.total_money}>
                  实际回款总和：
                  <span>{Number(actualAmountCount).toFixed(2) || 0}W</span>
                </div>
                <div className={styles.serch}>
                  <DatePicker
                    placeholder="按年份筛选"
                    className={styles.rangePicker}
                    onChange={onChanges}
                    picker="year"
                  />
                </div>
              </div>
            </div>

            <div>
              <Table
                columns={incomeColumns1}
                dataSource={incomeInfo}
                pagination={false}
                className={styles.anTdTable}
              />
            </div>
            <div>
              <Table
                columns={incomeColumns2}
                dataSource={passIncomeInfo}
                pagination={false}
                className={styles.anTdTable}
              />
            </div>
          </div>
          <div className={styles.status}>
            <div className={styles.iconTitle}>
              项目成本
              <div className={styles.headers} style={{ float: 'right' }}>
                <div className={styles.total_money}>
                  总预算：
                  <span>{Number(Plancost).toFixed(2) || 0}W</span>
                </div>

                {TypeTitle && (
                  <>
                    <div className={styles.total_money}>
                      {TypeTitle}总和：
                      <span>{planCostCounts || 0}W</span>
                    </div>
                  </>
                )}

                <div className={styles.serch}>
                  <DatePicker
                    placeholder="按年份筛选"
                    onChange={onChangesOut}
                    className={styles.rangePicker}
                    picker="year"
                  />
                </div>
              </div>
            </div>
            <Table
              columns={oucomeColumns}
              dataSource={oucomeInfo}
              pagination={false}
              className={styles.anTdTable}
            />
          </div>
          {/* <div className={styles.constract}>
            <div className={styles.iconTitle}>收支对比</div>
            <div className={styles.constractContent}>
              <Constract></Constract>
            </div>
          </div> */}
        </div>
        <div className={styles.progress}>
          <div>
            <div className={styles.title}>
              {' '}
              <img src={money_icon} alt="" /> 项目收支
              {/* <RightOutlined style={{ float: 'right' }} /> */}
            </div>
            <div className={styles.content}>
              <div>
                <Progress
                  type="circle"
                  percent={collectBrInfo.actual_billing_amountZb || 0}
                  width={80}
                  status="normal"
                  format={() =>
                    `${collectBrInfo.actual_billing_amountZb || 0}%`
                  }
                />
                <p className={styles.progressName}>确认收入占比</p>
                <div className={styles.describe}>
                  <div>计划收入</div>
                  <p>
                    {collectBrInfo.item_plan_income == '--'
                      ? collectBrInfo.item_plan_income
                      : (collectBrInfo.item_plan_income || 0) + '万'}
                  </p>
                  <div>已确认收入</div>
                  <p>
                    {collectBrInfo.actual_billing_amount == '--'
                      ? collectBrInfo.actual_billing_amount
                      : collectBrInfo.actual_billing_amount + '万'}
                  </p>
                </div>
              </div>
              <div>
                <Progress
                  type="circle"
                  percent={collectBrInfo.actual_costZb || 0}
                  width={80}
                  status="normal"
                  format={() => `${collectBrInfo.actual_costZb || 0}%`}
                />
                <p className={styles.progressName}>已核成本占比</p>
                <div className={styles.describe}>
                  <div>成本预算</div>
                  <p>
                    {collectBrInfo.item_cost_budgeting == '--'
                      ? collectBrInfo.item_cost_budgeting
                      : collectBrInfo.item_cost_budgeting + '万'}
                  </p>
                  <div>已核成本</div>
                  <p>
                    {collectBrInfo.actual_cost == '--'
                      ? collectBrInfo.actual_cost
                      : collectBrInfo.actual_cost + '万'}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className={styles.summary}>
            <div>
              {' '}
              <img src={file_icon} alt="" /> 项目利润
            </div>
            <div>
              目标利润
              <p>{collectBrInfo.item_plan_profit}万</p>
            </div>
            <div>
              目标利润率
              <p>{collectBrInfo.item_plan_profitZb}%</p>
            </div>
            <div>
              实际利润
              <p>
                {collectBrInfo.real_profit}
                {typeof collectBrInfo.real_profit === 'number' && '万'}
              </p>
            </div>
            <div>
              实际利润率
              <p>
                {collectBrInfo.real_profitZb}
                {typeof collectBrInfo.real_profitZb === 'number' && '%'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IncomeExpenditure;
