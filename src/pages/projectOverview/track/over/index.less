.card {
  position: relative;
  width: 1024px;
  background: #ffffff;
  border: 1px solid #edeff2;
  box-sizing: border-box;
  padding: 13px 24px;
  box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
  border-radius: 8px !important;
  margin: 0 auto;
  margin-top: 12px;

  > div:first-child {
    padding: 0px 0px 0px 0px !important;
  }
}

.titles {
  font-family: OPPOSans;
  font-style: normal;
  font-weight: bold;
  font-size: 16px;
  line-height: 24px;
  color: #333333;
  display: flex;
  justify-content: space-between;

  .name {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    color: #333333;
    margin-left: 5px;
  }

  .inputName {
    box-sizing: border-box;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
    margin-right: 8px;
  }
}

nav {
  display: flex;
  justify-content: space-between;
  background: #ffffff;
  /* 分割线/底部 */
  height: 150px;
  // box-shadow: inset 0px -1px 0px #edeff2;

  .NavLeft {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .navTitle {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: bold;
    font-size: 20px;
    line-height: 28px;
    color: #333333;
  }

  .navName {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500px;
    font-size: 14px;
    line-height: 22px;
    color: #7a7a7a;

    > span {
      margin-right: 16px;
    }
  }

  .navTime {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #333333;
  }

  .navType {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #333333;

    > span {
      margin-right: 16px;
    }
  }

  .NavRight {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    white-space: nowrap;

    .tags {
      > div {
        text-align: center;
        padding: 5px 12px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        color: #333333;
        margin-right: 10px;
      }

      img {
        margin-right: 5px;
      }

      display: flex;
    }

    > div:last-child {
      display: flex;
      justify-content: flex-end;
    }

    .selectBox {
      font-size: 14px;
      line-height: 22px;
      color: #333333;
      margin-left: 16px;

      > div {
        border-radius: 6px;

        > span {
          font-size: 14px;
          color: #333333;
        }
      }
    }
  }
}

.scroll_x {
  overflow-x: auto;
}

.scroll_x_boxs {
  display: flex;

  > div {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
    padding: 14px 12px;
  }
}

.margin_auto {
  margin: 0 auto;
}

.margin_right {
  margin-right: 16px;
}

.selects,
.ant-input-affix-wrapper,
.selects > div {
  width: 100%;
  border-color: #edeff2 !important;
  box-sizing: border-box;
  border-radius: 6px !important;
}

.buttom_box {
  width: 1024px;
  display: flex;
  margin: 0 auto;
  margin-top: 24px;
  justify-content: space-between;

  > div {
    width: 500px;
    background: #ffffff;
    border: 1px solid #edeff2;
    box-sizing: border-box;
    padding: 13px 24px;
    border-radius: 8px !important;
    box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
  }
}

.submit_boxs {
  width: 100%;
  position: fixed;
  bottom: 0;
  right: 0;
  background: #ffffff;
  box-shadow: inset 0px 1px 0px #edeff2;
  margin-left: 256px;

  > div {
    margin-left: 256px;
    display: flex;
    justify-content: space-between;

    > div {
      padding: 8px 20px;
      display: flex;
      align-items: center;
    }

    button {
      margin: 0 8px;
    }
  }
}

.progress_box {
  display: flex;
  margin-top: 50px;
  justify-content: space-between;

  div {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 22px;
    color: #7a7a7a;
  }

  span {
    font-size: 16px;
    line-height: 24px;
    color: #333333;
  }
}

.progressName {
  font-size: 12px;
  line-height: 20px;
  text-align: center;
  color: #7a7a7a;
  padding-top: 10px;
}

.anTdTable tr > th,
.anTdTable tr > td {
  border-bottom: 0px;
  padding-left: 0 5px 0;
}

.anTdTable tr > th {
  font-size: 12px;
  line-height: 20px;
  color: #7a7a7a;

  > div > div {
    padding: 10px 16px;
  }
}

.delete_cooperation_dept {
  float: right;

  span {
    color: #e8684a;
  }
}

.uplod_list {
  display: flex;
  justify-content: flex-end;

  > div {
    margin: -20px 150px 20px 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }
}

.proposal_box {
  display: flex;
  margin-top: 20px;
  flex-wrap: wrap;

  > div {
    width: 23%;
    margin-right: 10px;
    min-width: 180px;
    min-height: 100px;
    background: #ffffff;
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 24px;

    > div:last-child {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      line-height: 20px;
      color: #7a7a7a;
    }
  }

  .content {
    box-shadow: inset 0px -1px 0px #edeff2;
    padding: 3px 0px 5px 0px;
    font-size: 14px;
    line-height: 22px;
    color: #333333;
    margin-bottom: 8px;
  }

  .dept_title {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #333333;
    display: flex;
    justify-content: space-between;
  }
}
