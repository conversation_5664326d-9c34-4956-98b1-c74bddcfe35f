import request from '@/utils/request';
import { BASE_URl } from "../../../../utils/constant";

//获取项目结项台头信息
export function getItemEndTitle(params) {
  return request(`${BASE_URl}/projectEnd/getItemEndTitle?item_num=${params}`, {
    method: 'POST',
  });
}

//获取项目结项台头信息
export function inItemEnd(params) {
  return request(`${BASE_URl}/projectEnd/inItemEnd`, {
    method: 'POST',
    data: params,
  });
}

//获取项目结项
export function getItemEnd(params) {
  const { trial_ids, item_id } = params;
  return request(`${BASE_URl}/projectEnd/getItemEnds?item_id=${item_id}`, {
    method: 'POST',
  });
}
