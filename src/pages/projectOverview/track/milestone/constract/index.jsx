import React, { useEffect, useState } from 'react';
import styles from './index.less';
import echarts from 'echarts';

const Constract = props => {
  let planData = props.planData[0];
  let actualData = props.actualData[0];

  useEffect(() => {
    let datalables = [];
    let datavalues = ['未开始'];

    let planDataValue = [];
    let actualDataValue = [];

    if (actualData && actualData) {
      if (planData.length > 0 && actualData.length > 0) {
        planData.forEach(item => {
          datalables.push(item.milestoneName);

          if (!item.plan_end_time) {
            planDataValue.push('未开始');
          } else {
            planDataValue.push(item.plan_end_time);
          }
          if (!datavalues.includes(item.plan_end_time) && item.plan_end_time) {
            datavalues.push(item.plan_end_time);
          }
        });
        actualData.forEach(item => {
          if (!item.end_time) {
            actualDataValue.push('未开始');
          } else {
            actualDataValue.push(item.end_time);
          }

          if (!datavalues.includes(item.end_time) && item.end_time) {
            datavalues.push(item.end_time);
          }
        });
      }
    }

    let chartDom = document.getElementById('main');
    let myChart = echarts.init(chartDom);
    let option = {
      tooltip: {
        trigger: 'axis',
        formatter: (params, ticket, callback) => {
          let formatterHtml = `${params[0].axisValueLabel}<br/>`;
          params.map((item, index) => {
            formatterHtml += `
                ${item.marker}${item.seriesName}: ${
              item.data ? item.data : '未开始'
            } <br/>
                  `;
          });
          return formatterHtml;
        },
      },

      color: ['rgb(52, 182, 130)', '#3d7bf8'],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: datalables,

        axisLine: {
          lineStyle: {
            type: 'dashed',
            color: '#3d7bf8',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#3d7bf8',
          },
        },
      },
      yAxis: {
        type: 'category',
        data: datavalues,

        axisLine: {
          lineStyle: {
            type: 'dashed',
            color: '#3d7bf8',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#5d8be7',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            width: 1,
            color: '#c9cdd5',
            type: 'dashed',
          },
        },
      },

      series: [
        {
          name: '计划里程碑',
          type: 'line',
          stack: 'Total',
          data: planDataValue,
          Symbol: 'none',

          areaStyle: {
            color: 'rgb(52, 182, 130)',
            origin: 'start',
            opacity: '0.1',
          },
        },
        {
          name: '实际里程碑',
          type: 'line',
          stack: 'Total',
          data: actualDataValue,
          Symbol: 'none',
          areaStyle: {
            color: '#3d7bf8',
            origin: 'start',
            opacity: '0.1',
          },
        },
      ],
    };
    myChart.setOption(option);
  }, [props]);

  return (
    <div className={styles.constract}>
      <div className={styles.constractMain} id="main"></div>
      <div className={styles.constractBottom}>
        <div className={styles.textItem}>
          <div
            className={styles.textItemColor}
            style={{ backgroundColor: 'rgb(52, 182, 130)' }}
          ></div>
          <div className={styles.textItemText}>计划里程碑</div>
        </div>
        <div className={styles.textItem}>
          <div
            className={styles.textItemColor}
            style={{ backgroundColor: '#3d7bf8' }}
          ></div>
          <div className={styles.textItemText}>实际里程碑</div>
        </div>
      </div>
    </div>
  );
};

export default Constract;
