.milestone {
  width: 1024px;
  margin: 0 auto;
  background: #ffffff;
  border: 1px solid #edeff2;
  border-radius: 6px;
  /* 分割线/底部 */

  box-shadow: inset 0px -1px 0px #edeff2;

  .typeBox {
    position: relative;
    display: flex;
    align-items: center;

    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
    height: 120px;

    > div:nth-child(9) > div > .hoverBox {
      left: -160px;
    }

    > div:first-child {
      border: 1px solid #edeff2;
      box-sizing: border-box;
      border-radius: 6px;
      width: 100px;
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #666666;
      box-shadow: inset 0px -1px 0px #edeff2;
      text-align: center;
      line-height: 120px;
      margin-right: 15px;
    }

    > div:last-child {
      .iconLine {
        display: none;
      }
    }

    .iconLine {
      width: 80px;
      border-bottom: 1px dashed #3d7bf8;
      box-shadow: inset 0px -1px 0px #edeff2;
      height: 0px;
      margin: 0 8px;
    }

    .toneItems {
      position: relative;
      margin-right: 20px;
      width: 92px;
      margin-top: 10px;

      > div:first-child {
        position: absolute;
        left: 40px;
        display: flex;
        align-items: center;
      }

      .endTime {
        position: absolute;
        bottom: -20px;
        left: 13px;
        font-style: normal;
        font-weight: 500;
        font-size: 10px;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 3px;
      }

      > div:last-child {
        text-align: center;
        margin-top: 20px;

        div {
          font-family: OPPOSans;
          font-style: normal;
          font-weight: 500;
          font-size: 12px;
          line-height: 20px;
          text-align: center;
          color: #333333;
        }

        p {
          font-style: normal;
          font-weight: 500;
          font-size: 10px;
          line-height: 18px;
          text-align: center;
        }
      }
    }

    .hoverImg {
      cursor: pointer;
    }

    .hoverBox {
      width: 200px;
      background: #ffffff;
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #edeff2;
      box-sizing: border-box;
      box-shadow: 0px 4px 24px -8px rgba(22, 45, 90, 0.16);
      border-radius: 6px;
      z-index: 999;
      padding: 8px 12px;
      position: absolute;
      top: -100px;
      left: 30px;
      opacity: 0;
      z-index: -999;
      transition: all 1s;

      div {
        font-family: OPPOSans;
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        color: #333333;
      }

      p {
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 20px;
        color: #7a7a7a;
        margin-bottom: 3px;
        width: 1800px;
        max-width: 180px;
        word-break: break-all;
        word-wrap: break-word;
      }
    }

    .hoverImg:hover + .hoverBox {
      opacity: 1;
      z-index: 999;
    }

    .hoverBox:hover {
      opacity: 1;
      z-index: 999;
    }

    .percentage {
      font-style: normal;
      font-weight: 500;
      font-size: 10px;
      line-height: 18px;
      text-align: center;
      color: #3d7bf8;
      position: absolute;
      top: -20px;
      left: -4px;
      background: linear-gradient(
          0deg,
          rgba(61, 123, 248, 0.1),
          rgba(61, 123, 248, 0.1)
        ),
        #f7f8fa;
      border-radius: 4px;
    }
  }
}

.addModal {
  .ant-modal-content {
    border: 1px solid;
    border-radius: 14px;
  }

  .upload {
    position: absolute;
    top: -30px;
    right: 0px;
  }

  .modal_buttons {
    margin-bottom: 0px;

    button {
      margin-right: 10px;
    }

    > div > div > div {
      display: flex;
    }
  }
}

.tips {
  position: absolute;
  top: 70px;
  width: 100px;
  left: 0px;
  font-style: normal;
  font-weight: 500;
  font-size: 10px;
  line-height: 22px;
  color: #7a7a7a;
  cursor: pointer;

  > .tipsDiv {
    display: none;
    width: 200px;
    background: #ffffff;
    border: 1px solid #edeff2;
    box-sizing: border-box;
    box-shadow: 0px 4px 24px -8px rgba(22, 45, 90, 0.16);
    border-radius: 6px;
    z-index: 999;
    padding: 8px 12px;
    position: absolute;
    top: 20px;
    left: -30px;
  }
}

.tips:hover > .tipsDiv {
  display: block;
}

.show_file {
  margin-left: 5px;
  cursor: pointer;
}

.selects,
.selects > div {
  border-color: #edeff2 !important;
  box-sizing: border-box;
  border-radius: 6px !important;
}
.typeBoxs {
  position: relative;
  display: flex;
  align-items: center;

  border: 1px solid #edeff2;
  box-sizing: border-box;
  border-radius: 6px;
  height: auto;

  > div:nth-child(9) > div > .hoverBox {
    left: -160px;
  }

  > div:first-child {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
    width: 100px;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #666666;
    box-shadow: inset 0px -1px 0px #edeff2;
    text-align: center;
    line-height: 300px;
    margin-right: 15px;
  }

  > div:last-child {
    .iconLine {
      display: none;
    }
  }

  .iconLine {
    width: 80px;
    border-bottom: 1px dashed #3d7bf8;
    box-shadow: inset 0px -1px 0px #edeff2;
    height: 0px;
    margin: 0 8px;
  }

  .toneItems {
    position: relative;
    margin-right: 20px;
    width: 92px;
    margin-top: 10px;

    > div:first-child {
      position: absolute;
      left: 40px;
      display: flex;
      align-items: center;
    }

    .endTime {
      position: absolute;
      bottom: -20px;
      left: 13px;
      font-style: normal;
      font-weight: 500;
      font-size: 10px;
      line-height: 20px;
      color: rgba(0, 0, 0, 0.65);
      margin-bottom: 3px;
    }

    > div:last-child {
      text-align: center;
      margin-top: 20px;

      div {
        font-family: OPPOSans;
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        color: #333333;
      }

      p {
        font-style: normal;
        font-weight: 500;
        font-size: 10px;
        line-height: 18px;
        text-align: center;
      }
    }
  }

  .hoverImg {
    cursor: pointer;
  }

  .hoverBox {
    width: 200px;
    background: #ffffff;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #edeff2;
    box-sizing: border-box;
    box-shadow: 0px 4px 24px -8px rgba(22, 45, 90, 0.16);
    border-radius: 6px;
    z-index: 999;
    padding: 8px 12px;
    position: absolute;
    top: -100px;
    left: 30px;
    opacity: 0;
    z-index: -999;
    transition: all 1s;

    div {
      font-family: OPPOSans;
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #333333;
    }

    p {
      font-style: normal;
      font-weight: 500;
      font-size: 12px;
      line-height: 20px;
      color: #7a7a7a;
      margin-bottom: 3px;
      width: 1800px;
      max-width: 180px;
      word-break: break-all;
      word-wrap: break-word;
    }
  }

  .hoverImg:hover + .hoverBox {
    opacity: 1;
    z-index: 999;
  }

  .hoverBox:hover {
    opacity: 1;
    z-index: 999;
  }

  .percentage {
    font-style: normal;
    font-weight: 500;
    font-size: 10px;
    line-height: 18px;
    text-align: center;
    color: #3d7bf8;
    position: absolute;
    top: -20px;
    left: -4px;
    background: linear-gradient(
        0deg,
        rgba(61, 123, 248, 0.1),
        rgba(61, 123, 248, 0.1)
      ),
      #f7f8fa;
    border-radius: 4px;
  }
}
.typeBoxConstract {
  width: 80%;
}
.typeBoxs_erchers {
  height: 540px;
  position: relative;
  display: flex;
  align-items: center;

  border: 1px solid #edeff2;
  box-sizing: border-box;
  border-radius: 6px;
  .typeBoxs_erchers_title {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
    width: 100px;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #666666;
    box-shadow: inset 0px -1px 0px #edeff2;
    text-align: center;
    line-height: 540px;
    margin-right: 15px;
  }
}
