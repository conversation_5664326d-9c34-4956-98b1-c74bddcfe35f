import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { getProjectMilepost, updateProjPercent, delItemFile } from './service';
import hollow_icon from '@/assets/hollow_icon.svg';
import solid_icon from '@/assets/solid_icon.svg';
import reqwest from 'reqwest';
import Constract from './constract';
import {
  Button,
  Modal,
  Form,
  Upload,
  Input,
  Tooltip,
  InputNumber,
  Slider,
  Row,
  Popconfirm,
  Col,
  DatePicker,
  message,
} from 'antd';

import {
  QuestionCircleOutlined,
  LinkOutlined,
  ClockCircleOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { BASE_URl } from '../../../../utils/constant';

const Milestone = props => {
  const { itemNumber, over, memberType, actualData, planData } = props;

  const userInfo = JSON.parse(localStorage.getItem('userInfo'));
  const [formData] = Form.useForm();

  const milestoneInfo = [
    {
      itemStageName: '达成合作',
      itemStageType: 'demand_survey',
      info: '确定意向客户，达成合作意向',
    },
    {
      itemStageName: '合同确立',
      itemStageType: 'business_negotiation',
      info: '确定合同条款，启动签署流程',
    },
    {
      itemStageName: '合同签订',
      itemStageType: 'agreement_signed',
      info: '完成合同签订',
    },
    {
      itemStageName: '项目实施',
      itemStageType: 'project_implemen',
      info: '完成项目实施',
    },
    {
      itemStageName: '项目验收',
      itemStageType: 'project_acceptance',
      info: '完成项目验收（根据验收次数填写）',
    },
    {
      itemStageName: '项目开票',
      itemStageType: 'project_invoice',
      info: '完成项目开票（根据开票次数填写）',
    },
    {
      itemStageName: '项目回款',
      itemStageType: 'invoice_refund',
      info: '完成项目回款（根据回款次数填写）',
    },
    {
      itemStageName: '结项备案',
      itemStageType: 'project_node',
      info: '完成项目收尾及结项备案',
    },
  ];
  const [item, setItem] = useState();
  const [fileList, setFileList] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProps, setUploadProps] = useState({
    onRemove: file => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: file => {
      setFileList([...fileList, file]);
      return false;
    },
    fileList,
  });
  useEffect(() => {
    setUploadProps({
      onRemove: file => {
        const index = fileList.indexOf(file);
        const newFileList = fileList.slice();
        newFileList.splice(index, 1);
        setFileList(newFileList);
      },
      beforeUpload: file => {
        setFileList([...fileList, file]);
        return false;
      },
      fileList,
    });
  }, [fileList]);

  const handleUpload = () => {
    fileList.forEach(file => {
      const formData = new FormData();
      formData.append('file', file);
      setUploading(true);
      formData.append('id', item.id);
      formData.append('itemNum', item.item_num);
      formData.append('itemStageType', item.item_stage_type);
      formData.append('name', file.name);
      formData.append('url', file.name);

      reqwest({
        url: BASE_URl + '/File/uploadFileGs',
        method: 'post',
        headers: {
          isToken: false,
          Authorization: `Bearer ${userInfo.access_token}`,
        },
        data: formData,
        processData: false,
        success: () => {
          setUploading(false);
          setFileList([]);
          message.success('上传成功');
        },
        error: () => {
          setUploading(false);
          message.error('上传失败');
        },
      });
    });
  };
  // //计划里程碑
  // const [planData, setPlanData] = useState([]);
  // //实际里程碑
  // const [actualData, setActualData] = useState([]);

  const [visible, setVisible] = useState(false);

  //是否必填
  const [formReuqire, setFormReuqire] = useState(false);

  const [completionTime, setCompletionTime] = useState();
  const [modalTitle, setModalTitle] = useState('达成合作');
  const [milestoneName, setMilestoneName] = useState();

  const modalBodyStyle = {
    borderRadius: '6px',
  };

  const updatePercent = value => {
    setItem(value);
    if (value.stage_type_percent !== '100') {
      setModalTitle(value.itemStageName);
      setMilestoneName(value.item_stage_type);
      formData.setFieldsValue({
        stage_type_percent: value.stage_type_percent,
        project_describe: value.project_describe,
      });
      console.log(value, 'qwe');
      setVisible(true);
    }
  };

  const handleOk = () => {
    setVisible(false);
  };

  const handleCancel = () => {
    setVisible(false);
  };

  //百分比达到100 日期必填
  const onSliderChange = value => {
    if (value === 100) {
      setFormReuqire(true);
    } else {
      setFormReuqire(false);
    }
  };

  //提交更新
  const onFinishFormData = values => {
    handleUpload();
    updateProjPercents({
      ...values,
      item_num: itemNumber,
      milestone_name_id: milestoneName,
      end_time: completionTime,
    });
  };
  const updateProjPercents = async value => {
    const { code, data } = await updateProjPercent(value);
    if (code === 200) {
      message.success({
        content: '更新成功!',
        key: 'updateProjPercents',
        duration: 2,
      });
      setVisible(false);
      getMilepostInfo(itemNumber);
    } else {
      // message.success({ content: '更新!', key: 'updateProjPercents', duration: 2 });
    }
  };

  // 日期选择框发生变化
  const onDateChange = (date, dateString) => {
    setCompletionTime(dateString);
  };

  //删除附件
  const deleteFlie = async value => {
    const { id } = value;
    const { code, data } = await delItemFile({ id: id, itemNum: itemNumber });
    if (code === 200) {
      message.success({
        content: '删除成功!',
        key: 'deleteFlie',
        duration: 2,
      });
      Modal.destroyAll();
      getMilepostInfo(itemNumber);
    } else {
      message.error({
        content: '删除失败!',
        key: 'deleteFlie',
        duration: 2,
      });
    }
  };
  //查看附件
  const showFileBox = flieList => {
    Modal.info({
      title: flieList[0].name,
      content: (
        <div>
          {flieList.map((item, index) => {
            return (
              <div>
                <a href={item.url} download={`${item.name}`} target="_blank">
                  {item.name}
                </a>
                {over !== 'over' && memberType !== '1' && (
                  <Popconfirm
                    title="是否删除？"
                    okText="是"
                    cancelText="否"
                    onConfirm={() => deleteFlie({ id: item.id })}
                  >
                    <CloseOutlined style={{ marginLeft: 5, fontSize: 12 }} />
                  </Popconfirm>
                )}
              </div>
            );
          })}
        </div>
      ),
      onOk() {},
    });
  };
  return (
    <div className={styles.milestone}>
      <div className={styles.typeBox}>
        <div>
          计划里程碑
          <Tooltip
            title={
              <div>
                鼠标移到菱形图标查看概述
                <br />
              </div>
            }
          >
            <QuestionCircleOutlined style={{ width: 20 }} />
          </Tooltip>
        </div>
        {planData[0] &&
          planData[0].map((item, index) => (
            <div className={styles.toneItems} key={index}>
              <div>
                <img src={hollow_icon} alt="" className={styles.hoverImg} />
                {item.update_past.length > 0 && (
                  <div className={styles.hoverBox}>
                    <div>{item.milestoneName}</div>
                    {item.update_past.length > 0 &&
                      item.update_past.map((items, indexs) => (
                        <div key={indexs}>
                          <p>计划完成时间：{items.plan_end_time}</p>
                          <p>修改时间：{items.create_time}</p>
                        </div>
                      ))}
                  </div>
                )}
                <div className={styles.iconLine}></div>
              </div>
              <div>
                <div>
                  {item.milestoneName}
                  {milestoneInfo.map((value, index) => {
                    if (item.milestone_name === value.itemStageType) {
                      return (
                        <Tooltip
                          key={index}
                          title={
                            <div>
                              {value.info}
                              <br />
                            </div>
                          }
                        >
                          <QuestionCircleOutlined style={{ width: 20 }} />
                        </Tooltip>
                      );
                    }
                  })}
                </div>
                <p>{item.plan_end_time}</p>
              </div>
            </div>
          ))}
      </div>
      <div className={styles.typeBox}>
        <div>
          <div>
            实际里程碑
            <Tooltip
              title={
                <div>
                  鼠标移到菱形图标查看概述
                  <br />
                  点击对应里程碑更新进度
                  <br />
                </div>
              }
            >
              <QuestionCircleOutlined style={{ width: 20 }} />
            </Tooltip>
          </div>
        </div>
        {actualData[0] &&
          actualData[0].map((item, index) => (
            <div className={styles.toneItems} key={index}>
              <div>
                <div className={styles.percentage}>
                  {item.stage_type_percent}%
                </div>
                <Tooltip
                  placement="right"
                  title={
                    item.update_past.length > 0 && (
                      <div className={styles.hoverBox}>
                        <div>{item.milestoneName}</div>
                        {item.update_past.length > 0 &&
                          item.update_past.map((items, indexs) => (
                            <div key={indexs}>
                              {indexs == 0 && (
                                <div>实际完成时间：{item.end_time}</div>
                              )}
                              <>
                                <div>修改时间：{items.create_time}</div>
                                <div>
                                  完成百分比：{items.stage_type_percent}%
                                </div>
                                <div>
                                  概述：
                                  {(item.project_describe &&
                                    item.project_describe) ||
                                    ''}
                                </div>
                              </>
                            </div>
                          ))}
                      </div>
                    )
                  }
                >
                  <img
                    src={solid_icon}
                    alt=""
                    className={styles.hoverImg}
                    onClick={() =>
                      over !== 'over' &&
                      memberType !== '1' &&
                      updatePercent(item)
                    }
                  />
                </Tooltip>
                <div className={styles.iconLine}></div>
              </div>
              <div>
                <div>
                  {item.itemStageName}
                  {milestoneInfo.map((value, index) => {
                    if (item.item_stage_type === value.itemStageType) {
                      return (
                        <Tooltip
                          key={index}
                          title={
                            <div>
                              {value.info}
                              <br />
                            </div>
                          }
                        >
                          <QuestionCircleOutlined style={{ width: 20 }} />
                        </Tooltip>
                      );
                    }
                  })}
                  {item.File && item.File.length > 0 && (
                    <LinkOutlined
                      onClick={() => showFileBox(item.File)}
                      className={styles.show_file}
                    />
                  )}
                </div>
                <div className={styles.endTime}>{item.end_time}</div>
                <div></div>
              </div>
            </div>
          ))}
      </div>
      <div className={styles.typeBoxs_erchers}>
        <div className={styles.typeBoxs_erchers_title}>里程碑偏离度</div>
        <div className={styles.typeBoxConstract}>
          <Constract planData={planData} actualData={actualData}></Constract>
        </div>
      </div>
      <Modal
        title={modalTitle}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
        className={styles.addModal}
        width="380px"
        bodyStyle={modalBodyStyle}
      >
        <Form form={formData} onFinish={onFinishFormData} layout="vertical">
          <Row>
            <Col span={12}>
              <Form.Item
                label="完成百分比"
                name="stage_type_percent"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Slider
                  min={0}
                  name="stage_type_percent"
                  max={100}
                  onChange={onSliderChange}
                  tipFormatter={value => `${value}%`}
                  value={
                    typeof formData.stage_type_percent === 'number'
                      ? formData.stage_type_percent
                      : 0
                  }
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item style={{ marginTop: 30 }} name="stage_type_percent">
                <InputNumber
                  min={0}
                  name="stage_type_percent"
                  className={styles.selects}
                  max={100}
                  style={{ margin: '0 16px' }}
                  value={formData.stage_type_percent}
                  formatter={value => `${value}%`}
                  parser={value => value.replace('%', '')}
                  onChange={onSliderChange}
                />
              </Form.Item>
            </Col>
          </Row>
          {formReuqire && (
            <Form.Item
              label="完成时间"
              name="end_time"
              rules={[
                {
                  required: formReuqire,
                },
              ]}
            >
              <DatePicker
                onChange={onDateChange}
                suffixIcon={<ClockCircleOutlined />}
              />
            </Form.Item>
          )}
          {/* <Form.Item>

            <a className={styles.upload}>上传附件</a>
          </Form.Item> */}
          <Form.Item
            label="进度概述"
            name="project_describe"
            rules={[
              {
                required: formReuqire,
              },
            ]}
          >
            <Input.TextArea />
          </Form.Item>
          <Form.Item label="" name="upFil">
            <Upload
              //showUploadList={false}
              {...uploadProps}
              // withCredentials={true}
            >
              <Button style={{ transform: 'translateX(245px)' }} type="file">
                上传附件
              </Button>
            </Upload>
          </Form.Item>
          <Form.Item className={styles.modal_buttons}>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
            <Button onClick={handleCancel}>取消</Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Milestone;
