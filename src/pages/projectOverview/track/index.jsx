import React, { useState, useEffect } from 'react';
import { getProjectTrackTitle, getProjectFacts, getHistory } from './service';
import Survey from './survey';
import Milestone from './milestone';
import IncomeExpenditure from './IncomeExpenditure';
import State from './state';
import Problem from './problem';
import Over from './over';
import Team from './team';
import { Link, useHistory } from 'umi';

import dotsGreen from '@/assets/dots-green.svg';
import dotsRed from '@/assets/dots-red.svg';

import { Button, message } from 'antd';

import styles from './index.less';
import { getProjectMilepost } from './milestone/service';
export default props => {
  const { location } = props;
  const history = useHistory();
  //tabs列表
  const [navList, setNavlist] = useState([
    {
      key: 1,
      value: '概况',
    },
    {
      key: 2,
      value: '里程碑',
    },
    {
      key: 3,
      value: '收支',
    },
    {
      key: 4,
      value: '状态/把握度',
    },
    {
      key: 5,
      value: '近况/问题',
    },
    {
      key: 6,
      value: '团队',
    },
  ]);
  //项目表头信息
  const [titleInfo, setTitleInfo] = useState({});
  //项目列表信息
  const [projectInfo, setProjectInfo] = useState({});
  //tabs切换
  const [checkedTabs, setCheckedTabs] = useState(1);
  const [power, setPower] = useState(0);
  const [memberType, setMemberType] = useState('0');

  const [actualMilestoneData, setActualMilestoneData] = useState(null);
  const [planMilestoneData, setPlanMilestoneData] = useState(null);
  const over = location.query.over;
  const UpEndType = location.query.UpEndType || {
    MemberType: '0',
  };
  //获取项目title
  const getTitleInfo = async value => {
    const { code, data } = await getProjectTrackTitle(value);
    if (code === 200) {
      setTitleInfo(data);
      setMemberType(data.MemberType);
    }
  };
  //获取项目信息
  const getFacts = async value => {
    const { code, data } = await getProjectFacts(value);
    if (code === 200) {
      setProjectInfo(data);
    }
  };

  //获取里程碑数据
  const getMilepostInfo = async value => {
    const { code, data } = await getProjectMilepost(value);
    if (code === 200) {
      setPlanMilestoneData([data[0].planMilepost]);
      setActualMilestoneData([data[0].realMilepost]);
    }
  };
  //初始化数据
  useEffect(() => {
    const itemNum = location.query.itemNo;
    if (over === 'over') {
      setNavlist([
        ...navList,
        {
          key: 7,
          value: '结项',
        },
      ]);
    }
    getTitleInfo(itemNum);
    getFacts(itemNum);
    getMilepostInfo(itemNum);
  }, [location, power]);

  // //项目里程碑“合同签订—项目验收—项目开票—项目回款”四个节点信息为必填项 才能结项
  // const judgeMilestone = data => {
  //   const arr = data?.map(item => {
  //     return !(
  //       (item?.itemStageName === '合同签署' ||
  //         item?.itemStageName === '项目验收' ||
  //         item?.itemStageName === '项目开票' ||
  //         item?.itemStageName === '项目回款') &&
  //       !item?.project_describe
  //     );
  //   });
  //
  //   if (arr?.indexOf(true) > -1) {
  //     message.warning(
  //       '项目里程碑“合同签订—项目验收—项目开票—项目回款”四个节点信息填写完成后才能结项',
  //     );
  //   }
  //   return arr?.indexOf(true) === -1;
  // };

  useEffect(() => {
    if (location.query.type) {
      setCheckedTabs(Number(location.query.type));
    }
  }, [location]);

  //切换tabs
  const changeTabs = key => {
    setCheckedTabs(key);
  };

  return (
    <div className={styles.track}>
      <nav>
        <div className={styles.NavLeft}>
          <div className={styles.navTitle}>{titleInfo.name}</div>
          <div className={styles.navName}>
            <span>{titleInfo.item_num}</span>
            <span>{titleInfo.dept}</span>
            <span>{titleInfo.manager}</span>
          </div>
          <div className={styles.navTime}>
            {titleInfo.start_time}至{titleInfo.end_time}
          </div>
          <div className={styles.navType}>
            <span>{titleInfo.type}</span>
            <span>{titleInfo.in_come_type}</span>
            <span>{titleInfo.level}</span>
          </div>
        </div>
        <div className={styles.NavRight}>
          <div className={styles.tags}>
            <div>{titleInfo.item_stage}</div>
            {over !== 'over' && memberType !== '1' && (
              <div>
                {titleInfo.item_state === '进度正常' && (
                  <img src={dotsGreen} alt="" />
                )}
                {titleInfo.item_state === '风险预警' && (
                  <img src={dotsRed} alt="" />
                )}
                {titleInfo.item_state === '高风险' && (
                  <img src={dotsRed} alt="" />
                )}
                {titleInfo.item_state}
              </div>
            )}
            {over !== 'over' && memberType !== '1' && (
              <div>
                {titleInfo.item_grasp === '低把握度' && (
                  <img src={dotsRed} alt="" />
                )}
                {titleInfo.item_grasp === '较低把握度' && (
                  <img src={dotsRed} alt="" />
                )}
                {titleInfo.item_grasp === '较高把握度' && (
                  <img src={dotsGreen} alt="" />
                )}
                {titleInfo.item_grasp === '高把握度' && (
                  <img src={dotsGreen} alt="" />
                )}
                {titleInfo.item_grasp}
              </div>
            )}
            {/* <Select defaultValue={titleInfo.item_state || '进度正常'} value={titleInfo.item_state} style={{ width: 120 }} className={styles.selectBox} >
            </Select>
            <Select defaultValue={titleInfo.item_grasp || '高把握度'} value={titleInfo.item_grasp} style={{ width: 140 }} className={styles.selectBox}>
            </Select> */}
          </div>
          <div>
            <div>
              {over !== 'over' &&
                memberType !== '1' &&
                UpEndType.MemberType === '0' && (
                  <Button type="primary" style={{ marginRight: 10 }}>
                    <Link
                      to={{
                        pathname: '/updateProject',
                        query: {
                          id: location.query.id,
                          itemNum: location.query.itemNo,
                          type: 'update',
                        },
                      }}
                    >
                      变更
                    </Link>
                  </Button>
                )}
              {UpEndType.MemberType === '2' && (
                <div className={styles.tags2}>项目变更中</div>
              )}
              {UpEndType.MemberType === '3' && (
                <div className={styles.tags2}>项目变更中</div>
              )}
              {UpEndType.MemberType === '4' && (
                <div className={styles.tags2}>项目结项中</div>
              )}
              {UpEndType.MemberType === '5' && (
                <div className={styles.tags2}>项目结项中</div>
              )}
            </div>
            {over !== 'over' &&
              memberType !== '1' &&
              UpEndType.MemberType === '0' && (
                <Button
                  type="primary"
                  onClick={() => {
                    history?.push({
                      pathname: '/closure',
                      query: {
                        itemNo: location.query.itemNo,
                        id: location.query.id,
                      },
                    });
                    // if (judgeMilestone(actualMilestoneData?.[0])) {
                    //
                    // }
                  }}
                >
                  结项
                </Button>
              )}
          </div>
        </div>
      </nav>

      <section>
        <div className={styles.tabs}>
          {navList.map(item => (
            <div
              key={item.key}
              className={checkedTabs === item.key ? styles.checked : ''}
              onClick={() => changeTabs(item.key)}
            >
              {item.value}
            </div>
          ))}
        </div>
        {checkedTabs === 1 && (
          <Survey
            itemNumber={location.query.itemNo}
            changeTabs={value => changeTabs(value)}
            over={UpEndType.MemberType !== '0' ? 'over' : location.query.over}
            memberType={memberType}
          />
        )}
        {checkedTabs === 2 && (
          <Milestone
            itemNumber={location.query.itemNo}
            memberType={memberType}
            over={UpEndType.MemberType !== '0' ? 'over' : location.query.over}
            actualData={actualMilestoneData}
            planData={planMilestoneData}
          />
        )}
        {checkedTabs === 3 && (
          <IncomeExpenditure
            projectInfo={projectInfo}
            itemNumber={location.query.itemNo}
            memberType={memberType}
            over={UpEndType.MemberType !== '0' ? 'over' : location.query.over}
          />
        )}
        {checkedTabs === 4 && (
          <State
            itemNumber={location.query.itemNo}
            changePower={() => setPower(power + 1)}
            memberType={memberType}
            over={UpEndType.MemberType !== '0' ? 'over' : location.query.over}
          />
        )}
        {checkedTabs === 5 && (
          <Problem
            itemNumber={location.query.itemNo}
            itemId={location.query.id}
            memberType={memberType}
            over={UpEndType.MemberType !== '0' ? 'over' : location.query.over}
          />
        )}
        {checkedTabs === 6 && (
          <Team
            id={location.query.id}
            memberType={memberType}
            over={UpEndType.MemberType !== '0' ? 'over' : location.query.over}
          />
        )}
        {checkedTabs === 7 && <Over id={location.query.id} />}
      </section>
    </div>
  );
};
