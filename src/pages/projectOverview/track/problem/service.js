import request from '@/utils/request';
import { BASE_URl } from "../../../../utils/constant";

//查询项目近况/问题
export function getProjectAscTopic(params) {
  return request(
    `${BASE_URl}/projectTrack/getProjectAscTopic?item_num=${params}`,
    {
      method: 'POST',
    },
  );
}

//新增项目问题
export function AddProjectRisks(params) {
  const { item_num, risk, occurred_time, solutions } = params;
  return request(
    `${BASE_URl}/projectTrack/AddProjectRisks?item_num=${item_num}&risk=${risk}&occurred_time=${occurred_time}&solutions=${solutions}`,
    {
      method: 'POST',
      data: params,
    },
  );
}
//新增项目问题
export function updateItemRisk(params) {
  const { itemNum, risk, occurredTime, solutions, itemId, id } = params;
  return request(
    `${BASE_URl}/projectTrack/updateItemRisk?itemId=${itemId}&id=${id}&itemNum=${itemNum}&risk=${risk}&occurredTime=${occurredTime}&solutions=${solutions}&isResolved="N"`,
    {
      method: 'POST',
    },
  );
}

//新增近期工作重点、计划
export function AddProjectAscTopic(params) {
  const { item_num, current_week_content, next_week_content } = params;
  return request(
    `${BASE_URl}/projectTrack/AddProjectAscTopic?item_num=${item_num}&current_week_content=${current_week_content}&next_week_content=${next_week_content}`,
    {
      method: 'POST',
    },
  );
}

//删除工作问题
export function deleteProjectPromble(params) {
  return request(`${BASE_URl}/projectTrack/delProjectRisks?id=${params}`, {
    method: 'POST',
  });
}

//删除近期工作
export function deleteProjectCurrent(params) {
  const { id, item_num } = params;
  return request(
    `${BASE_URl}/projectTrack/DelProjectAscTopic?item_num=${item_num}&id=${id}`,
    {
      method: 'POST',
    },
  );
}

//编辑近期工作
export function UpdateProjectCurrent(params) {
  const { id, item_num, current_week_content, next_week_content } = params;
  return request(
    `${BASE_URl}/projectTrack/UpdateProjectAscTopic?item_num=${item_num}&id=${id}&current_week_content=${current_week_content}&next_week_content=${next_week_content}`,
    {
      method: 'POST',
    },
  );
}

//处理项目问题
export function InProjectRisks(params) {
  const { id, resolved_time, resolved_condition, is_resolved } = params;
  return request(
    `${BASE_URl}/projectTrack/InProjectRisks?is_resolved=${is_resolved}&id=${id}&resolved_time=${resolved_time ||
      ''}&resolved_condition=${resolved_condition || ''}`,
    {
      method: 'POST',
    },
  );
}
