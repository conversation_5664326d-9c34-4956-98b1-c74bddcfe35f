.problem {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 1024px;
  margin: 0 auto;
  font-family: OPPOSans;

  > div {
    width: 100%;
    background: #ffffff;
    border: 1px solid #edeff2;
    box-sizing: border-box;
    box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
    border-radius: 8px;
  }

  .top {
    margin-bottom: 16px;
  }

  .title {
    display: flex;
    justify-content: space-between;
    padding: 12px 24px 3px 24px;

    p {
      font-family: OPPOSans;
      font-style: normal;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }
  }

  .workBox {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
    margin: 0 24px 8px 24px;

    .boxTitle {
      padding: 9px 16px;
      display: flex;
      justify-content: space-between;
      font-style: normal;
      font-weight: bold;
      font-size: 14px;
      line-height: 22px;
      color: #333333;
      border-bottom: 1px solid #edeff2;
      box-sizing: border-box;
    }

    .boxContent {
      display: flex;
      padding: 9px 16px;
      justify-content: space-between;

      > div {
        width: 40%;

        > div:first-child {
          font-style: normal;
          font-weight: 500;
          font-size: 12px;
          line-height: 20px;
          color: #7a7a7a;
        }

        > div {
          font-style: normal;
          font-weight: 500;
          font-size: 14px;
          line-height: 22px;
          color: #333333;
        }
      }

      > div:last-child {
        width: 10%;
      }
    }
  }

  .editBottun {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #7a7a7a;
    cursor: pointer;
    margin-left: 20px;
  }

  .deleteBottun {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #e8684a;
    margin-left: 26px;
    cursor: pointer;
  }

  .showMore {
    text-align: center;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #3d7bf8;
    margin-bottom: 17px;
    cursor: pointer;
  }

  .problemBox {
    display: flex;
    flex-wrap: wrap;

    > div {
      width: 45%;
      margin: 0 24px;
      border: 1px solid #edeff2;
      box-sizing: border-box;
      margin-bottom: 16px;
      border-radius: 6px;
    }

    .boxTitle {
      padding: 9px 16px;
      display: flex;
      justify-content: space-between;
      font-style: normal;
      font-weight: bold;
      font-size: 14px;
      line-height: 22px;
      color: #333333;
    }

    .boxContent {
      padding: 9px 16px;

      > div {
        > div:first-child {
          font-style: normal;
          font-weight: 500;
          font-size: 12px;
          line-height: 20px;
          color: #7a7a7a;
        }

        > div:last-child {
          font-style: normal;
          font-weight: 500;
          font-size: 14px;
          line-height: 22px;
          color: #333333;
          margin-bottom: 10px;
        }
      }
    }
  }

  .problemList {
    span {
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #525252;
      border: 1px solid #edeff2;
      padding: 5px 12px;
      cursor: pointer;
    }

    span:first-child {
      margin-left: 16px;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
    }

    span:last-child {
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }

    .checked {
      background: #3d7bf8;
      color: #ffffff;
    }
  }

  .problemError,
  .problemOver {
    background: #e8684a;
    border-radius: 6px;
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    /* identical to box height, or 167% */

    text-align: right;
    padding: 2px 8px;
    color: #ffffff;
    margin-left: 5px;
  }

  .problemOver {
    background: #87d068;
    color: #fff;
  }
}

.boxContentOver {
  word-break: break-all;
  word-wrap: break-word;
}

.selects,
.selects > div {
  border-color: #edeff2 !important;
  box-sizing: border-box;
  border-radius: 6px !important;
}
