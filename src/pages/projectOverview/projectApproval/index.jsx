import React, { useState, useEffect } from 'react';
import styles from './index.less';
import {
  QuestionCircleOutlined,
  ClockCircleOutlined,
  UpCircleOutlined,
  DownCircleOutlined,
} from '@ant-design/icons';
import hollow_icon from '@/assets/hollow_icon.svg';
import moment from 'moment';
import {
  Button,
  Modal,
  Anchor,
  Table,
  Collapse,
  Checkbox,
  Form,
  Tooltip,
  Input,
  Select,
  Radio,
  DatePicker,
  message,
  InputNumber,
} from 'antd';
import {
  gitItemInfo,
  getInComeType,
  itemSubmit,
  getDeptListInfo,
  getNameListInfo,
  getSearchListInfo,
  inProjectStand,
} from './service';
import { history } from 'umi';
import html2pdf from 'html2pdf.js/src/index.js';

import classNames from 'classnames';

const { RangePicker } = DatePicker;
const { TextArea, Search } = Input;
const { Link } = Anchor;
const { Panel } = Collapse;
const { Option } = Select;
export default props => {
  const { location } = props;

  const milestoneInfo = [
    {
      itemStageName: '达成合作',
      itemStageType: 'demand_survey',
      info: '确定意向客户，达成合作意向',
    },
    {
      itemStageName: '合同确立',
      itemStageType: 'business_negotiation',
      info: '确定合同条款，启动签署流程',
    },
    {
      itemStageName: '合同签订',
      itemStageType: 'agreement_signed',
      info: '完成合同签订',
    },
    {
      itemStageName: '项目实施',
      itemStageType: 'project_implemen',
      info: '完成项目实施',
    },
    {
      itemStageName: '项目验收',
      itemStageType: 'project_acceptance',
      info: '完成项目验收（根据验收次数填写）',
    },
    {
      itemStageName: '项目开票',
      itemStageType: 'project_invoice',
      info: '完成项目开票（根据开票次数填写）',
    },
    {
      itemStageName: '项目回款',
      itemStageType: 'invoice_refund',
      info: '完成项目回款（根据回款次数填写）',
    },
    {
      itemStageName: '结项备案',
      itemStageType: 'project_node',
      info: '完成项目收尾及结项备案',
    },
  ];
  const columns = [
    {
      title: '名称',
      fixed: 'left',
      align: 'left',
      dataIndex: 'name',
      key: '1',
      ellipsis: {
        showTitle: false,
      },
      render: name => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    {
      title: '部门',
      dataIndex: 'dept',
      fixed: 'left',
      align: 'left',
      key: '2',
      ellipsis: {
        showTitle: false,
      },
      render: name => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    {
      title: '角色',
      dataIndex: 'character',
      fixed: 'left',
      align: 'left',
      key: '3',
      ellipsis: {
        showTitle: false,
      },
      render: name => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    ,
    {
      title: '职责',
      dataIndex: 'dutie',
      fixed: 'left',
      align: 'left',
      key: '4',
      ellipsis: {
        showTitle: false,
      },
      render: name => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    {
      title: '进入项目组时间',
      dataIndex: 'startTime',
      key: 'startTime',
      fixed: 'left',
      align: 'left',
      render: value => (
        <Tooltip placement="topLeft" title={value}>
          {moment(value).format('YYYY-MM-DD')}
        </Tooltip>
      ),
    },
    {
      title: '出项目组时间',
      dataIndex: 'endTime',
      key: 'endTime',
      fixed: 'left',
      align: 'left',
      render: value => (
        <Tooltip placement="topLeft" title={value}>
          {moment(value).format('YYYY-MM-DD')}
        </Tooltip>
      ),
    },
  ];
  //协作部门信息列表
  const [cooperationDeptList, setCooperationDeptList] = useState([
    {
      departmentCooperation: '', //协作部门
      cooperationManager: '', //协作经理
      cooperationManagerDutie: '', //协作项目经理职责
      cooperationDutie: '', //协作部门职责
    },
  ]);
  const [type, setType] = useState('');
  //初始化数据
  useEffect(() => {
    const itemID = location.query.id;
    const trial_ids = location.query.trial_ids;
    setType(location.query.type);
    if (itemID) {
      gitItemInfos({ id: itemID, trial_ids });
    } else {
      form.resetFields();
    }
  }, [location]);

  const [needNumber, setNeedNumber] = useState(false);
  const [needPDF, setNeedPDF] = useState(false);

  const [projectName, setProjectName] = useState('立项部门');
  const [disabled, setDisabled] = useState(false);

  //获取项目
  const gitItemInfos = async value => {
    const { code, data } = await gitItemInfo(value);
    if (code === 200) {
      const formData = { ...data };
      formData.itemTrial &&
        formData.itemTrial.forEach(item => {
          if (item.isStatus == 1 && item.trialId == 8) {
            setNeedNumber(true);
          }
          if (item.isStatus == 1 && item.trialId == 7) {
            setNeedPDF(true);
          }
        });
      formData.name = data.name;
      setProjectName(data.dept);
      formData.managerFull = data.manager;
      formData.deptFull = data.dept;
      if (data.startTime && data.endTime) {
        formData.planTimes = [moment(data.startTime), moment(data.endTime)];
      }
      if (data.itemRisk && data.itemRisk.length > 0) {
        setRiskList([...data.itemRisk]);
      }
      if (data.type) {
        changeManageType(data.type);
      }
      setDataSource(data.itemMember);
      setIfStart(data.ifStart === 'Y');
      setIsCross(data.isDeptCooperation === 'Y');
      setPlanData(data.itemSchedule);
      if (data.cooperation) {
        setCooperationDeptList([...data.cooperation]);
      }
      let cooperationlist = {};
      if (data.cooperation) {
        data.cooperation.forEach((item, index) => {
          const keysList = Object.keys(item);
          keysList.forEach(key => {
            cooperationlist[`${key}${index}`] = item[key];
          });
        });
      }
      if (data.itemPlan && data.itemPlan.length > 0) {
        setPlannedRevenueInputList([
          {
            year: '',
            money: 0,
            item: data.itemPlan,
          },
        ]);
      }

      if (!data.itemCostDept && data.cooperation) {
        let newcooperationList = [];
        data.cooperation.forEach(item => {
          if (item.deptId) {
            let newObj = {
              itemId: item.itemId,
              itemDept: item.departmentCooperation,
              deptId: item.deptId,
              planTime: null,
              planCost: null,
              name: item.departmentCooperation,
              itemCostDept: [
                {
                  deptId: null,
                  itemCostDept: null,
                  itemCostHis: null,
                  itemDept: null,
                  itemId: null,
                  planCost: '0.00',
                  planTime: null,
                },
              ],
              itemCostHis: [
                {
                  deptId: item.itemId,
                  itemDept: item.departmentCooperation,
                  itemId: item.itemId,
                  itemNum: null,
                  planContent: null,
                  planContentSubject: null,
                  planCost: '0.00',
                  planTime: null,
                  proof: '',
                },
              ],
              contentList: [
                {
                  deptId: item.itemId,
                  itemDept: item.departmentCooperation,
                  itemId: item.itemId,
                  itemNum: null,
                  planContent: null,
                  planContentSubject: null,
                  planCost: '0.00',
                  planTime: null,
                  proof: '',
                },
              ],
              content: [
                {
                  dept: item.departmentCooperation,
                  deptId: item.itemId,
                  item: [
                    {
                      money: '0.00',
                      planContent: null,
                      planContentSubject: null,
                      planCost: '0.00',
                      proof: '',
                      type: null,
                    },
                  ],
                  money: '0.00',
                  year: null,
                },
              ],
            };
            newcooperationList.push(newObj);
          }
        });
        setCooperationDeptInputList([...newcooperationList]);
      }
      if (data.itemCostDept && data.itemCostDept.length > 0) {
        if (data.itemCostDept) {
          const yearList = [];
          let contentList = [];
          const cooperationList = [];
          data.itemCostDept.forEach(item => {
            if (item.deptId === data.deptId) {
              //立项部门
              // contentList = item.itemCostHis;
              contentList = contentList.concat(item.itemCostHis);
              item.itemCostDept.forEach(value => {
                yearList.push({
                  year: moment(value.planTime).format('YYYY'),
                  money: value.planCost,
                  item: [],
                });
              });
            } else {
              //协作部门
              cooperationList.push(item);
            }
          });
          contentList.forEach(item => {
            yearList.forEach(yearItem => {
              if (moment(item.planTime).format('YYYY') === yearItem.year) {
                yearItem.item.push({
                  type: item.planContent,
                  planContent: item.planContent,
                  planContentSubject: item.planContentSubject,
                  money: item.planCost,
                  planCost: item.planCost,
                  proof: item.proof,
                });
              }
            });
          });
          if (yearList.length > 0) {
            // 项目成本预算去重
            const map = new Map();
            const newYearList = yearList.filter(
              v => !map.has(v.year) && map.set(v.year, 1),
            );
            setDeptInputList(newYearList);
          }
          if (cooperationList.length > 0) {
            cooperationList.forEach(deptItems => {
              deptItems.content = [];
              deptItems.name = deptItems.itemDept;
              deptItems.contentList = deptItems.itemCostHis;
              deptItems.itemCostDept.forEach(value => {
                deptItems.content.push({
                  year: moment(value.planTime).format('YYYY'),
                  money: value.planCost,
                  deptId: deptItems.deptId,
                  dept: deptItems.itemDept,
                  item: [],
                });
              });
            });
            cooperationList.forEach(deptItems => {
              deptItems.contentList.forEach(item => {
                deptItems.content.forEach(yearItem => {
                  if (moment(item.planTime).format('YYYY') === yearItem.year) {
                    yearItem.item.push({
                      type: item.planContent,
                      planContent: item.planContent,
                      planContentSubject: item.planContentSubject,
                      money: item.planCost,
                      planCost: item.planCost,
                      proof: item.proof,
                    });
                  }
                });
              });
            });

            setCooperationDeptInputList([...cooperationList]);
          }

          if (data.cooperation) {
            if (data.cooperation.length - cooperationList.length > 0) {
              let deptNumber = data.cooperation.length - cooperationList.length;
              let newcooperationList = cooperationList;
              let allDept = [];
              let accDept = [];
              let midDept = [];
              let midcooperation = data.cooperation;
              cooperationList.forEach(item => {
                accDept.push(item.deptId);
              });
              data.cooperation.forEach(item => {
                allDept.push(item.deptId);
                midDept.push(item.deptId);
              });

              accDept.forEach((items, indexs) => {
                if (allDept.indexOf(items) >= 0) {
                  midDept.splice(allDept.indexOf(items), 1);
                  midcooperation.splice(allDept.indexOf(items), 1);
                }
              });
              midcooperation.forEach(item => {
                if (item.deptId) {
                  let newObj = {
                    itemId: item.itemId,
                    itemDept: item.departmentCooperation,
                    deptId: item.deptId,
                    planTime: null,
                    planCost: null,
                    name: item.departmentCooperation,
                    itemCostDept: [
                      {
                        deptId: null,
                        itemCostDept: null,
                        itemCostHis: null,
                        itemDept: null,
                        itemId: null,
                        planCost: '0.00',
                        planTime: null,
                      },
                    ],
                    itemCostHis: [
                      {
                        deptId: item.itemId,
                        itemDept: item.departmentCooperation,
                        itemId: item.itemId,
                        itemNum: null,
                        planContent: null,
                        planContentSubject: null,
                        planCost: '0.00',
                        planTime: null,
                        proof: '',
                      },
                    ],
                    contentList: [
                      {
                        deptId: item.itemId,
                        itemDept: item.departmentCooperation,
                        itemId: item.itemId,
                        itemNum: null,
                        planContent: null,
                        planContentSubject: null,
                        planCost: '0.00',
                        planTime: null,
                        proof: '',
                      },
                    ],
                    content: [
                      {
                        dept: item.departmentCooperation,
                        deptId: item.itemId,
                        item: [
                          {
                            money: '0.00',
                            planContent: null,
                            planContentSubject: null,
                            planCost: '0.00',
                            proof: '',
                            type: null,
                          },
                        ],
                        money: '0.00',
                        year: null,
                      },
                    ],
                  };
                  newcooperationList.push(newObj);
                  deptNumber = deptNumber - 1;
                } else {
                }
              });
              setCooperationDeptInputList([...newcooperationList]);
            }
          }
        }
      }

      form.setFieldsValue({ ...formData, ...cooperationlist });
    }
  };

  const [form] = Form.useForm();
  const [modalFrom] = Form.useForm();
  const [teamForm] = Form.useForm();
  const [visibleModal, setVisibleModal] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  //团队成员
  const [dataSource, setDataSource] = useState([]);
  //项目风险
  const [riskList, setRiskList] = useState([]);

  const [levelList, setLevelList] = useState([]);
  const [newLevelList, setNewLevelList] = useState([]);
  const [revenueTypeList, setRevenueTypeList] = useState([]);
  // 获取表列的所有种类
  const getTotalDorpList = async () => {
    const { data, code } = await getSearchListInfo();
    if (code === 200) {
      const levelList = new Set();
      const revenueTypeList = new Set();
      data.forEach(item => {
        if (item.type === 'item_level') {
          levelList.add(item);
        }
        if (item.type === 'in_come_type') {
          revenueTypeList.add(item);
        }
      });
      setLevelList([...levelList]);
      setRevenueTypeList([...revenueTypeList]);
    }
  };

  //弹出框提交
  const onFinishMoadlFormData = value => {
    setDisabled(true);
    const formData = form.getFieldValue();
    let trialId = '';
    let lowerId = '';
    formData.itemTrial.forEach(item => {
      if (item.isStatus === '1') {
        trialId = item.trialId;
        lowerId = item.lowerId;
      }
    });
    itemSubmits({ ...value, itemId: location.query.id, trialId, lowerId });
  };
  // 审核
  const itemSubmits = async params => {
    const { data, code } = await itemSubmit(params);
    if (code === 200) {
      message.success({ content: '成功!', key: 'itemSubmits', duration: 2 });
      history.replace('/workbench');
    } else {
      message.error({ content: '失败!', key: 'itemSubmits', duration: 2 });
      setDisabled(false);
    }
  };

  //弹出框类别
  const [moaldType, setMoaldType] = useState('');

  const [manageType, setManageType] = useState('manage');
  const changeManageType = value => {
    if (value === 'no_manage') {
      setManageType('no_manage');
      setPlanData([
        {
          milestoneName: '合同确立',
          itemStageType: 'businessNegotiation',
          planEndTime: '',
        },
        {
          milestoneName: '项目实施',
          itemStageType: 'projectImplemen',
          planEndTime: '',
        },
        {
          milestoneName: '项目验收',
          itemStageType: 'projectAcceptance',
          planEndTime: '',
        },
        {
          milestoneName: '结项备案',
          itemStageType: 'projectNode',
          planEndTime: '',
        },
      ]);
      let list = [...levelList];
      list.splice(0, 3);
      setNewLevelList([...list]);
    } else {
      setManageType('manage');
      setPlanData([
        {
          milestoneName: '达成合作',
          itemStageType: 'demandSurvey',
          planEndTime: '',
        },
        {
          milestoneName: '合同确立',
          itemStageType: 'businessNegotiation',
          planEndTime: '',
        },
        {
          milestoneName: '合同签订',
          itemStageType: 'agreementSigned',
          planEndTime: '',
        },
        {
          milestoneName: '项目实施',
          itemStageType: 'projectImplemen',
          planEndTime: '',
        },
        {
          milestoneName: '项目验收',
          itemStageType: 'projectAcceptance',
          planEndTime: '',
        },
        {
          milestoneName: '项目开票',
          itemStageType: 'projectInvoice',
          planEndTime: '',
        },
        {
          milestoneName: '项目回款',
          itemStageType: 'invoiceRefund',
          planEndTime: '',
        },
        {
          milestoneName: '结项备案',
          itemStageType: 'projectNode',
          planEndTime: '',
        },
      ]);
      let list = [...levelList];
      list.splice(3);
      setNewLevelList([...list]);
    }
  };

  const InComeTypeChange = value => {
    if (manageType === 'manage') {
      if (value === 'system_maintenance') {
        setPlanData([
          {
            milestoneName: '达成合作',
            itemStageType: 'demandSurvey',
            planEndTime: '',
          },
          {
            milestoneName: '合同确立',
            itemStageType: 'businessNegotiation',
            planEndTime: '',
          },
          {
            milestoneName: '合同签订',
            itemStageType: 'agreementSigned',
            planEndTime: '',
          },
          {
            milestoneName: '项目开票',
            itemStageType: 'projectInvoice',
            planEndTime: '',
          },
          {
            milestoneName: '项目回款',
            itemStageType: 'invoiceRefund',
            planEndTime: '',
          },
          {
            milestoneName: '结项备案',
            itemStageType: 'projectNode',
            planEndTime: '',
          },
        ]);
      } else {
        setPlanData([
          {
            milestoneName: '达成合作',
            itemStageType: 'demandSurvey',
            planEndTime: '',
          },
          {
            milestoneName: '合同确立',
            itemStageType: 'businessNegotiation',
            planEndTime: '',
          },
          {
            milestoneName: '合同签订',
            itemStageType: 'agreementSigned',
            planEndTime: '',
          },
          {
            milestoneName: '项目实施',
            itemStageType: 'projectImplemen',
            planEndTime: '',
          },
          {
            milestoneName: '项目验收',
            itemStageType: 'projectAcceptance',
            planEndTime: '',
          },
          {
            milestoneName: '项目开票',
            itemStageType: 'projectInvoice',
            planEndTime: '',
          },
          {
            milestoneName: '项目回款',
            itemStageType: 'invoiceRefund',
            planEndTime: '',
          },
          {
            milestoneName: '结项备案',
            itemStageType: 'projectNode',
            planEndTime: '',
          },
        ]);
      }
    }
  };
  //计划里程碑
  const [planData, setPlanData] = useState([]);

  const editMilesTone = index => {
    setItemIndex(index);
    setModalTitle(`${planData[index].milestoneName}`);
    setMoaldType('milesTone');
    setVisibleModal(true);
  };

  //费用类分类
  const [inComeTypeList, setInComeTypeList] = useState([]);
  //部门列表
  const [deptList, setDeptList] = useState([]);
  //人员列表
  const [nameList, setNameList] = useState([]);
  //获取费用类分类
  const getInComeTypes = async () => {
    const { data, code } = await getInComeType();
    if (code === 200) {
      setInComeTypeList(data);
    }
  };
  //获取部门下拉
  const getDeptListInfos = async value => {
    const { code, data } = await getDeptListInfo(value);
    if (code === 200) {
      const deptList = new Set();
      data.forEach(item => {
        item.deptFullName && deptList.add(item);
      });
      setDeptList(deptList);
    }
  };
  let timeout;
  function fetch(value, callback) {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    function fake() {
      const getNameListInfos = async value => {
        const { code, data } = await getNameListInfo({
          page: 1,
          limit: 10,
          employeeName: value,
        });
        if (code === 200) {
          callback(data.records);
        }
      };
      getNameListInfos(value);
    }
    timeout = setTimeout(fake, 300);
  }
  const handleSearch = value => {
    if (value) {
      fetch(value, data => setNameList(data));
    }
  };
  //获取费用类分类
  useEffect(() => {
    getInComeTypes();
    getDeptListInfos();
    getTotalDorpList();
  }, []);

  //是否合同签署前实施
  const [ifStart, setIfStart] = useState(false);

  //是否跨部门协作
  const [isCross, setIsCross] = useState(false);

  //是否跨部门协作
  const deptCheck = e => {
    setIsCross(e.target.checked);
    if (!e.target.checked) {
      setCooperationDeptList([
        {
          departmentCooperation: '', //协作部门
          cooperationManager: '', //协作经理
          cooperationManagerDutie: '', //协作项目经理职责
          cooperationDutie: '', //协作部门职责
        },
      ]);
      setCooperationDeptInputList([
        {
          name: '协作部门',
          content: [
            {
              year: moment(new Date(), 'YYYY'),
              money: 0,
              item: [{ type: '', money: 0, proof: '' }],
            },
          ],
        },
      ]);
    }
  };

  //协作部门
  const cooperationDeptListChange = porps => {
    const { value, index, key } = porps;
    const list = cooperationDeptList;
    if (key === 'departmentCooperation') {
      const idIndex = value.indexOf('@');
      const deptId = value.substring(idIndex + 1);
      const departmentCooperation = value.substring(0, idIndex);
      list[index].deptId = deptId;
      list[index].departmentCooperation = departmentCooperation;
      const assistList = cooperationDeptInputList;
      assistList[index].name = departmentCooperation;
      assistList[index].id = deptId;
      setCooperationDeptInputList([...assistList]);
    }
    if (key === 'cooperationManager') {
      const idIndex = value.indexOf('@');
      const nameIdCard = value.substring(idIndex + 1);
      const cooperationManager = value.substring(0, idIndex);
      list[index].nameIdCard = nameIdCard;
      list[index].cooperationManager = cooperationManager;
    }
    if (key !== 'cooperationManager' && key !== 'departmentCooperation') {
      list[index][key] = value;
    }
    setCooperationDeptList([...list]);
  };

  function warning(value) {
    Modal.warning({
      title: value,
    });
  }
  const showMoald = data => {
    const { title } = data;
    if (title === '审核' && !savePDFClicked && needPDF) {
      warning('请先导出pdf，再审核!');
      return;
    }
    setVisibleModal(true);
    // if (title === '编辑团队成员' || title === '新增团队成员') {
    //   setMoaldType('team');
    // }
    // if (title === '编辑团队成员') {
    //   setItemIndex(itemIndex);
    //   const name = `${item.name}-${item.dept}`;
    //   form.setFieldsValue({ ...item, name });
    // }
    setModalTitle(title);
    setVisibleModal(true);
  };

  const handleOk = () => {
    setVisibleModal(false);
    form.setFieldsValue({});
    modalFrom.setFieldsValue({});
  };

  const handleCancel = () => {
    setVisibleModal(false);
    form.setFieldsValue({
      name: '',
      character: '',
      dutie: '',
    });
    modalFrom.setFieldsValue({});
  };

  //计划收入列
  const [plannedRevenueInputList, setPlannedRevenueInputList] = useState([
    {
      year: '',
      money: 0,
      item: [{ planTime: '', planIncome: '' }],
    },
  ]);

  const plannedRevenueInputListMoneyChange = porps => {
    const { value, index, indexs } = porps;
    const list = plannedRevenueInputList;
    list[index].item[indexs].planIncome = value;
    let money = 0;
    list[index].item.forEach(value => {
      money += +value.planIncome;
    });
    list[index].money = money;
    setPlannedRevenueInputList([...list]);
  };
  //协助部门金额变化
  const cooperationDeptInputListMoneyChange = porps => {
    const { value, index, indexs, contentIndex } = porps;
    const list = cooperationDeptInputList;
    list[index].content[contentIndex].item[indexs].money = value;
    let money = 0;
    list[index].content[contentIndex].item.forEach(value => {
      money += +value.money;
    });
    list[index].content[contentIndex].money = money;
    setCooperationDeptInputList([...list]);
  };
  //协助部门
  const cooperationDeptInputListChange = porps => {
    const { value, index, indexs, contentIndex } = porps;
    const list = cooperationDeptInputList;
    list[index].content[contentIndex].item[indexs].proof = value;
    setCooperationDeptInputList([...list]);
  };

  const deptInputListChange = porps => {
    const { value, index, indexs } = porps;
    const list = deptInputList;
    list[index].item[indexs].proof = value;
    setDeptInputList([...list]);
  };
  const datePickerChange = value => {
    const { date, deteString, index, indexs, key, contentIndex } = value;
    if (key === 'plannedRevenueInputList') {
      const list = plannedRevenueInputList;
      if (indexs === -1) {
        list[index].year = deteString;
      } else {
        const list = plannedRevenueInputList;
        list[index].item[indexs].planTime = deteString;
      }
      setPlannedRevenueInputList([...list]);
    }
    if (key === 'deptInputList') {
      const list = deptInputList;
      list[index].year = deteString;
      setDeptInputList([...list]);
    }
    if (key === 'cooperationDeptInputList') {
      const list = cooperationDeptInputList;
      if (indexs === -1) {
        list[index].content[contentIndex].year = deteString;
      }
      setCooperationDeptInputList([...list]);
    }
  };

  const deptInputListMoneyChange = porps => {
    const { value, index, indexs } = porps;
    const list = deptInputList;
    list[index].item[indexs].planCost = value;
    let money = 0;
    list[index].item.forEach(value => {
      money += +value.planCost;
    });
    list[index].money = money;
    setDeptInputList([...list]);
  };
  //部门预算
  const [deptInputList, setDeptInputList] = useState([
    {
      year: '',
      money: 0,
      item: [{ type: '', money: 0, proof: '' }],
    },
  ]);
  //协作部门预算
  const [cooperationDeptInputList, setCooperationDeptInputList] = useState([
    {
      name: '协作部门',
      content: [
        {
          year: '',
          money: 0,
          item: [{ type: '', money: 0, proof: '' }],
        },
      ],
    },
  ]);

  //费用类别改变
  const itemCostTypeChange = porpos => {
    const { value, index, indexs, type, contentIndex } = porpos;
    if (type === 'deptInputList') {
      const list = deptInputList;
      const indexOf = value.indexOf('@');
      list[index].item[indexs].plan_content = value.substring(0, indexOf);
      list[index].item[indexs].plan_content_subject = value.substring(
        indexOf + 1,
      );
      setDeptInputList([...list]);
    } else if (type === 'cooperationDeptInputList') {
      const list = cooperationDeptInputList;
      list[index].content[contentIndex].item[indexs].item_dept =
        list[index].name;
      list[index].content[contentIndex].item[indexs].deptId = list[index].id;
    }
  };

  const [itemPlanIncome, setItemPlanIncome] = useState(0);
  useEffect(() => {
    let money = 0;
    plannedRevenueInputList.forEach(item => {
      money += +item.money;
      if (item.item.length > 0) {
        item.item.forEach(value => {
          money += +value.planIncome;
        });
      }
    });
    setItemPlanIncome(money);
  }, [plannedRevenueInputList]);

  const [itemCostBudgeting, setItem_cost_budgeting] = useState(0);
  useEffect(() => {
    let money = 0;
    let sumId = [];
    deptInputList.forEach(item => {
      money += +item.money;
    });
    cooperationDeptInputList.forEach(item => {
      if (!sumId.includes(item.deptId)) {
        item.content.forEach(value => {
          money += +value.money;
        });
        sumId.push(item.deptId);
      }
    });
    setItem_cost_budgeting(money);
  }, [deptInputList, cooperationDeptInputList]);
  const onFinishFormData = e => {
    saveData(1);
  };
  const saveData = type => {
    const formData = form.getFieldValue();
    const { managerFull, deptFull } = formData;
    if (formData.planTimes) {
      formData.startTime = moment(formData.planTimes[0]).format('YYYY-MM-DD');
      formData.endTime = moment(formData.planTimes[1]).format('YYYY-MM-DD');
    }
    if (managerFull && managerFull.indexOf('@') !== -1) {
      const idIndex = managerFull.indexOf('@');
      formData.manager = managerFull.substring(0, idIndex);
      formData.managerIdCard = managerFull.substring(idIndex + 1);
    }
    if (deptFull && deptFull.indexOf('@') !== -1) {
      const idIndex = deptFull.indexOf('@');
      formData.dept = deptFull.substring(0, idIndex);
      formData.deptId = deptFull.substring(idIndex + 1);
    }
    if (itemPlanIncome !== 0) {
      const itemPlan = [];
      plannedRevenueInputList.forEach(item => {
        item.item.forEach(value => {
          itemPlan.push(value);
        });
      });
      formData.itemPlan = itemPlan;
      formData.itemPlanIncome = itemPlanIncome;
    }
    const itemCost = [];
    if (deptInputList[0].money !== 0) {
      const list = deptInputList;
      list.forEach(items => {
        items.item.forEach(value => {
          value.deptId = formData.deptId;
          value.dept = formData.dept;
          value.planTime = `${items.year}-01-01`;
          itemCost.push(value);
        });
      });
    }
    if (itemCostBudgeting - deptInputList[0].money !== 0) {
      const list = cooperationDeptInputList;
      list.forEach(item => {
        item.content.forEach(contentItem => {
          contentItem.item.forEach(lastItem => {
            lastItem.deptId = item.id;
            lastItem.dept = item.name;
            lastItem.planTime = `${contentItem.year}-01-01`;
            lastItem.planCost = lastItem.money;
            itemCost.push(lastItem);
          });
        });
      });
    }
    formData.ifStart = ifStart ? 'Y' : 'N';
    formData.isDeptCooperation = isCross ? 'Y' : 'N';
    formData.cooperation = cooperationDeptList;
    formData.itemCost = [...itemCost];
    if (riskList.length > 0) {
      formData.itemRisk = [...riskList];
    }
    createProjectSave({ formData, type, lowerId: itemTrial });
  };

  const createProjectSave = async formData => {
    const { data, code } = await inProjectStand(formData);
    if (code === 200) {
      message.success({
        content: '保存成功!',
        key: 'createProjectSave',
        duration: 2,
      });
    } else {
      message.error({
        content: '保存失败!',
        key: 'createProjectSave',
        duration: 2,
      });
    }
  };

  const [yearMoneyList, setYearMoneyList] = useState([]);
  useEffect(() => {
    const planList = plannedRevenueInputList[0].item;
    if (planList.length > 0) {
      let tempArr = [];
      let afterData = [];
      for (let i = 0; i < planList.length; i++) {
        if (tempArr.indexOf(planList[i].planTime.substring(0, 4)) === -1) {
          afterData.push({
            year: planList[i].planTime.substring(0, 4),
            money: Number(planList[i].planIncome),
          });
          tempArr.push(planList[i].planTime.substring(0, 4));
        } else {
          for (let j = 0; j < afterData.length; j++) {
            if (afterData[j].year == planList[i].planTime.substring(0, 4)) {
              afterData[j].money += Number(planList[i].planIncome);
              break;
            }
          }
        }
      }
      setYearMoneyList(afterData);
    }
  }, [plannedRevenueInputList]);

  const [showAnchor, setShowAnchor] = useState(true);
  const [savePDFClicked, setSavePDFClicked] = useState(false);

  const savePDF = () => {
    const element = document.getElementById('scrollDIV');
    const PDFData = form.getFieldValue();
    var opt = {
      margin: [0, 1],
      filename: `${PDFData.name}立项审核.pdf`,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { scale: 2 },
      jsPDF: { unit: 'in', format: 'a2', orientation: 'landscape' },
    };
    html2pdf()
      .set(opt)
      .from(element)
      .save();
    setSavePDFClicked(true);
  };

  const [submitType, setSubmitType] = useState(0);
  const changeSubmitType = value => {
    setSubmitType(value);
  };
  return (
    <div id="scrollDIV">
      <Anchor
        className={styles.anchor}
        getContainer={() => document.querySelector('#content')}
        affix={true}
        targetOffset={200}
      >
        <Link>
          {showAnchor ? (
            <UpCircleOutlined onClick={() => setShowAnchor(!showAnchor)} />
          ) : (
            <DownCircleOutlined onClick={() => setShowAnchor(!showAnchor)} />
          )}
        </Link>
        {showAnchor && (
          <>
            <Link href="#22" title="项目基本信息" />
            <Link href="#23" title="协作部门信息" />
            <Link href="#24" title="项目成员" />
            <Link href="#25" title="计划里程碑" />
            <Link href="#26" title="项目风险" />
            <Link href="#32" title="项目计划收入(税后)" />
            <Link href="#33" title="项目成本预算" />
            <Link href="#44" title="项目利润" />
          </>
        )}
      </Anchor>
      <Form
        className={styles.forms}
        colon={false}
        form={form}
        labelAlign="right"
        name="nest-messages"
        scrollToFirstError={true}
        onFinish={onFinishFormData}
        labelCol={{ span: 7 }}
        wrapperCol={{ span: 13 }}
      >
        <Collapse
          defaultActiveKey={['1']}
          expandIconPosition="right"
          ghost={true}
        >
          <Panel
            header={<div className={styles.titles}>项目基本信息</div>}
            key="1"
            id="22"
            className={classNames(styles.card)}
          >
            <div>
              <div>
                <Form.Item
                  label="项目名称"
                  name="name"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Input
                    className={styles.selects}
                    placeholder="暂无数据"
                    disabled
                  />
                </Form.Item>
                <Form.Item
                  label="项目客户"
                  name="projectClient"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Input placeholder="暂无数据" disabled />
                </Form.Item>
                <Form.Item
                  label="立项部门"
                  name="deptFull"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    showSearch
                    defaultActiveFirstOption={false}
                    showArrow={false}
                    placeholder="暂无数据"
                    disabled
                    filterOption={false}
                    className={styles.selects}
                    notFoundContent={null}
                    filterOption={(input, option) =>
                      option.children
                        .toLowerCase()
                        .indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {deptList.map(item => (
                      <Option
                        value={`${item.deptFullName}@${item.id}`}
                        key={item.deptCode}
                      >
                        {item.deptFullName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  label="项目经理"
                  name="managerFull"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    showSearch
                    defaultActiveFirstOption={false}
                    placeholder="暂无数据"
                    showArrow={false}
                    className={styles.selects}
                    filterOption={false}
                    disabled
                    onSearch={handleSearch}
                    notFoundContent={null}
                  >
                    {nameList.length > 0 &&
                      nameList.map((item, index) => (
                        <Option
                          value={`${item.employee_name}-${item.deptName}@${item.user_id}`}
                          key={index}
                        >
                          {item.employee_name}-{item.deptName}
                        </Option>
                      ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  label="项目把握度"
                  name="itemGrasp"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Radio.Group
                    disabled
                    value={form.itemGrasp}
                    className={styles.grasp_Box}
                  >
                    <Radio value={'high_grasp'}>
                      高<span className={styles.grasp_info}>(81%-100%)</span>
                    </Radio>
                    <Radio value={'higher_grasp'}>
                      较高
                      <span className={styles.grasp_info}>(51%-80%)</span>
                    </Radio>
                    <Radio value={'lower_grasp'}>
                      较低
                      <span className={styles.grasp_info}>(31%-50%)</span>
                    </Radio>
                    <Radio value={'lowz_grasp'}>
                      低<span className={styles.grasp_info}>(0%-30%)</span>
                    </Radio>
                  </Radio.Group>
                </Form.Item>
                <Form.Item
                  label="项目经理职责"
                  disabled
                  name="managerDutie"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <TextArea placeholder="暂无数据" disabled rows={4} />
                </Form.Item>
                <Form.Item
                  label="项目计划时间"
                  name="planTimes"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <RangePicker
                    className={styles.selects}
                    disabled
                    suffixIcon={<ClockCircleOutlined />}
                    format="YYYY/MM/DD"
                  />
                </Form.Item>
                <Form.Item
                  label="项目概述"
                  name="projectOverview"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <TextArea placeholder="暂无数据" disabled rows={4} />
                </Form.Item>
              </div>
              <div className={styles.bottom_checkboxs}>
                <Form.Item name="ifStart">
                  <Checkbox
                    disabled
                    checked={ifStart}
                    onChange={e => {
                      setIfStart(e.target.checked);
                    }}
                  >
                    合同签署前开始实施
                  </Checkbox>
                </Form.Item>
                <Form.Item name="isDeptCooperation">
                  <Checkbox disabled checked={isCross} onChange={deptCheck}>
                    跨部门协作
                  </Checkbox>
                </Form.Item>
              </div>
            </div>
          </Panel>
        </Collapse>
        {isCross && (
          <Collapse
            defaultActiveKey={['2']}
            expandIconPosition="right"
            ghost={true}
          >
            <Panel
              header={
                <div className={styles.titles}>
                  协作部门信息
                  <div></div>
                </div>
              }
              key="2"
              className={classNames(styles.card)}
              id="23"
            >
              <div>
                <div className={styles.scroll_x}>
                  <div
                    className={styles.cooperation_dept_boxs}
                    style={{
                      width: `${
                        cooperationDeptList.length > 1
                          ? cooperationDeptList.length * 60
                          : 100
                      }%`,
                    }}
                  >
                    {cooperationDeptList.map((item, index) => (
                      <div
                        key={index}
                        style={{
                          width: `${
                            cooperationDeptList.length > 1
                              ? cooperationDeptList.length * 50 * 0.6
                              : 60
                          }%`,
                        }}
                        className={
                          cooperationDeptList.length > 1
                            ? styles.margin_right
                            : styles.margin_auto
                        }
                      >
                        <Form.Item
                          label="协作部门"
                          name={'departmentCooperation' + index}
                          rules={[
                            {
                              required: true,
                            },
                          ]}
                          wrapperCol={{ span: 17 }}
                        >
                          <Select
                            showSearch
                            defaultActiveFirstOption={false}
                            placeholder="暂无数据"
                            disabled
                            showArrow={false}
                            filterOption={false}
                            className={styles.selects}
                            notFoundContent={null}
                            value={item.departmentCooperation}
                            filterOption={(input, option) =>
                              option.children
                                .toLowerCase()
                                .indexOf(input.toLowerCase()) >= 0
                            }
                            onChange={value =>
                              cooperationDeptListChange({
                                value,
                                index,
                                key: 'departmentCooperation',
                              })
                            }
                          >
                            {deptList.map(item => (
                              <Option
                                value={`${item.deptFullName}@${item.id}`}
                                key={item.deptCode}
                              >
                                {item.deptFullName}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                        <Form.Item
                          label="协作部门职责"
                          name={'cooperationDutie' + index}
                          rules={[
                            {
                              required: true,
                            },
                          ]}
                          wrapperCol={{ span: 17 }}
                        >
                          <TextArea
                            placeholder="暂无数据"
                            onChange={e =>
                              cooperationDeptListChange({
                                value: e.target.value,
                                index,
                                key: 'cooperationDutie',
                              })
                            }
                            value={item.cooperationDutie}
                            disabled
                            rows={4}
                          />
                        </Form.Item>
                        <Form.Item
                          label="协作项目经理"
                          wrapperCol={{ span: 17 }}
                          name={'cooperationManager' + index}
                          rules={[
                            {
                              required: true,
                            },
                          ]}
                        >
                          <Select
                            showSearch
                            defaultActiveFirstOption={false}
                            placeholder="暂无数据"
                            disabled
                            showArrow={false}
                            className={styles.selects}
                            onChange={value =>
                              cooperationDeptListChange({
                                value,
                                index,
                                key: 'cooperationManager',
                              })
                            }
                            filterOption={false}
                            onSearch={handleSearch}
                            notFoundContent={null}
                          >
                            {nameList.length > 0 &&
                              nameList.map((item, index) => (
                                <Option
                                  value={`${item.employee_name}-${item.deptName}@${item.user_id}`}
                                  key={index}
                                >
                                  {item.employee_name}-{item.deptName}
                                </Option>
                              ))}
                          </Select>
                        </Form.Item>
                        <Form.Item
                          label="协作项目经理职责"
                          wrapperCol={{ span: 17 }}
                          name={'cooperationManagerDutie' + index}
                          rules={[
                            {
                              required: true,
                            },
                          ]}
                        >
                          <TextArea
                            placeholder="暂无数据"
                            onChange={e =>
                              cooperationDeptListChange({
                                value: e.target.value,
                                index,
                                key: 'cooperationManagerDutie',
                              })
                            }
                            disabled
                            rows={4}
                          />
                        </Form.Item>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </Panel>
          </Collapse>
        )}
        <Collapse
          defaultActiveKey={['1']}
          expandIconPosition="right"
          ghost={true}
        >
          <Panel
            header={<div className={styles.titles}>项目分类</div>}
            key="1"
            className={classNames(styles.card)}
          >
            <div>
              <div>
                <Form.Item
                  label="项目分类"
                  name="type"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Radio.Group onChange={changeManageType} disabled>
                    <Radio value={'manage'}>经营类</Radio>
                    <Radio value={'no_manage'}>非经营类</Radio>
                    <Radio value={'product_category'}>产品类</Radio>
                  </Radio.Group>
                </Form.Item>
                {/* <Form.Item
                  label="项目级别"
                  name="level"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    showSearch
                    defaultActiveFirstOption={false}
                    placeholder="暂无数据"
                    disabled
                    showArrow={false}
                    filterOption={false}
                    // onSearch={handleSearch}
                    notFoundContent={null}
                    className={styles.selects}
                  >
                    {newLevelList.length > 0 &&
                      newLevelList.map((item, index) => (
                        <Option value={item.code} key={item.code}>
                          {item.code}
                        </Option>
                      ))}
                  </Select>
                </Form.Item>
                {manageType === 'manage' && (
                  <Form.Item
                    label="收入分类"
                    name="inComeType"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  >
                    <Select
                      showSearch
                      defaultActiveFirstOption={false}
                      showArrow={false}
                      filterOption={false}
                      disabled
                      placeholder="暂无数据"
                      // onSearch={handleSearch}
                      onChange={InComeTypeChange}
                      notFoundContent={null}
                      className={styles.selects}
                    >
                      {revenueTypeList.length > 0 &&
                        revenueTypeList.map((item, index) => (
                          <Option value={item.code} key={item.name}>
                            {item.name}
                          </Option>
                        ))}
                    </Select>
                  </Form.Item>
                )} */}
                {(manageType === 'manage' || manageType === 'no_manage') && (
                  <Form.Item
                    label={<div>项目级别</div>}
                    name="level"
                    rules={[
                      {
                        required: type !== 'update' && type !== 'updateDraft',
                      },
                    ]}
                  >
                    <Select
                      showSearch
                      style={{ width: '80%' }}
                      defaultActiveFirstOption={false}
                      placeholder="选择项目级别"
                      disabled
                      showArrow={false}
                      filterOption={false}
                      // onSearch={handleSearch}
                      notFoundContent={null}
                      className={styles.selects}
                    >
                      {newLevelList.length > 0 &&
                        newLevelList.map((item, index) => (
                          <Option value={item.code} key={item.code}>
                            {item.code}
                          </Option>
                        ))}
                    </Select>
                  </Form.Item>
                )}
                {(manageType === 'manage' ||
                  manageType === 'product_category') && (
                  <Form.Item
                    label={
                      <div>
                        收入分类
                        <Tooltip
                          title={
                            <div>
                              1、一类项目：系统连接及运维服务
                              <br />
                              2、非一类项目：系统集成服务、产品和解决方案服务、软件外包开发服务
                              <br />
                            </div>
                          }
                        >
                          <QuestionCircleOutlined style={{ width: 20 }} />
                        </Tooltip>
                      </div>
                    }
                    name="inComeType"
                    rules={[
                      {
                        required: type !== 'update' && type !== 'updateDraft',
                      },
                    ]}
                  >
                    <Select
                      showSearch
                      defaultActiveFirstOption={false}
                      showArrow={false}
                      disabled
                      filterOption={false}
                      style={{ width: '80%' }}
                      placeholder="选择收入分类"
                      // onSearch={handleSearch}
                      onChange={InComeTypeChange}
                      notFoundContent={null}
                      className={styles.selects}
                    >
                      {revenueTypeList.length > 0 &&
                        revenueTypeList.map((item, index) => (
                          <Option value={item.code} key={item.name}>
                            {item.name}
                          </Option>
                        ))}
                    </Select>
                  </Form.Item>
                )}
              </div>
            </div>
          </Panel>
        </Collapse>
        <Collapse
          defaultActiveKey={['1']}
          expandIconPosition="right"
          ghost={true}
        >
          <Panel
            header={<div className={styles.titles}>项目成员</div>}
            key="1"
            className={classNames(styles.card)}
            id="24"
          >
            <div>
              <div>
                <Table
                  columns={columns}
                  dataSource={dataSource}
                  // scroll={{ x: 1000 }}
                  pagination={false}
                  // rowSelection={{ columnWidth: 20, ...rowSelection }}
                  // loading={loading}
                  size="middle"
                  className={styles.anTdTable}
                />
              </div>
            </div>
          </Panel>
        </Collapse>
        <Collapse
          defaultActiveKey={['1']}
          expandIconPosition="right"
          ghost={true}
        >
          <Panel
            header={
              <div className={styles.titles}>
                <div>
                  计划里程碑
                  <Tooltip
                    title={
                      <div>
                        鼠标移到菱形图标查看概述
                        <br />
                      </div>
                    }
                  >
                    <QuestionCircleOutlined style={{ width: 20 }} />
                  </Tooltip>
                </div>
              </div>
            }
            key="1"
            className={classNames(styles.card)}
            id="25"
          >
            <div>
              <div className={styles.typeBox}>
                <div>计划里程碑</div>
                {planData &&
                  planData.map((item, index) => (
                    <div className={styles.toneItems} key={index}>
                      <div>
                        <Tooltip
                          title={
                            item.facts && (
                              <div>
                                <div>{item.itemStageName}</div>
                                <div>
                                  <p>概述：{item.facts}</p>
                                </div>
                              </div>
                            )
                          }
                        >
                          <img
                            src={hollow_icon}
                            alt=""
                            className={styles.hoverImg}
                          />
                        </Tooltip>
                        <div className={styles.iconLine}></div>
                      </div>
                      <div>
                        <div>
                          {item.itemStageName}
                          {milestoneInfo.map(value => {
                            if (item.itemStageType === value.itemStageType) {
                              return (
                                <Tooltip
                                  title={
                                    <div>
                                      {value.info}
                                      <br />
                                    </div>
                                  }
                                >
                                  <QuestionCircleOutlined
                                    style={{ marginLeft: 5 }}
                                  />
                                </Tooltip>
                              );
                            }
                          })}
                        </div>
                        {item.planEndTime ? (
                          <p className={styles.margin_bottom0}>
                            {moment(item.planEndTime).format('YYYY-MM-DD')}
                          </p>
                        ) : (
                          <p
                            className={styles.hoverP}
                            onClick={() => editMilesTone(index)}
                          >
                            点此设置
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </Panel>
        </Collapse>
        <Collapse
          expandIconPosition="right"
          defaultActiveKey={['1']}
          ghost={true}
        >
          <Panel
            header={<div className={styles.titles}>项目风险</div>}
            key="1"
            className={classNames(styles.card)}
            id="26"
          >
            <div>
              <div>
                {riskList && riskList.length > 0 ? (
                  riskList.map((item, index) => (
                    <div className={styles.risk_content} key={index}>
                      <div>{item.risk}</div>
                      <div>{item.solutions}</div>
                    </div>
                  ))
                ) : (
                  <p style={{ textAlign: 'center', width: '100%' }}>暂无数据</p>
                )}
              </div>
              <div></div>
            </div>
          </Panel>
        </Collapse>
        <Collapse
          defaultActiveKey={['1']}
          expandIconPosition="right"
          ghost={true}
        >
          <Panel
            header={
              <div className={styles.money_title} id="32">
                <div>项目计划收入(税后)</div>
                <div>收入总计：{Number(itemPlanIncome).toFixed(2)}万</div>
              </div>
            }
            key="1"
            showArrow={false}
            className={classNames(styles.card)}
            id="3"
          >
            <div>
              <div>
                <div className={styles.project_cost}>
                  <div
                    className={styles.project_cost_left}
                    style={{ margin: ' 0 auto' }}
                  >
                    <div className={styles.money_title}>
                      <div>计划收入</div>
                    </div>
                    <div>
                      {yearMoneyList &&
                        yearMoneyList.length > 0 &&
                        yearMoneyList.map(item => {
                          return (
                            item.year &&
                            item.money !== 0 && (
                              <div>
                                {item.year}年计划收入:
                                {Number(item.money).toFixed(2)}万
                              </div>
                            )
                          );
                        })}
                    </div>
                    {plannedRevenueInputList.length > 0 &&
                      plannedRevenueInputList.map((item, index) => (
                        <>
                          <div
                            className={classNames(styles.income_year)}
                            key={index}
                          >
                            <div></div>
                          </div>
                          {item.item.map((items, indexs) => (
                            <div
                              className={classNames(
                                styles.income_year,
                                index < plannedRevenueInputList.length - 1 &&
                                  plannedRevenueInputList.length > 1 &&
                                  indexs === item.item.length - 1 &&
                                  styles.border,
                              )}
                              key={indexs}
                            >
                              <div>
                                <div>
                                  <div>{indexs + 1}</div>
                                </div>
                              </div>
                              <div>
                                <DatePicker
                                  suffixIcon={<ClockCircleOutlined />}
                                  disabled
                                  value={
                                    (items.planTime &&
                                      moment(items.planTime)) ||
                                    ''
                                  }
                                  placeholder="暂无数据"
                                  onChange={(date, deteString) =>
                                    datePickerChange({
                                      date,
                                      deteString,
                                      index,
                                      indexs,
                                      key: 'plannedRevenueInputList',
                                    })
                                  }
                                  className={styles.selects}
                                />
                              </div>
                              <div className={styles.last_div}>
                                <Input
                                  min={0}
                                  className={styles.selects}
                                  value={items.planIncome}
                                  disabled
                                  placeholder="暂无数据"
                                  onChange={e =>
                                    plannedRevenueInputListMoneyChange({
                                      value: e.target.value,
                                      index,
                                      indexs,
                                    })
                                  }
                                  suffix="万元"
                                />
                              </div>
                            </div>
                          ))}
                        </>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          </Panel>
        </Collapse>
        <Collapse
          defaultActiveKey={['1']}
          expandIconPosition="right"
          ghost={true}
        >
          <Panel
            header={
              <div className={styles.money_title}>
                <div>项目成本预算</div>
                <div>预算总计：{Number(itemCostBudgeting).toFixed(2)}万</div>
              </div>
            }
            key="1"
            showArrow={false}
            className={classNames(styles.card)}
            id="33"
          >
            <div>
              <div>
                <div
                //  className={styles.scroll_x}
                >
                  <div
                  // className={styles.project_cost}
                  // style={{
                  //   width: `${
                  //     cooperationDeptInputList.length > 1
                  //       ? (cooperationDeptInputList.length + 1) * 50
                  //       : 100
                  //   }%`,
                  // }}
                  >
                    <div
                    // className={classNames(
                    //   styles.project_cost_left,
                    //   cooperationDeptInputList.length > 1 && isCross
                    //     ? styles.margin_right
                    //     : styles.margin_auto,
                    // )}
                    >
                      <div>{projectName}</div>
                      {deptInputList.length > 0 &&
                        deptInputList.map((item, index) => (
                          <>
                            <div className={styles.income_year} key={index}>
                              <div>
                                <div>部门年度预算：{item.money}万</div>
                              </div>
                              <div className={styles.last_div}>
                                <DatePicker
                                  suffixIcon={<ClockCircleOutlined />}
                                  placeholder="暂无数据"
                                  disabled
                                  value={(item.year && moment(item.year)) || ''}
                                  className={styles.selects}
                                  onChange={(date, deteString) =>
                                    datePickerChange({
                                      date,
                                      deteString,
                                      index,
                                      indexs: -1,
                                      key: 'deptInputList',
                                    })
                                  }
                                  picker="year"
                                />
                              </div>
                            </div>
                            {item.item.map((items, indexs) => (
                              <div
                                className={classNames(
                                  styles.income_year,
                                  index < deptInputList.length - 1 &&
                                    deptInputList.length > 1 &&
                                    indexs === item.item.length - 1 &&
                                    styles.border,
                                )}
                                key={indexs}
                              >
                                <div>
                                  <div>
                                    <Select
                                      style={{ width: 100 }}
                                      className={styles.selects}
                                      placeholder="暂无数据"
                                      value={items.planContent}
                                      disabled
                                      showSearch
                                      onChange={value =>
                                        itemCostTypeChange({
                                          value,
                                          index,
                                          indexs,
                                          type: 'deptInputList',
                                        })
                                      }
                                    >
                                      {inComeTypeList.length > 0 &&
                                        inComeTypeList.map((item, index) => (
                                          <Option
                                            value={`${item.name}@${item.code}`}
                                            key={index}
                                          >
                                            {item.name}
                                          </Option>
                                        ))}
                                    </Select>
                                  </div>
                                </div>
                                <div>
                                  <Input
                                    min={0}
                                    className={styles.selects}
                                    placeholder="暂无数据"
                                    value={items.planCost}
                                    disabled
                                    onChange={e =>
                                      deptInputListMoneyChange({
                                        value: e.target.value,
                                        index,
                                        indexs,
                                      })
                                    }
                                    suffix="万元"
                                  />
                                </div>
                                <div className={styles.last_div}>
                                  <Input
                                    value={items.proof}
                                    min={0}
                                    className={styles.selects}
                                    onChange={e =>
                                      deptInputListChange({
                                        value: e.target.value,
                                        index,
                                        indexs,
                                      })
                                    }
                                    disabled
                                    placeholder="暂无数据"
                                  />
                                </div>
                              </div>
                            ))}
                          </>
                        ))}
                    </div>
                    {isCross &&
                      cooperationDeptInputList.length > 0 &&
                      cooperationDeptInputList.map((item, index) => {
                        const { content } = item;
                        return (
                          <div
                            className={classNames(
                              styles.project_cost_right,
                              styles.margin_right,
                            )}
                          >
                            <div>{item.name}</div>
                            {content.length > 0 &&
                              content.map((contentItem, contentIndex) => (
                                <>
                                  <div
                                    className={styles.income_year}
                                    key={contentIndex}
                                  >
                                    <div>
                                      <div>
                                        部门年度预算：{contentItem.money}万
                                      </div>
                                    </div>
                                    <div className={styles.last_div}>
                                      <DatePicker
                                        suffixIcon={<ClockCircleOutlined />}
                                        value={
                                          (contentItem.year &&
                                            moment(contentItem.year)) ||
                                          ''
                                        }
                                        disabled
                                        className={styles.selects}
                                        onChange={(date, deteString) =>
                                          datePickerChange({
                                            date,
                                            deteString,
                                            index,
                                            indexs: -1,
                                            key: 'cooperationDeptInputList',
                                            contentIndex,
                                          })
                                        }
                                        picker="year"
                                      />
                                    </div>
                                  </div>

                                  {contentItem.item.map((items, indexs) => (
                                    <div
                                      className={classNames(
                                        styles.income_year,
                                        index < content.length - 1 &&
                                          content.length > 1 &&
                                          indexs ===
                                            contentItem.item.length - 1 &&
                                          styles.border,
                                      )}
                                      key={indexs}
                                    >
                                      <div>
                                        <div>
                                          <Select
                                            style={{ width: 100 }}
                                            value={items.planContent}
                                            className={styles.selects}
                                            onChange={value =>
                                              itemCostTypeChange({
                                                value,
                                                index,
                                                indexs,
                                                type:
                                                  'cooperationDeptInputList',
                                                contentIndex,
                                              })
                                            }
                                            placeholder="暂无数据"
                                            disabled
                                            showSearch
                                          >
                                            {inComeTypeList.length > 0 &&
                                              inComeTypeList.map(
                                                (item, index) => (
                                                  <Option
                                                    value={`${item.name}@${item.code}`}
                                                    key={index}
                                                  >
                                                    {item.name}
                                                  </Option>
                                                ),
                                              )}
                                          </Select>
                                        </div>
                                      </div>
                                      <div>
                                        <Input
                                          min={0}
                                          className={styles.selects}
                                          value={items.planCost}
                                          disabled
                                          placeholder="暂无数据"
                                          defaultValue={items.money}
                                          onChange={e =>
                                            cooperationDeptInputListMoneyChange(
                                              {
                                                value: e.target.value,
                                                contentIndex,
                                                index,
                                                indexs,
                                              },
                                            )
                                          }
                                          suffix="万元"
                                        />
                                      </div>
                                      <div className={styles.last_div}>
                                        <Input
                                          value={items.proof}
                                          min={0}
                                          className={styles.selects}
                                          onChange={e =>
                                            cooperationDeptInputListChange({
                                              value: e.target.value,
                                              contentIndex,
                                              index,
                                              indexs,
                                            })
                                          }
                                          disabled
                                          placeholder="暂无数据"
                                        />
                                      </div>
                                    </div>
                                  ))}
                                </>
                              ))}
                          </div>
                        );
                      })}
                  </div>
                </div>
              </div>
            </div>
          </Panel>
        </Collapse>
        <Collapse
          defaultActiveKey={['1']}
          expandIconPosition="right"
          ghost={true}
        >
          <Panel
            header={
              <div className={styles.money_title}>
                <div>项目利润</div>
                <div>
                  预估利润：
                  {(Number(itemPlanIncome) - Number(itemCostBudgeting)).toFixed(
                    2,
                  )}
                  万
                </div>
              </div>
            }
            key="1"
            showArrow={false}
            className={classNames(styles.card, styles.card_last)}
            id="44"
          >
            <Form.Item label="利润率" name="profitRate">
              <InputNumber
                addonAfter="%"
                controls={false}
                precision={2}
                disabled={true}
              />
            </Form.Item>
          </Panel>
        </Collapse>

        <div className={styles.submit_boxs}>
          <div>
            <div>
              {needPDF && <Button onClick={() => savePDF()}>导出为pdf</Button>}
              {type !== 'lookOver' ? (
                <Button
                  type="primary"
                  onClick={() => showMoald({ title: '审核' })}
                >
                  审核
                </Button>
              ) : (
                <Button type="primary" onClick={() => window.history.go(-1)}>
                  返回
                </Button>
              )}
            </div>
          </div>
        </div>
      </Form>

      <Modal
        title={`${modalTitle}`}
        visible={visibleModal}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
      >
        <Form
          layout="vertical"
          form={modalFrom}
          name="nest-messages"
          onFinish={onFinishMoadlFormData}
        >
          <Form.Item
            label="审核状态"
            name="submitType"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Radio.Group
              className={styles.grasp_Box}
              onChange={e => changeSubmitType(e.target.value)}
            >
              <Radio value={0}>通过</Radio>
              <Radio value={1}>不通过</Radio>
            </Radio.Group>
          </Form.Item>
          {needNumber && submitType === 0 && (
            <Form.Item
              label="项目立项审批单编号"
              name="itemGroupNum"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input className={styles.selects} />
            </Form.Item>
          )}
          <Form.Item
            label="审核意见"
            name="trace"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <TextArea className={styles.selects} rows={4} />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Button type="primary" htmlType="submit" disabled={disabled}>
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
