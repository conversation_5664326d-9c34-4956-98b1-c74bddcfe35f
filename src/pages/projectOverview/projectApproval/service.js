import request from '@/utils/request';
import { BASE_URl } from "../../../utils/constant";

//费用类型下拉框信息
export function getInComeType() {
  return request(`${BASE_URl}/Finance/getInComeType`, {
    method: 'post',
  });
}

//获取部门下拉框数据
export function getDeptListInfo() {
  return request(`${BASE_URl}/dept/SWCARESdeptAll`, {
    method: 'POST',
  });
}

//获取姓名下拉框数据
export function getNameListInfo(params) {
  return request(`${BASE_URl}/user/selectSysUserPageData`, {
    method: 'POST',
    data: params,
  });
}

//  项目列表标题行下拉框信息
export function getSearchListInfo() {
  return request(`${BASE_URl}/projectList/getSearchListInfo`, {
    method: 'POST',
  });
}

//  保存
export function inProjectStand(params) {
  const { formData, type } = params;
  return request(`${BASE_URl}/projectStand/inProjectStand?type=${type}`, {
    data: formData,
    method: 'POST',
  });
}

export function gitItemInfo(params) {
  const { id, trial_ids } = params;
  return request(
    `${BASE_URl}/projectStand/gitItemInfo?id=${id}&trial_ids=${trial_ids}`,
    {
      method: 'post',
    },
  );
}

export function itemSubmit(params) {
  const { itemId, submitType, trace, trialId, lowerId, itemGroupNum } = params;
  return request(
    `${BASE_URl}/projectStand/itemSubmit?itemId=${itemId}&submitType=${submitType}&trace=${trace}&trialId=${trialId}&lowerId=${lowerId}&itemGroupNum=${itemGroupNum ||
      ''}`,
    {
      method: 'post',
    },
  );
}
