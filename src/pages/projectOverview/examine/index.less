.card {
  position: relative;
  background: #ffffff;
  border: 1px solid #edeff2;
  box-sizing: border-box;
  padding: 13px 24px;
  box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
  border-radius: 8px;
  margin: 0 auto;
  margin-top: 24px;

  .titles {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    color: #333333;
    display: flex;
    justify-content: space-between;

    .name {
      font-family: OPPOSans;
      font-style: normal;
      font-weight: bold;
      font-size: 16px;
      line-height: 24px;
      color: #333333;
      margin-left: 5px;
    }

    .inputName {
      box-sizing: border-box;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      margin-right: 8px;
    }
  }

  .content {
  }

  .forms {
    // margin: 0 auto;
    // width: 800px;
  }

  input,
  button,
  li,
  textarea,
  .selects,
  .ant-input-affix-wrapper,
  .selects > div {
    border-color: #edeff2 !important;
    box-sizing: border-box;
    border-radius: 6px !important;
  }

  label {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #333333;
  }

  .anTdTable tr > th,
  .anTdTable tr > td {
    border-bottom: 0px;
    padding: 10px 8px !important;
  }

  .anTdTable tr > th {
    font-size: 12px;
    line-height: 20px;
    color: #7a7a7a;

    > div > div {
      padding: 10px 16px;
    }
  }

  .typeBox {
    width: 200%;
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
    height: 120px;
    // margin: 0 -26px;

    > div:nth-child(9) > div > .hoverBox {
      left: -160px;
    }

    > div:first-child {
      border: 1px solid #edeff2;
      box-sizing: border-box;
      border-radius: 6px;
      width: 100px;
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #666666;
      box-shadow: inset 0px -1px 0px #edeff2;
      text-align: center;
      line-height: 120px;
      margin-right: 15px;
    }

    > div:last-child {
      > div > div:last-child {
        display: none;
      }
    }
  }

  .iconLine {
    width: 80px;
    border-bottom: 1px dashed #3d7bf8;
    box-shadow: inset 0px -1px 0px #edeff2;
    height: 0px;
    margin: 0 8px;
  }

  .toneItems {
    position: relative;
    margin-right: 20px;
    width: 92px;
    margin-top: 10px;

    > div:first-child {
      position: absolute;
      left: 40px;
      display: flex;
      align-items: center;
    }

    .endTime {
      position: absolute;
      bottom: -20px;
      left: 13px;
      font-style: normal;
      font-weight: 500;
      font-size: 10px;
      line-height: 20px;
      color: rgba(0, 0, 0, 0.65);
      margin-bottom: 3px;
    }

    > div:last-child {
      text-align: center;
      margin-top: 20px;

      div {
        font-family: OPPOSans;
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        color: #333333;
      }

      p {
        font-style: normal;
        font-weight: 500;
        font-size: 10px;
        line-height: 18px;
        text-align: center;
      }
    }
  }

  .hoverImg {
    cursor: pointer;
  }

  .risk_content {
    box-shadow: inset 0px -1px 0px #edeff2;
    word-wrap: break-word;
    position: relative;
    padding-right: 25px;

    > div:first-child {
      font-style: normal;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      color: #333333;
      word-wrap: break-word;
    }

    > div:last-child {
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #7a7a7a;
      word-wrap: break-word;
    }

    > span {
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #3d7bf8;
      position: absolute;
      right: 0px;
      top: 10px;
      background: #ffffff;
      box-shadow: -8px 0px 8px #ffffff;
      cursor: pointer;
      transition: 0.5s;
      opacity: 0;

      > span:last-child {
        margin-left: 10px;
        color: #e8684a;
      }
    }
  }

  .risk_content:hover > span {
    opacity: 1;
  }

  .project_cost {
    display: flex;
    line-height: 32px;

    > div {
      width: 80%;
    }

    .border {
      padding-bottom: 16px;
      border-bottom: 1px solid #edeff2;
    }

    .hide_icon {
      opacity: 0;
    }

    .project_cost_year {
      display: flex;
      line-height: 32px;
      justify-content: space-between;
      flex-wrap: nowrap;
      margin-bottom: 16px;

      > div {
        width: 30%;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
      }

      > div:first-child {
        svg {
          margin-right: 10px;
        }
      }

      > .last_div {
        width: 35%;

        > div {
          margin-left: 10px;
        }
      }
    }

    .icons {
      line-height: 32px;
      cursor: pointer;
    }
  }

  .project_cost_left,
  .project_cost_right {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
    padding: 14px 12px;

    > div:first-child {
      font-weight: bold;
      font-size: 14px;
      line-height: 22px;
      color: #333333;
      margin-bottom: 13px;
    }
  }
}

.project_cost_right {
  margin: 0 0 0 auto;
}

.anchor {
  background: #ffffff;
  width: 100px;
  position: fixed;
  right: 8px;
  top: 80px;

  a {
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    color: #7a7a7a;
  }

  .ant-anchor-link .ant-anchor-link {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
  }

  div {
    padding: 0px 0px 0px 2px;
    padding-top: 0px !important;
    padding-bottom: 0px !important;

    a {
      margin: 0 0 0 0;
    }

    div {
      padding: 0px 0px 0px 5px;
      padding-top: 0px !important;
      padding-bottom: 0px !important;

      > div {
        padding-top: 0px !important;
        padding-bottom: 0px !important;
      }
    }
  }
}

.bottom_checkboxs {
  display: flex;
  flex-direction: row-reverse;
  flex-wrap: nowrap;
  box-shadow: inset 0px 1px 0px #edeff2;

  > div {
    white-space: nowrap;
    align-self: center;
    margin: 6px 0px;

    span {
      font-family: OPPOSans;

      white-space: nowrap;
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #333333;
    }
  }
}

.submit_boxs {
  width: 100%;
  position: fixed;
  bottom: 0;
  right: 0;
  background: #ffffff;
  /* 分割线/顶部 */

  box-shadow: inset 0px 1px 0px #edeff2;
  margin-left: 256px;

  > div {
    margin-left: 256px;

    > div {
      width: 960px;
      margin: 0 auto;
      padding: 8px 0;
      display: flex;
      flex-direction: row-reverse;
    }
  }

  button {
    border-color: #edeff2 !important;
    box-sizing: border-box;
    border-radius: 6px !important;
    margin-left: 16px;
  }
}

.card_last {
  margin-bottom: 200px;
}

.grasp_Box {
  display: flex;
  flex-wrap: wrap;

  > label {
    width: 50%;
  }
}

.grasp_info {
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 22px;
  color: #a3a3a3;
}

.scroll_x {
  overflow-x: auto;
}

.cooperation_dept_boxs {
  display: flex;

  > div {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
    padding: 14px 12px;
  }
}

.delete_cooperation_dept {
  float: right;

  span {
    color: #e8684a;
  }
}

.income_year {
  display: flex;
  line-height: 32px;
  justify-content: space-between;
  white-space: nowrap;
  margin-bottom: 16px;

  > div {
    display: flex;
    margin-right: 10px;

    svg {
      margin-right: 10px;
    }

    white-space: nowrap;
  }

  .last_div {
    // width: 50%;
    justify-content: flex-end;

    svg {
      margin-right: 0px;
      margin-left: 10px;
    }
  }
}

.margin_auto {
  margin: 0 auto;
}

.margin_right {
  margin-right: 16px;
}

.examine {
  display: flex;
  justify-content: space-between;
  padding: 0 24px;

  > div {
    width: 49%;
  }
}

.top_title {
  margin-top: 0;
  position: relative;
  background: #ffffff;
  border: 1px solid #edeff2;
  box-sizing: border-box;
  padding: 3px 24px;
  box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
  display: flex;
  line-height: 50px;
  height: 50px;
  > div {
    width: 50%;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 40px;
    text-align: center;
    color: #000000;

    > div {
      font-weight: 500;
      font-size: 12px;
      line-height: 20px;
      text-align: center;
      color: #7a7a7a;
    }
  }
}
