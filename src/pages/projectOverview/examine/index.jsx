import React, { useState, useEffect } from 'react';
import styles from './index.less';

import Child from './component/index';
import classNames from 'classnames';

export default props => {
  const { location } = props;
  return (
    <div id="scrollDIV">
      <div className={classNames(styles.top_title)}>
        <div>变更前</div>
        <div>变更后</div>
      </div>
      <div className={styles.examine}>
        <div>
          <Child
            id={location.query.id}
            trial_ids={location.query.trial_ids}
            lookOver={location.query.lookOver}
            type={1}
          ></Child>
        </div>
        <div>
          <Child
            id={location.query.id}
            trial_ids={location.query.trial_ids}
            lookOver={location.query.lookOver}
            type={0}
          ></Child>
        </div>
      </div>
    </div>
  );
};
