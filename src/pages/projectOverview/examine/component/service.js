import request from '@/utils/request';
import { BASE_URl } from "../../../../utils/constant";

//费用类型下拉框信息
export function getInComeType() {
  return request(`${BASE_URl}/Finance/getInComeType`, {
    method: 'post',
  });
}

//获取部门下拉框数据
export function getDeptListInfo() {
  return request(`${BASE_URl}/dept/SWCARESdeptAll`, {
    method: 'POST',
  });
}

//获取姓名下拉框数据
export function getNameListInfo(params) {
  return request(`${BASE_URl}/user/selectSysUserPageData`, {
    method: 'POST',
    data: params,
  });
}

//  项目列表标题行下拉框信息
export function getSearchListInfo() {
  return request(`${BASE_URl}/projectList/getSearchListInfo`, {
    method: 'POST',
  });
}

//  保存
export function inProjectStand(params) {
  const { formData, type } = params;
  return request(`${BASE_URl}/projectStand/inProjectStand?type=${type}`, {
    data: formData,
    method: 'POST',
  });
}

//编辑信息
export function gitItemInfo(params) {
  const { type, id } = params;
  if (type === 'update') {
    return request(`${BASE_URl}/projectStand/getUpItemInfo?id=${id}`, {
      method: 'post',
    });
  } else {
    return request(`${BASE_URl}/projectStand/gitItemInfo?id=${id}`, {
      method: 'post',
    });
  }
}

export function getItemInfoUp(params) {
  const { id, trial_ids } = params;
  return request(
    `${BASE_URl}/projectStand/getItemInfoUp?id=${id}&trial_ids=${trial_ids}`,
    {
      method: 'post',
    },
  );
}

export function itemSubmit(params) {
  const { itemId, submitType, trace, trialId, lowerId } = params;
  return request(
    `${BASE_URl}/projectStand/itemSubmitUpdate?itemId=${itemId}&submitType=${submitType}&trace=${trace}&trialId=${trialId}&lowerId=${lowerId}`,
    {
      method: 'post',
    },
  );
}
//查询项目审批流程图信息
export function getItemTrial(id) {
  return request(`${BASE_URl}/projectStand/getItemTrial?item_id=${id}`, {
    method: 'post',
  });
}
