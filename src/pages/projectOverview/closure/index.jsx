import React, { useState, useEffect } from 'react';
import moment from 'moment';
import styles from './index.less';
import {
  Button,
  Modal,
  Anchor,
  Table,
  Collapse,
  Checkbox,
  Form,
  Tooltip,
  Input,
  Select,
  Radio,
  Row,
  Col,
  DatePicker,
  message,
  Upload,
  Progress,
  Popconfirm,
  Steps,
} from 'antd';
import {
  InfoCircleOutlined,
  QuestionCircleOutlined,
  UserOutlined,
  CloseOutlined,
  ClockCircleOutlined,
  PlusOutlined,
  UpCircleOutlined,
  DownCircleOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { getItemEndTitle, inItemEnd } from './service';
import { history } from 'umi';
import reqwest from 'reqwest';
import classNames from 'classnames';
import { BASE_URl } from '../../../utils/constant';
const { RangePicker } = DatePicker;
const { TextArea, Search } = Input;
const { Link } = Anchor;
const { Step } = Steps;
const { Panel } = Collapse;
const { confirm } = Modal;
const { Option } = Select;
const dateFormat = 'YYYY-MM-DD';
export default props => {
  const { location } = props;
  const [disabled, setDisabled] = useState(false);

  const [form] = Form.useForm();

  const [fileList, setFileList] = useState([]);
  const [uploadProps, setUploadProps] = useState({
    onRemove: file => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: file => {
      setFileList([...fileList, file]);
      return false;
    },
    fileList,
  });

  useEffect(() => {
    setUploadProps({
      onRemove: file => {
        const index = fileList.indexOf(file);
        const newFileList = fileList.slice();
        newFileList.splice(index, 1);
        setFileList(newFileList);
      },
      beforeUpload: file => {
        setFileList([...fileList, file]);
        return false;
      },
      fileList,
    });
  }, [fileList]);

  const [contractList, setContractList] = useState([
    {
      itemNum: '', //项目编号
      type: '', //合同类型：0收入合同、1成本合同
      conNum: '', //合同编号
      conName: '', //合同名称
      groupNum: '', //呈批件编号
      groupName: '', //呈批件名称
      startTime: '', //合同开始时间
      endTime: '', //合同结束时间
      conState: '', //执行情况：0执行完成、1未执行完成
      remarks: '', //合同备注
      fileList: [],
    },
  ]);

  const oucomeColumns = [
    {
      title: '费用类别',
      dataIndex: 'cost_type',
      // filterIcon: filtered => handleGetIcon(filtered),
      // filterDropdown: radioGrouop(TypeData),
    },
    {
      title: '实际成本',
      dataIndex: 'plan_cost',
      render: item => item && <span>{item}W</span>,
    },
    {
      title: '项目预算',
      dataIndex: 'actual_cost',
      render: item => item && <span>{item}W</span>,
    },
    {
      title: '成本占比',
      dataIndex: 'zb',
      render: item =>
        item && (
          <Tooltip
            placement="topLeft"
            title={`${(item !== 0 && Number(item).toFixed(2)) || 0}%`}
          >
            <Progress
              percent={item}
              status="normal"
              format={() => `${(item !== 0 && Number(item).toFixed(2)) || 0}%`}
              style={{ width: '70%' }}
            />
          </Tooltip>
        ),
    },
  ];
  const [oucomeInfo, setOucomeInfo] = useState([]);
  const [titleInfo, setTitleInfo] = useState({});

  useEffect(() => {
    if (location.query.itemNo) {
      getTitleInfo(location.query.itemNo);
    }
  }, []);
  const getTitleInfo = async value => {
    const { data, code } = await getItemEndTitle(value);
    if (code === 200) {
      setTitleInfo(data);
      setOucomeInfo(data.costInfo);
    }
  };

  const [saveButtonDisable, setSaveButtonDisable] = useState(false);
  const checkboxChange = value => {
    setSaveButtonDisable(value);
  };

  const addContractList = e => {
    setContractList([
      ...contractList,
      {
        itemNum: '', //项目编号
        type: '', //合同类型：0收入合同、1成本合同
        conNum: '', //合同编号
        conName: '', //合同名称
        groupNum: '', //呈批件编号
        groupName: '', //呈批件名称
        startTime: '', //合同开始时间
        endTime: '', //合同结束时间
        conState: '', //执行情况：0执行完成、1未执行完成
        remarks: '',
        fileList: [],
      },
    ]);
    e.stopPropagation();
  };

  const contractListChange = porps => {
    const { key, value, index } = porps;
    const list = contractList;
    list[index][key] = value;
    setContractList([...list]);
  };

  const deleteContractList = index => {
    const list = contractList;
    list.splice(index, 1);
    setContractList([...list]);
  };

  const onFinishFormData = value => {
    if (!saveButtonDisable) {
      message.error('请勾选承诺事项！');
      return;
    }
    let notContract = false;
    const itemNum = location.query.itemNo;
    const list = contractList;
    let notItemNum = false;
    list.forEach((item, index) => {
      if (item.conNum === '' && item.fileList.length !== 0) {
        message.error('传合同附件，必须填写对应的合同编号！');
        notItemNum = true;
      }
    });
    if (!notItemNum) {
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      contractList.forEach(item => {
        if (
          item.itemNum === '' &&
          item.type === '' &&
          item.conNum === '' &&
          item.conName === '' &&
          item.groupNum === '' &&
          item.startTime === '' &&
          item.endTime === '' &&
          item.conState === '' &&
          item.remarks === '' &&
          fileList.length === 0
        ) {
          notContract = true;
        }
      });
      setDisabled(true);
      //客户满意度附件
      fileList.forEach(file => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', '1');
        formData.append('item_num', itemNum);
        formData.append('file_name', file.name);

        reqwest({
          url: BASE_URl + '/File/uploadUserOpinion',
          method: 'post',
          headers: {
            isToken: false,
            Authorization: `Bearer ${userInfo.access_token}`,
          },
          data: formData,
          processData: false,
          success: () => {},
          error: () => {
            message.error('客户满意度附件上传失败');
          },
        });
      });
      //合同附件
      contractList.forEach(item => {
        item.fileList.forEach(file => {
          const formData = new FormData();
          formData.append('file', file);
          formData.append('type', '2');
          formData.append('item_num', item.conNum);
          formData.append('file_name', file.name);
          reqwest({
            url: BASE_URl + '/File/uploadUserOpinion',
            method: 'post',
            headers: {
              isToken: false,
              Authorization: `Bearer ${userInfo.access_token}`,
            },
            data: formData,
            processData: false,
            success: () => {},
            error: () => {
              message.error('合同附件上传失败');
            },
          });
        });
      });
      if (notContract) {
        save({
          ...value,
          itemNum: itemNum,
        });
      } else {
        save({
          ...value,
          itemNum: itemNum,
          itemContrat: list,
        });
      }
    }
  };

  const save = async value => {
    const { data, code, msg } = await inItemEnd(value);
    if (code === 200) {
      message.success(msg || '成功');
      history.replace('/workbench');
    } else {
      message.error(msg || '失败');
      setDisabled(false);
    }
  };

  const getPorps = index => {
    const onRemove = file => {
      const fileIndex = contractList[index].fileList.indexOf(file);
      const newFileList = contractList[index].fileList.slice();
      newFileList.splice(fileIndex, 1);
      let newContractList = contractList;
      newContractList[index].fileList = newFileList;
      setContractList([...newContractList]);
    };
    const beforeUpload = file => {
      let newContractList = contractList;
      newContractList[index].fileList.push(file);
      setContractList([...newContractList]);
      return false;
    };
    const fileList = contractList[index].fileList;

    return { onRemove, beforeUpload, fileList };
  };
  return (
    <div>
      <div className={classNames(styles.card)}>
        <Steps current={0} size="small">
          <Step title="申请结项" />
          <Step title="结项审核" />
          <Step title="项目结项" />
        </Steps>
      </div>
      <div className={classNames(styles.card)}>
        <nav>
          <div className={styles.NavLeft}>
            <div className={styles.navTitle}>{titleInfo.name}</div>
            <div className={styles.navName}>
              <span>{titleInfo.item_num}</span>
              <span>{titleInfo.dept}</span>
              <span>{titleInfo.manager}</span>
            </div>
            <div>
              <span style={{ marginRight: 5 }}>
                {titleInfo.in_come_type || '-'}
              </span>
              <span style={{ marginRight: 5 }}>{titleInfo.type || '-'}</span>
              <span style={{ marginRight: 5 }}>{titleInfo.level || '-'}</span>
            </div>
            <div className={styles.navTime}>
              立项批准时间：{titleInfo.create_time}
            </div>
            <div className={styles.navTime}>
              计划起止时间：{titleInfo.start_time}至{titleInfo.end_time}
            </div>
          </div>
          {/* <div className={styles.NavRight}>
            <div className={styles.tags}>
              <div>
                项目完成
              </div>
              <div>
                项目终止
              </div>
            </div>
          </div> */}
        </nav>
      </div>
      <Form
        className={styles.forms}
        // initialValues={fromData}
        colon={false}
        // layout="vertical"
        form={form}
        labelAlign="right"
        name="nest-messages"
        scrollToFirstError={true}
        onFinish={onFinishFormData}
        labelCol={{ span: 7 }}
        wrapperCol={{ span: 13 }}
      >
        <Collapse
          defaultActiveKey={['1']}
          expandIconPosition="right"
          ghost={true}
        >
          <Panel
            header={<div className={styles.titles}>项目执行概况</div>}
            key="1"
            id="22"
            className={classNames(styles.card)}
          >
            <Form.Item
              label="申请结项类型"
              name="type"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Radio.Group className={styles.grasp_Box}>
                <Radio value={'0'}>项目完成</Radio>
                <Radio value={'1'}>项目终止</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="完成概述" name="overview">
              <TextArea placeholder="请填写完成概述" rows={3} />
            </Form.Item>
            <Form.Item label="实施过程评价" name="implEvaluate">
              <TextArea placeholder="请填写实施过程评价" rows={3} />
            </Form.Item>
            <Form.Item label="客户满意度及意见" name="userOpinion">
              <TextArea placeholder="请填写客户满意度及意见" rows={3} />
            </Form.Item>
            <div className={styles.uplod_list}>
              <Upload {...uploadProps}>
                <Button type="file">
                  <CopyOutlined />
                  选择结项附件
                </Button>
              </Upload>
            </div>
            <Form.Item label="质量进度成本控制情况" name="situation">
              <TextArea placeholder="请填写质量进度成本控制情况" rows={3} />
            </Form.Item>
            <Form.Item label="滞留问题及影响" name="influence">
              <TextArea placeholder="请填写滞留问题及影响" rows={3} />
            </Form.Item>
            <Form.Item label="有待提高的方面" name="hoist">
              <TextArea placeholder="请填写有待提高的方面" rows={3} />
            </Form.Item>
            <Form.Item label="经验与教训" name="experience">
              <TextArea placeholder="请填写经验与教训" rows={3} />
            </Form.Item>
          </Panel>
        </Collapse>

        <Collapse
          defaultActiveKey={['1']}
          expandIconPosition="right"
          ghost={true}
        >
          <Panel
            header={
              <div className={styles.titles}>
                项目合同执行情况
                <div>
                  <Button onClick={e => addContractList(e)}>
                    <PlusOutlined className={styles.hide_icon} />
                    添加
                  </Button>
                </div>
              </div>
            }
            key="1"
            id="22"
            className={classNames(styles.card)}
          >
            <div className={styles.scroll_x}>
              <div
                className={styles.scroll_x_boxs}
                style={{
                  width: `${
                    contractList.length > 1 ? contractList.length * 60 : 100
                  }%`,
                }}
              >
                {contractList.length > 0 &&
                  contractList.map((item, index) => {
                    return (
                      <div
                        key={index}
                        style={{
                          width: `${
                            contractList.length > 1
                              ? contractList.length * 50 * 0.6
                              : 100
                          }%`,
                          border: contractList.length === 1 && 0,
                        }}
                        className={
                          contractList.length > 1
                            ? styles.margin_right
                            : styles.margin_auto
                        }
                      >
                        <Form.Item label="合同类型" name={`type` + index}>
                          <Radio.Group
                            className={styles.grasp_Box}
                            onChange={e =>
                              contractListChange({
                                value: e.target.value,
                                index,
                                key: 'type',
                              })
                            }
                          >
                            <Radio value={'0'}>收入合同</Radio>
                            <Radio value={'1'}>成本合同</Radio>
                          </Radio.Group>
                        </Form.Item>
                        <Form.Item label="合同编号" name={`conNum` + index}>
                          <Input
                            placeholder="请填写"
                            onChange={e =>
                              contractListChange({
                                value: e.target.value,
                                index,
                                key: 'conNum',
                              })
                            }
                          />
                        </Form.Item>
                        <Form.Item label="呈批件编号" name={`groupNum` + index}>
                          <Input
                            onChange={e =>
                              contractListChange({
                                value: e.target.value,
                                index,
                                key: 'groupNum',
                              })
                            }
                            placeholder="请填写"
                          />
                        </Form.Item>
                        <Form.Item
                          label="呈批件名称"
                          name={`groupName` + index}
                        >
                          <Input
                            onChange={e =>
                              contractListChange({
                                value: e.target.value,
                                index,
                                key: 'groupName',
                              })
                            }
                            placeholder="请填写"
                          />
                        </Form.Item>
                        <Form.Item label="合同名称" name={`conName` + index}>
                          <Input
                            onChange={e =>
                              contractListChange({
                                value: e.target.value,
                                index,
                                key: 'conName',
                              })
                            }
                            placeholder="请填写"
                          />
                        </Form.Item>
                        <Form.Item label="合同有效期" name="planTimes">
                          <RangePicker
                            className={styles.selects}
                            suffixIcon={<ClockCircleOutlined />}
                            format="YYYY/MM/DD"
                            onChange={value =>
                              contractListChange({
                                value,
                                index,
                                key: 'planTimes',
                              })
                            }
                          />
                        </Form.Item>
                        <Form.Item
                          label="合同执行情况"
                          name={`conState` + index}
                          rules={[
                            {
                              required: true,
                              message: '请选择执行情况',
                            },
                          ]}
                        >
                          <Radio.Group
                            className={styles.grasp_Box}
                            onChange={e =>
                              contractListChange({
                                value: e.target.value,
                                index,
                                key: 'conState',
                              })
                            }
                          >
                            <Radio value={'0'}>执行完成</Radio>
                            <Radio value={'1'}>未执行完成</Radio>
                          </Radio.Group>
                        </Form.Item>
                        <Form.Item label="合同备注" name={`remarks` + index}>
                          <TextArea
                            placeholder="请填写合同背景或合同备注"
                            rows={3}
                            onChange={e =>
                              contractListChange({
                                value: e.target.value,
                                index,
                                key: 'remarks',
                              })
                            }
                          />
                        </Form.Item>
                        <Form.Item label="合同附件">
                          <Upload {...getPorps(index)}>
                            <Button type="file">
                              <CopyOutlined />
                              选择结项附件
                            </Button>
                          </Upload>
                        </Form.Item>
                        {contractList.length > 1 && (
                          <div className={styles.delete_cooperation_dept}>
                            <Popconfirm
                              title="是否删除？"
                              okText="是"
                              cancelText="否"
                              onConfirm={() => deleteContractList(index)}
                            >
                              <Button>删除</Button>
                            </Popconfirm>
                          </div>
                        )}
                      </div>
                    );
                  })}
              </div>
            </div>
          </Panel>
        </Collapse>
        <div className={styles.buttom_box}>
          <div>
            <div className={styles.titles}>项目回款执行情况</div>
            <div className={styles.progress_box}>
              <div>
                <div>项目合同总收入</div>
                <span>{titleInfo.refund_number}元</span>
                <div>实际回款金额</div>
                <span>{titleInfo.refund_number}元</span>
                {/* <div>项目利润</div>
                <span>{titleInfo.xmlr}元</span> */}
              </div>
              <div>
                <Progress
                  type="circle"
                  percent={titleInfo.incomeZb}
                  width={80}
                  status="normal"
                  format={value => `${value}%`}
                />
                <p className={styles.progressName}>项目整体</p>
              </div>
            </div>
          </div>
          <div>
            <div className={styles.titles}>项目成本情况</div>
            <Table
              columns={oucomeColumns}
              dataSource={oucomeInfo}
              pagination={false}
              className={styles.anTdTable}
            />
          </div>
        </div>
        <div className={styles.submit_boxs}>
          <div>
            <div>
              <Checkbox onChange={e => checkboxChange(e.target.checked)}>
                本人承诺，已严格按照公司要求履行项目经理职责，项目结项材料及数据真实，申请结项。
              </Checkbox>
            </div>
            <div>
              <Button onClick={() => window.history.go(-1)}>返回</Button>
              {/* <Button >打印</Button> */}
              {/* <Button onClick={() => savePDF()}>导出为pdf</Button> */}
              <Button type="primary" htmlType="submit" disabled={disabled}>
                申请结项
              </Button>
            </div>
          </div>
        </div>
      </Form>
    </div>
  );
};
