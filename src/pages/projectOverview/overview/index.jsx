import React, { useState, useEffect } from 'react';
import { Select, Radio, Tooltip } from 'antd';
import {
  projectFinancialNumByDept,
  statisticalAccess,
  projectSpeedNum,
  projectFinancialNum,
  selectDeptOutComeDetails,
  selectProjectNumsByStage,
  getDeptIncome,
  getIncomeMasterySum,
  selectIncomeStatistics,
} from './service';
import { QuestionCircleOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { Link } from 'umi';
import styles from './index.less';
import moment from 'moment';
import OngoingManage from './echarts/OngoingManage';
import OngoingNoManage from './echarts/OngoingNoManage';
import OverManage from './echarts/OverManage';
import OverNoManage from './echarts/OverNoManage';
import ProjectsNumber from './echarts/ProjectsNumber';
import TotalRevenue from './echarts/TotalRevenue';
import Level from './echarts/Level';
import IncomeType from './echarts/IncomeType';
import SectorRevenue from './echarts/SectorRevenue';
import Milestone from './echarts/Milestone';

const { Option } = Select;
const RadioButton = Radio.Button;

export default () => {
  useEffect(() => {
    getInfo();
  }, []);
  const [numberInfo, setNumberInfo] = useState({});
  const [projectLevelData, setProjectLevelData] = useState([]);
  const [incomeTypeData, setIncomTypeData] = useState([]);
  const [powerTotalNumber, setPowerTotalNumber] = useState([]);
  const [powerNumber, setPowerNumber] = useState({});
  const [powerTotalIncom, setPowerTotalIncom] = useState([]);
  const [powerMoneyNumber, setPowerMoneyNumber] = useState({});
  const [deptIncome, setDeptIncome] = useState([]);
  const [milestoneData, setMilestoneData] = useState([]);

  const getInfo = async () => {
    //把握度
    const projectSpeedNumResp = await projectSpeedNum();
    if (projectSpeedNumResp && projectSpeedNumResp.code === 200) {
      setNumberInfo(projectSpeedNumResp.data);
    }
    //项目级别
    const projectLevelResp = await projectFinancialNum();
    if (projectLevelResp && projectLevelResp.code === 200) {
      setProjectLevelData(projectLevelResp.data);
    }
    const selectIncomeStatisticsResp = await selectIncomeStatistics();
    if (selectIncomeStatisticsResp && selectIncomeStatisticsResp.code === 200) {
      setIncomTypeData(selectIncomeStatisticsResp.data);
    }
    //收入分类
    const projectFinancialNumByDeptResp = await projectFinancialNumByDept();
    if (
      projectFinancialNumByDeptResp &&
      projectFinancialNumByDeptResp.code === 200
    ) {
      const list = [];
      projectFinancialNumByDeptResp.data.forEach(item => {
        if (item.type_name) {
          list.push(item);
        }
      });
      setPowerTotalNumber(list);
      const powerNumber = {};
      let totle = 0;
      list.forEach(item => {
        powerNumber[item.item_grasp] = item.counts;
        totle += item.counts;
      });
      powerNumber.totle = totle;
      setPowerNumber(powerNumber);
    }
    //项目把握度
    const getIncomeMasterySumResp = await getIncomeMasterySum();
    if (getIncomeMasterySumResp && getIncomeMasterySumResp.code === 200) {
      const list = [];
      getIncomeMasterySumResp.data.forEach(item => {
        if (item.type_name) {
          list.push(item);
        }
      });
      setPowerTotalIncom(list);
      const powerMoneyNumber = {};
      let totle = 0;
      list.forEach(item => {
        powerMoneyNumber[item.item_grasp] = item.plan_num;
        totle += item.plan_num;
      });
      powerMoneyNumber.totle = totle;
      setPowerMoneyNumber(powerMoneyNumber);
    }
    //部门收入
    const getDeptIncomeResp = await getDeptIncome();
    if (getDeptIncomeResp && getDeptIncomeResp.code === 200) {
      setDeptIncome(getDeptIncomeResp.data);
    }
    //里程碑进度
    const selectProjectNumsByStageResp = await selectProjectNumsByStage();
    if (
      selectProjectNumsByStageResp &&
      selectProjectNumsByStageResp.code === 200
    ) {
      setMilestoneData(selectProjectNumsByStageResp.data);
    }
  };

  const [milestoneType, setMilestoneType] = useState('manage');

  //tab切换
  const tabChange = value => {
    setMilestoneType(value);
  };
  return (
    <div className="content">
      <div className={classNames(styles.card, styles.project_num)}>
        <div>
          项目总数
          <span>{numberInfo.allProjectNum}</span>
        </div>
        <div>
          <div
            className={styles.circle}
            style={{ backgroundColor: '#3D7BF8' }}
          ></div>
          经营类
          <span>{numberInfo.manageProjectNum}</span>
        </div>
        <div>
          <div
            className={styles.circle}
            style={{ backgroundColor: '#34B682' }}
          ></div>
          非经营类
          <span>{numberInfo.noManageProjectNum}</span>
        </div>
      </div>
      <div className={styles.project_two_box}>
        <div className={styles.card}>
          <div className={styles.title}>进行中的项目</div>
          <div className={styles.charts_two_box}>
            <div>
              <OngoingManage data={numberInfo} />
              <div className={styles.charts_total_num}>
                <div>经营类</div>
                <div>{numberInfo && numberInfo.runningAndManageProjectNum}</div>
              </div>
              <div className={styles.charts_buttom_info}>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#3D7BF8' }}
                    ></div>
                    <div>进度正常</div>
                  </div>
                  <div>
                    {numberInfo &&
                      numberInfo.runningAndManageAndNormalProjectNum}
                  </div>
                </div>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#F6BD16' }}
                    ></div>
                    <div>风险预警</div>
                  </div>
                  <div>
                    {numberInfo &&
                      numberInfo.runningAndManageAndRiskEarlyWarningProjectNum}
                  </div>
                </div>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#E8684A' }}
                    ></div>
                    <div>高风险</div>
                  </div>
                  <div>
                    {numberInfo &&
                      numberInfo.runningAndManageAndHighRiskProjectNum}
                  </div>
                </div>
              </div>
            </div>
            <div>
              <OngoingNoManage data={numberInfo} />
              <div className={styles.charts_total_num}>
                <div>非经营类</div>
                <div>{numberInfo && numberInfo.noManageProjectNum}</div>
              </div>
              <div className={styles.charts_buttom_info}>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#3D7BF8' }}
                    ></div>
                    <div>进度正常</div>
                  </div>
                  <div>
                    {numberInfo &&
                      numberInfo.runningAndNoManageAndnormalProjectNum}
                  </div>
                </div>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#F6BD16' }}
                    ></div>
                    <div>风险预警</div>
                  </div>
                  <div>
                    {numberInfo &&
                      numberInfo.runningAndNoManageAndRiskEarlyWarningProjectNum}
                  </div>
                </div>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#E8684A' }}
                    ></div>
                    <div>高风险</div>
                  </div>
                  <div>
                    {numberInfo &&
                      numberInfo.runningAndNoManageAndHighRiskProjectNum}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className={styles.card}>
          <div className={styles.title}>已结项项目</div>
          <div className={styles.charts_two_box}>
            <div>
              <OverManage
                data={{
                  value1: numberInfo.endingAndCompletedItemAndManageProjectNum,
                  value2:
                    numberInfo.endingAndCompletedItemAndNoManageProjectNum,
                }}
              />
              <div className={styles.charts_total_num}>
                <div>项目完成</div>
                <div>
                  {numberInfo && numberInfo.endingAndCompletedItemProjectNum}
                </div>
              </div>
              <div className={styles.charts_buttom_info}>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#3D7BF8' }}
                    ></div>
                    <div>经营类</div>
                  </div>
                  <div>
                    {numberInfo &&
                      numberInfo.endingAndCompletedItemAndManageProjectNum}
                  </div>
                </div>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#34B682' }}
                    ></div>
                    <div>非经营类</div>
                  </div>
                  <div>
                    {numberInfo &&
                      numberInfo.endingAndCompletedItemAndNoManageProjectNum}
                  </div>
                </div>
              </div>
            </div>
            <div>
              <OverNoManage
                data={{
                  value1: numberInfo.endingAndTerminatedAndManageProjectNum,
                  value2: numberInfo.endingAndTerminatedAndNoManageProjectNum,
                }}
              />
              <div className={styles.charts_total_num}>
                <div>项目终止</div>
                <div>
                  {numberInfo && numberInfo.endingAndTerminatedProjectNum}
                </div>
              </div>
              <div className={styles.charts_buttom_info}>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#3D7BF8' }}
                    ></div>
                    <div>经营类</div>
                  </div>
                  <div>
                    {numberInfo &&
                      numberInfo.endingAndTerminatedAndManageProjectNum}
                  </div>
                </div>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#34B682' }}
                    ></div>
                    <div>非经营类</div>
                  </div>
                  <div>
                    {numberInfo &&
                      numberInfo.endingAndTerminatedAndNoManageProjectNum}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* <div className={styles.card}>项目营收 单位 万</div> */}
      <div className={styles.project_two_box}>
        <div className={styles.card}>
          <div className={styles.title}>项目级别</div>
          <div>
            <Level data={projectLevelData} />
          </div>
        </div>
        <div className={styles.card}>
          <div className={styles.title}>收入分类</div>
          <IncomeType data={incomeTypeData} />
        </div>
      </div>

      <div className={styles.project_two_box}>
        <div className={styles.card}>
          <div className={styles.title}>
            项目把握度
            <Tooltip
              title={
                <>
                  0-30% 低把握度
                  <br />
                  31%-50% 较低把握度
                  <br />
                  31%-50% 较低把握度
                  <br />
                  81%-100% 高把握度
                  <br />
                </>
              }
            >
              <QuestionCircleOutlined style={{ width: 20 }} />
            </Tooltip>
          </div>
          <div className={styles.charts_two_box}>
            <div>
              <ProjectsNumber deptList={powerTotalNumber} />
              <div className={styles.charts_name}>项目数量</div>
              <div className={styles.charts_buttom_info_two}>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#3D7BF8' }}
                    ></div>
                    <div>高把握度</div>
                  </div>
                  <div>
                    {(powerNumber && powerNumber.high_grasp) || 0}项(
                    {parseInt(
                      (powerNumber.high_grasp / powerNumber.totle || 0).toFixed(
                        2,
                      ) * 100,
                    )}
                    %)
                  </div>
                </div>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#F6BD16' }}
                    ></div>
                    <div>较高把握度</div>
                  </div>
                  <div>
                    {(powerNumber && powerNumber.higher_grasp) || 0}项(
                    {parseInt(
                      (
                        powerNumber.higher_grasp / powerNumber.totle || 0
                      ).toFixed(2) * 100,
                    )}
                    %)
                  </div>
                </div>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#E8684A' }}
                    ></div>
                    <div>低把握度</div>
                  </div>
                  <div>
                    {(powerNumber && powerNumber.low_grasp) || 0}项(
                    {parseInt(
                      (powerNumber.low_grasp / powerNumber.totle || 0).toFixed(
                        2,
                      ) * 100,
                    )}
                    %)
                  </div>
                </div>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#E8684A' }}
                    ></div>
                    <div>较低把握度</div>
                  </div>
                  <div>
                    {(powerNumber && powerNumber.lower_grasp) || 0}项(
                    {parseInt(
                      ((powerNumber.lower_grasp / powerNumber.totle).toFixed(
                        2,
                      ) || 0) * 100,
                    )}
                    %)
                  </div>
                </div>
              </div>
            </div>
            <div>
              <TotalRevenue total={powerTotalIncom} />
              <div className={styles.charts_name}>年度计划收入总和(W)</div>
              <div className={styles.charts_buttom_info_two}>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#3D7BF8' }}
                    ></div>
                    <div>高把握度</div>
                  </div>
                  <div>
                    {(powerMoneyNumber && powerMoneyNumber.high_grasp) || 0}w
                    <br /> (
                    {parseInt(
                      (
                        powerMoneyNumber.high_grasp / powerMoneyNumber.totle ||
                        0
                      ).toFixed(2) * 100,
                    )}
                    %)
                  </div>
                </div>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#F6BD16' }}
                    ></div>
                    <div>较高把握度</div>
                  </div>
                  <div>
                    {(powerMoneyNumber && powerMoneyNumber.higher_grasp) || 0}w
                    <br /> (
                    {parseInt(
                      (
                        powerMoneyNumber.higher_grasp /
                          powerMoneyNumber.totle || 0
                      ).toFixed(2) * 100,
                    )}
                    %)
                  </div>
                </div>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#E8684A' }}
                    ></div>
                    <div>低把握度</div>
                  </div>
                  <div>
                    {(powerMoneyNumber && powerMoneyNumber.low_grasp) || 0}w
                    <br /> (
                    {parseInt(
                      (
                        powerMoneyNumber.low_grasp / powerMoneyNumber.totle || 0
                      ).toFixed(2) * 100,
                    )}
                    %)
                  </div>
                </div>
                <div>
                  <div>
                    <div
                      className={styles.circle}
                      style={{ backgroundColor: '#E8684A' }}
                    ></div>
                    <div>较低把握度</div>
                  </div>
                  <div>
                    {(powerMoneyNumber && powerMoneyNumber.lower_grasp) || 0}w
                    <br /> (
                    {parseInt(
                      (
                        powerMoneyNumber.lower_grasp / powerMoneyNumber.totle ||
                        0
                      ).toFixed(2) * 100,
                    )}
                    %)
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.card}>
          <div className={styles.title}>部门收入</div>
          {/* <div className={styles.overflow_box}> */}
          <SectorRevenue sectorRevenue={deptIncome} />
          {/* </div> */}
        </div>
      </div>
      <div className={styles.card} style={{ marginBottom: 100 }}>
        <div className={classNames(styles.title, styles.cardOption)}>
          <div>里程碑进度</div>
          <div className={styles.tabSwitch}>
            <Radio.Group
              defaultValue="manage"
              buttonStyle="solid"
              onChange={e => tabChange(e.target.value)}
            >
              <RadioButton value="manage">经营类</RadioButton>
              <RadioButton value="no_manage">非经营类</RadioButton>
            </Radio.Group>
          </div>
        </div>
        <Milestone
          milestoneData={milestoneData}
          milestoneType={milestoneType}
        />
      </div>
    </div>
  );
};
