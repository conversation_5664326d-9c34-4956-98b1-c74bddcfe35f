import React, { useEffect, useState } from 'react';
import style from './index.less';
import echarts from 'echarts';
import { connect } from 'dva';
import { handleDept, reserveTwo } from '@/utils/utils';

const Milestone = props => {
  const { milestoneData, milestoneType } = props;
  const colorList = [
    '#7E26EE',
    '#B214EA',
    '#EA14D4',
    '#E5125E',
    '#F64332',
    '#F6784E',
    '#EC9B2A',
    '#F0BC22',
  ];
  const [handleDeptList, setHandleDeptList] = useState([]);
  const [handleData, setHandleData] = useState({
    planIncome: [],
    planIncomeCount: [],
    incomePassNum: [],
  });
  useEffect(() => {
    const handleDeptList = [];
    const handleData = {
      allNumber: [],
    };
    milestoneData.map((item, index) => {
      if (item.type === milestoneType) {
        handleData.allNumber.push({
          value: item.allNum,
          itemStyle: {
            color: colorList[index],
          },
        });
        handleDeptList.push(item.item_stage_type);
      }
    });
    setHandleDeptList(handleDeptList);
    setHandleData(handleData);
  }, [milestoneData, milestoneType]);
  useEffect(() => {
    const myChart = echarts.init(document.getElementById('Milestone'));
    myChart.resize({ height: milestoneData.length * 36 + 40 });
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        position: function(point, params, dom, rect, size) {
          return [point[0], point[1]];
        },
        // backgroundColor: 'rgba(255, 255, 255,1)',
        padding: 8,
        // textStyle: {
        //   color: '#333333',
        //   fontSize: 11,
        // },
        // extraCssText:
        //   'box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.24);border-radius: 2px;',
        formatter: (params, ticket, callback) => {
          let formatterHtml = '';
          params.map(item => {
            formatterHtml +=
              item.componentIndex === 0
                ? `<div style='margin:2px 0 -12px 0;font-size:13px;font-weight:500'>${item.axisValue}</div><br/>${item.marker}${item.seriesName}: ${item.value} <br/>`
                : `${item.marker}${item.seriesName}: ${item.value} <br/>`;
          });
          const proportion =
            params[2] &&
            `${Math.round((params[0].value / params[2].value) * 100)}% `;
          formatterHtml += proportion
            ? `  已确认收入计划占总计划收入: ${proportion || ''}`
            : '';
          return formatterHtml;
        },
      },
      color: ['#F0BC22', '#EC9B2A', '#7E26EE'],
      legend: {
        data: ['已确定收入', '当期累计计划收入', '计划收入'],
        height: 20,
        itemGap: 12,
        itemWidth: 6,
        itemHeight: 6,
        icon: 'circle',
        textStyle: {
          color: 'rgba(85,85,85,1)',
        },
        bottom: 0,
      },
      grid: {
        left: 0,
        right: 20,
        top: 0,
        bottom: 40,
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        axisTick: { show: false },
        axisLine: { show: false },
        axisLabel: {
          formatter: value => {
            return `${value || 0}`;
          },
        },

        splitLine: {
          lineStyle: {
            color: '#EDEFF2',
          },
        },
      },
      yAxis: {
        type: 'category',
        axisTick: { show: false },
        axisLine: {
          lineStyle: {
            color: '#EDEFF2',
          },
        },
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontFamily: 'DINRegular',
          color: '#555555',
          formatter: function(value) {
            if (value === '四川分公司（筹）机场事业部') {
              return '四川分公司';
            }
            if (value === '四川分公司（筹）') {
              return '四川分公司';
            }
            if (value === '产品与解决方案中心（PSC）') {
              return 'PSC';
            }
            if (value.length > 5) {
              return value.substring(0, 5) + '...';
            } else {
              return value;
            }
          },
        },
        data: handleDeptList,
      },
      barMaxWidth: 12,
      series: [
        {
          name: '数量',
          type: 'bar',
          stack: '总量',
          label: {
            show: false,
            position: 'insideRight',
          },
          // data: handleData.planInComeData,
          data: handleData.allNumber,

          // data: handleData.planInComeData,
          // emphasis: {
          //   itemStyle: {
          //     color: '#34B682',
          //   },
          // },
        },
      ],
    };
    myChart.setOption(option);
  });

  return <div id="Milestone" className={style.Milestone} />;
};

export default connect(({ indexPage }) => ({
  indexPage,
}))(Milestone);
