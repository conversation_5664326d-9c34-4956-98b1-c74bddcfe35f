import React, { useEffect, useState } from 'react';
import style from './index.less';
import echarts from 'echarts';

const TotalRevenue = props => {
  const { total } = props;
  const data = [];
  useEffect(() => {
    total.map(item => {
      data.push({
        value: `${item.ratio}`,
        name: `${item.type_name}`,
        label: {
          show: false,
        },
        backgroundColor: 'rgba(255, 255, 255,1)',

        itemStyle: {
          borderColor: '#fff',
          borderWidth: 2,
        },
        textStyle: {
          color: '#333333',
          fontSize: 11,
        },
        position: function(point, params, dom, rect, size) {
          return [point[0], point[1]];
        },
        extraCssText:
          'box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.24);border-radius: 2px;',
        tooltip: {
          formatter: `<div style='font-size:13px;font-weight:500'>${
            item.type_name
          }</div><div style='margin:2px 0 -14px 0;font-size:13px;font-weight:500'>年度计划总和:${item.plan_num ||
            0}W</div><br/>系统连接及运维服务: ${item.系统连接及运维服务 ||
            0}W<br/>系统集成服务:${item.系统集成服务 ||
            0}W<br/>产品和解决方案服务:${item.产品和解决方案服务 ||
            0}W<br/>软件外包开发服务:${item.软件外包开发服务 || 0}W`,
        },
      });
    });
    const myChart = echarts.init(document.getElementById('totalRevenue'));
    const option = {
      color: ['#34B682', '#F6BD16', '#E8684A', '#3D7BF8'],
      tooltip: {
        trigger: 'item',
        // backgroundColor: 'rgba(255, 255, 255,1)',
        padding: 8,
        // textStyle: {
        //   color: '#666666',
        //   fontSize: 11,
        // },
        // extraCssText:'box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.24);border-radius: 2px;',
        confine: true,
        alwaysShowContent: false,
        hideDelay: 100,
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: [],
      },
      grid: {
        left: 0,
        right: '1',
        top: 0,
        containLabel: true,
      },
      label: {
        fontFamily: 'PingFang SC',
        fontSize: 10,
      },
      series: [
        {
          name: '访问来源',
          type: 'pie',
          radius: ['60%', '85%'],
          minAngle: 30,
          margin: 10,
          clockwise: false,
          left: 0,
          data,
          tooltip: {
            alwaysShowContent: false,
            hideDelay: 100,
          },
          emphasis: {},
          label: {
            show: false,
          },
          labelLine: {
            length: 0,
            smooth: 1,
            length2: 8,
          },
        },
      ],
    };
    myChart.setOption(option);
  }, [total]);

  return <div id="totalRevenue" className={style.totalRevenue} />;
};

export default TotalRevenue;
