//预算
import React, { useEffect, useState } from 'react';
import style from './index.less';
import echarts from 'echarts';
import moment from 'moment';

const IncomeType = props => {
  const { data } = props;
  const [actualTotal, setActualTotal] = useState([]);
  const [planIncomeTotal, setPlanIncomeTotal] = useState([]);
  const [total, setTotal] = useState([]);
  const [severData, setSaverData] = useState([]);
  const [xAxisData, setXAxisData] = useState([]);
  const [percentage, setPercentage] = useState([]);

  useEffect(() => {
    const dataList = [];
    const xAxisData = [];
    const actualTotal = [];
    const planIncomeTotal = [];
    const total = [];
    const percentage = [];
    data.forEach((item, index) => {
      actualTotal.push(item.actualTotal);
      planIncomeTotal.push(item.planIncomeTotal);
      total.push(item.total);
      xAxisData.push(item.typeName);
      percentage.push(Number(item.actualTotal / item.total).toFixed(4));
    });
    setActualTotal(actualTotal);
    setPlanIncomeTotal(planIncomeTotal);
    setTotal(total);
    setSaverData(dataList);
    setXAxisData(xAxisData);
    setPercentage(percentage);
  }, [data]);

  function sum(arr) {
    var s = 0;
    arr.forEach(function(val, idx, arr) {
      s += val;
    }, 0);

    return s;
  }
  useEffect(() => {
    if (data.length > 0) {
      const myChart = echarts.init(document.getElementById('IncomeType'));
      myChart.resize({ height: 6 * 36 + 40 });
      const option = {
        tooltip: {
          formatter: (params, ticket, callback) => {
            let formatterHtml = '';
            params.map((item, index) => {
              if (item.componentSubType !== 'line') {
                formatterHtml +=
                  item.componentIndex === 0
                    ? `<div style=';margin:5px 0 -10px 0;font-size:13px;font-weight:500'>${
                        item.axisValue
                      }</div><br/>${item.marker}${item.seriesName}: ${
                        item.value
                      }W <br/>
            <div >占整体计划收入:${Number(
              (item.value / sum(total)).toFixed(4) * 100,
            ).toFixed(2)}%</div>
            `
                    : `${item.marker}${item.seriesName}: ${item.value}W <br/>
            <div >占计划收入:${(item.value / total[item.axisIndex]).toFixed(4) *
              100}%</div>
            `;
              }
            });
            return formatterHtml;
          },

          position: function(point, params, dom, rect, size) {
            return [point[0], point[1]];
          },
          trigger: 'axis',
          // backgroundColor: '#FFFFFF',
          confine: true,
          padding: 8,
          postion: 'bottom',
          // textStyle: {
          //   color: '#333333',
          //   fontSize: 11,
          // },
          // extraCssText:
          //   'box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.24);border-radius: 2px;',
        },
        color: ['#E8684A', '#F6BD16', '#3D7BF8'],
        grid: {
          left: 0,
          right: '1',
          top: 20,
          bottom: 40,
          containLabel: true,
        },
        axisLine: {
          lineStyle: {
            color: '#555555',
          },
        },
        legend: {
          data: ['计划收入', '当期累计计划收入', '已确定收入'],
          height: 20,
          itemGap: 12,
          itemWidth: 6,
          itemHeight: 6,
          icon: 'circle',
          textStyle: {
            color: 'rgba(85,85,85,1)',
          },
          bottom: 0,
        },
        // markLine: {
        //   silent: true,
        // },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisTick: { show: false },
          axisLine: { show: false },
          splitLine: {
            lineStyle: {
              color: '#EDEFF2',
            },
          },
          axisLabel: {
            formatter: function(value) {
              //关键代码
              var res = value;
              if (res.length > 5) {
                res = res.substring(0, 4) + '..';
              }
              return res;
            },
          },

          // nameTextStyle: {
          //   rich: {
          //     a: {
          //       width: 10,

          //     }
          //   }
          // }
        },
        yAxis: [
          {
            show: true,
            type: 'value',
            axisLine: { show: false },
          },
          {
            show: false,
            type: 'value',
            axisLine: { show: false },
          },
        ],

        series: [
          {
            name: '计划收入',
            type: 'bar',
            barWidth: 20,
            stack: '计划收入',
            label: {
              show: false,
              position: 'insideRight',
            },
            data: total,
            emphasis: {
              itemStyle: {
                color: '#E8684A',
              },
            },
          },
          {
            name: '当期累计计划收入',
            type: 'bar',
            barWidth: 20,
            stack: '当期累计计划收入',
            label: {
              show: false,
              position: 'insideRight',
            },
            data: planIncomeTotal,
            emphasis: {
              itemStyle: {
                color: '#E8684A',
              },
            },
          },
          {
            name: '已确定收入',
            type: 'bar',
            barWidth: 20,
            stack: '已确定收入',
            label: {
              show: false,
              position: 'insideRight',
            },
            data: actualTotal,
            emphasis: {
              itemStyle: {
                color: '#E8684A',
              },
            },
          },
          {
            name: '已确定收入占总计划收入',
            type: 'line',
            smooth: true,
            yAxisIndex: 1,
            data: percentage,
            itemStyle: {
              color: '#34B682',
            },
          },
        ],
      };
      myChart.setOption(option);
    }
  }, [severData]);

  return data.length > 0 ? (
    <div id="IncomeType" className={style.budget} />
  ) : (
    <div className={style.noData}>暂无数据</div>
  );
};

export default IncomeType;
