import React, { useEffect } from 'react';
import style from './index.less';
import echarts from 'echarts';

const Index = props => {
  const { data } = props;
  const {
    runningAndNoManageAndnormalProjectNum = 0,
    runningAndNoManageAndRiskEarlyWarningProjectNum = 0,
    runningAndNoManageAndHighRiskProjectNum = 0,
  } = data;
  useEffect(() => {
    const myChart = echarts.init(document.getElementById('OngoingNoManage'));
    const option = {
      tooltip: {
        show: false,
        trigger: 'none',
      },
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 2,
      },
      color: ['#3D7BF8', '#F6BD16', '#E8684A'],
      series: [
        {
          name: '访问来源',
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['60%', '85%'],
          label: {
            show: false,
            position: 'center',
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: runningAndNoManageAndnormalProjectNum, name: '进度正常' },
            {
              value: runningAndNoManageAndRiskEarlyWarningProjectNum,
              name: '风险预警',
            },
            { value: runningAndNoManageAndHighRiskProjectNum, name: '高风险' },
          ],
        },
      ],
    };
    myChart.setOption(option);
  }, [data]);

  return <div id="OngoingNoManage" className={style.statisticsEcharts} />;
};

export default Index;
