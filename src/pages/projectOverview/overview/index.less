.card {
  width: 100%;
  background: #ffffff;
  border: 1px solid #edeff2;
  box-sizing: border-box;
  padding: 13px 24px;
  box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
  border-radius: 8px;
  margin-bottom: 24px;

  :global {
    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      background-color: #3d7bf8;
      border-color: #3d7bf8;
    }

    .ant-radio-button-wrapper:first-child {
      border-radius: 5px 0 0 5px;
    }

    .ant-radio-button-wrapper:last-child {
      border-radius: 0 5px 5px 0;
    }

    .ant-btn-primary {
      background-color: #3d7bf8;
    }
  }
}

.tabSwitch {
  width: 180px;
  margin-left: 20px;
  font-weight: 400;
}

.project_num {
  display: flex;
  font-family: OPPOSans;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 22px;
  text-align: center;
  color: #000000;

  > div {
    display: flex;
    align-items: center;
  }

  span {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 800;
    font-size: 16px;
    line-height: 24px;
    color: #333333;
    margin-left: 8px;
    margin-right: 24px;
  }
}

.project_two_box {
  display: flex;
  justify-content: space-between;

  > div {
    width: 49%;
  }
}

.charts_two_box {
  display: flex;
  justify-content: space-between;

  > div:first-child {
    border-right: 1px dashed #edeff2;
  }

  > div {
    width: 49%;
    position: relative;

    > div:first-child {
      margin: 0 auto;
    }
  }
}

.title {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  margin-bottom: 16px;
  font-weight: bold;
  font-size: 16px;
  line-height: 32px;
  align-items: center;
  color: #333333;
}

.charts_total_num {
  position: absolute;
  width: 100%;
  position: absolute;
  left: 0;
  top: 40px;
  text-align: center;

  > div:first-child {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    text-align: center;
    color: #7a7a7a;
  }

  > div:last-child {
    font-family: Bebas;
    font-style: normal;
    font-weight: normal;
    font-size: 24px;
    line-height: 32px;
    text-align: center;
    color: #222222;
  }
}

.charts_buttom_info {
  display: flex;
  justify-content: space-around;
  margin-top: 10px;

  > div {
    > div:first-child {
      display: flex;
      font-family: OPPOSans;
      font-style: normal;
      font-weight: 500;
      font-size: 12px;
      line-height: 20px;
      color: #7a7a7a;
      align-items: center;
    }
  }
}

.circle {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  margin-right: 5px;
}

.charts_buttom_info_two {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  flex-wrap: wrap;

  > div {
    width: 45%;
    margin-top: 10px;
    margin-bottom: 20px;

    > div:first-child {
      display: flex;
      font-family: OPPOSans;
      font-style: normal;
      font-weight: 500;
      font-size: 12px;
      white-space: nowrap;
      line-height: 20px;
      color: #7a7a7a;
      align-items: center;
    }
  }
}

// .overflow_box{
//   height: 300px;
//   overflow-y: auto;
// }
.charts_name {
  text-align: center;
  font-size: 12px;
  color: #7a7a7a;
}
