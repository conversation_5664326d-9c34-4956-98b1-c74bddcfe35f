import request from '@/utils/request';
import { BASE_URl } from "../../../utils/constant";

//获取项目结项台头信息
export function getItemEndTitle(params) {
  return request(`${BASE_URl}/projectEnd/getItemEndTitle?item_num=${params}`, {
    method: 'POST',
  });
}

//获取项目结项台头信息
export function inItemEnd(params) {
  return request(`${BASE_URl}/projectEnd/inItemEnd`, {
    method: 'POST',
    data: params,
  });
}

//获取项目结项
export function getItemEnd(params) {
  const { trial_ids, item_id } = params;
  return request(
    `${BASE_URl}/projectEnd/getItemEnd?item_id=${item_id}&trial_ids=${trial_ids}`,
    {
      method: 'POST',
    },
  );
}

//审核
export function itemEndSubmit(params) {
  const { itemId, submitType, trace, trialId, lowerId } = params;
  return request(
    `${BASE_URl}/projectEnd/itemEndSubmit?itemId=${itemId}&submitType=${submitType}&trace=${trace}&trialId=${trialId}&lowerId=${lowerId}`,
    {
      method: 'POST',
    },
  );
}

//查询项目审批流程图信息
export function getItemTrial(id) {
  return request(`${BASE_URl}/projectStand/getItemTrial?item_id=${id}`, {
    method: 'post',
  });
}
