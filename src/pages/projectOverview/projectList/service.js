import request from '@/utils/request';
import { BASE_URl } from '../../../utils/constant';

//获取list
export function getProjectListPage(params) {
  return request(`${BASE_URl}/projectList/getProjectListPage`, {
    method: 'POST',
    data: params,
  });
}

export function getDynamicProjectListPage(params) {
  return request(`${BASE_URl}/projectList/getDynamicProjectListPage`, {
    method: 'POST',
    data: params,
  });
}

export function menuAddAndSave(params) {
  return request(`${BASE_URl}/menu/saveOrUpdate`, {
    method: 'POST',
    data: params,
  });
}
//  关注/取消关注项目 0关注 -1取消
export function updateFollowState(params) {
  const { item_id, state } = params;
  return request(
    `${BASE_URl}/projectList/updateFollowState?item_id=${item_id}&state=${state}`,
    {
      method: 'POST',
    },
  );
}
//  项目列表标题行下拉框信息
export function getSearchListInfo() {
  return request(`${BASE_URl}/projectList/getSearchListInfo`, {
    method: 'POST',
  });
}

//  查询项目列表名称
export function getItemNameList(params) {
  const { name } = params;
  return request(`${BASE_URl}/projectList/getItemNameList?name=${name}`, {
    method: 'POST',
  });
}

//获取部门下拉框数据
export function getDeptListInfo() {
  return request(`${BASE_URl}/dept/SWCARESdeptAll`, {
    method: 'POST',
  });
}

//获取项目列表的列
export function getSearchFieldInfo() {
  return request(`${BASE_URl}/projectList/getSearchFieldInfo`, {
    method: 'POST',
  });
}

//动态获取项目列表的查询条件
export function getDynamicSearchInfo() {
  return request(`${BASE_URl}/projectList/getDynamicSearchInfo`, {
    method: 'POST',
  });
}

//下载项目列表数据
export function downProjectListFile(params) {
  return request(`${BASE_URl}/projectList/downProjectListFile`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      Accept: '*/*',
    },
    responseType: 'blob',
  });
}
