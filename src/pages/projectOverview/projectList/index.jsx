import React, { useState, useEffect, useRef } from 'react';
import {
  updateFollowState,
  getSearchListInfo,
  getDeptListInfo,
  getItemNameList,
  getSearchFieldInfo,
  getDynamicSearchInfo,
  downProjectListFile,
  getDynamicProjectListPage,
} from './service';
import {
  Input,
  DatePicker,
  message,
  Button,
  Tooltip,
  Progress,
  Pagination,
  Select,
  Radio,
  InputNumber,
  Modal,
  Checkbox,
  Divider,
  Form,
} from 'antd';
import {
  DownloadOutlined,
  ClockCircleOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import classNames from 'classnames';

import { Link } from 'umi';
import styles from './index.less';
import moment from 'moment';
import follow_icon from '@/assets/follow_icon.svg';
import ProTable from '@ant-design/pro-table';
import { MyContext } from '../../home';
import { changeStr } from '../../../utils/utils';

const { Option } = Select;

export default () => {
  const CheckboxGroup = Checkbox.Group;
  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  const [plainOptions, setPlainOptions] = useState([]);
  const [allValues, setAllValue] = useState([]);
  //选择查询动态select选择列表
  const [selectOption, setselectOption] = useState([]);
  //选择查询动态select选择列表--区间查询
  const [selectOptionCoped, setselectOptionCoped] = useState([]);
  //动态查询selcet标记
  const [selectOptionText, setselectOptionText] = useState('');

  //动态查询区间查询标记
  const [selectOptionTextCoped, setselectOptionTextCoped] = useState('');

  //存储选择查询结果值
  const [selectParams, setselectParams] = useState([]);

  const { TextArea } = Input;
  //存储选择查询结果集
  const [selectItemRturn, setSelectItemRturn] = useState({});

  const [indeterminate, setIndeterminate] = React.useState(false);
  const [checkAll, setCheckAll] = React.useState(false);

  const onChangeCheckbox = list => {
    setCheckedLists(list);
    setIndeterminate(!!list.length && list.length < plainOptions.length);
    setCheckAll(list.length === plainOptions.length);
  };

  const onCheckAllChange = e => {
    setCheckedLists(e.target.checked ? allValues : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
    console.log('(e.target.checked', e.target.checked);
  };

  const onFinish = values => {
    console.log('Success:', values);
  };

  //查询展开收起状态
  const [openStatus, setOpenStatus] = useState(true);
  //部门列表
  const [deptList, setDeptList] = useState([]);
  useEffect(() => {
    setButtonList(context);
  }, [context]);

  useEffect(() => {
    setColumns(totalColumns);
  }, [buttonList, deptList]);

  //解决数据同步问题
  const [numFlush, setNumFlush] = useState(1);

  const dateFormat = 'YYYY/MM/DD';
  const [data, setData] = useState([]);
  const [dataTotal, setDataTotal] = useState(0);
  // 关注列表id
  const [keys, setKeys] = useState([]);
  const [recordParams, setRecordParams] = useState([]);

  //表下拉框查询
  const [dorwnList, setDorwnList] = useState([]);

  const [columns, setColumns] = useState(totalColumns);

  const [visibleModal, setVisibleModal] = useState(false);

  const [visibleModalUpdate, setVisibleModalUpdate] = useState(false);

  //选择查询是否可继续添加状态
  const [selectStatus, setSelectStatus] = useState(0);

  //选择查询是否可继续添加状态-copd
  const [selectStatusCopd, setSelectStatusCopd] = useState(0);

  //多维查询数组数据--选择查询列表--查询可选字段
  const [selectList, setSelectList] = useState([]);

  //多维查询数组数据--区间查询列表--查询可选字段
  const [selectListCoped, setSelectListCoped] = useState([]);

  const handleSearchSelectCoped = (value, index, type) => {
    let newLabel = selectOptionTextCoped + '_' + type;
    setselectParams({ ...selectParams, [newLabel]: value });
    let newArrs = selectScopetItem;
    if (type === 'start') {
      newArrs[index].startValue = value;
    } else {
      newArrs[index].endValue = value;
    }
    setSelectScopeItem(newArrs);
  };

  //选择查询
  const handleSearchSelect = (value, index) => {
    let selectItems = selectList;
    if (value) {
      setselectOptionText(value);
      let serchSelectArr = [];
      let item = selectListItem;

      //取出itemOption
      selectItems.forEach(item => {
        if (value === item.value) {
          serchSelectArr = item.item;
        }
      });
      setselectOption(serchSelectArr);

      if (selectListItem.length === 1) {
        item[0].key = 0;
        item[0].name = value;
        item[0].itemOption = serchSelectArr;

        selectItems.forEach(selectItem => {
          if (selectItem.key === 0) {
            selectItem.key = null;
          }
          if (selectItem.value === value) {
            selectItem.key = 0;
          }
        });
      } else {
        item.forEach((items, indexs) => {
          if (indexs === index) {
            items.key = index;
            items.name = value;
            items.itemOption = serchSelectArr;
          }
        });
        selectItems.forEach(selectItem => {
          if (selectItem.key === index) {
            selectItem.key = null;
          }
          if (selectItem.value === value) {
            // selectItem.isSelected = true;
            selectItem.key = index;
          }
        });
      }

      setNumFlush(numFlush + 1);
      let SelectStatusNumber = selectItems.length;
      selectItems.forEach(item => {
        if (item.isSelected) {
          SelectStatusNumber -= 1;
        }
      });
      setSelectStatus(SelectStatusNumber);
      setSelectListItem(item);
    } else {
      let items = selectListItem;
      items[index].itemValue = [];
      items[index].name = '';
      setSelectListItem(items);
      setNumFlush(numFlush + 1);
    }
    let labelArr = [];
    selectListItem.forEach(item => {
      if (item.name) {
        labelArr.push(item.name);
      }
    });
    selectItems.forEach(item => {
      if (labelArr.includes(item.value)) {
        item.isSelected = true;
      } else {
        item.isSelected = false;
      }
    });
    setNumFlush(numFlush + 1);
    setSelectList(selectItems);
  };

  //区间查询
  const handleSearchSelectScoped = (value, index) => {
    setselectOptionTextCoped(value);
    let selectItems = selectListCoped;
    if (value) {
      let type = '';
      let item = selectScopetItem;
      selectItems.forEach(item => {
        if (item.value === value) {
          type = item.type;
        }
      });
      if (selectScopetItem.length === 1) {
        item[0].key = 0;
        item[0].name = value;
        item[0].type = type;
        selectItems.forEach(selectItem => {
          if (selectItem.key === 0) {
            selectItem.key = null;
            item[0].type = type;
          }
          if (selectItem.value === value) {
            selectItem.key = 0;
            item[0].type = type;
          }
        });
      } else {
        item.forEach((items, indexs) => {
          if (indexs === index) {
            items.key = index;
            items.name = value;
            items.type = type;
          }
        });
        selectItems.forEach(selectItem => {
          if (selectItem.key === index) {
            selectItem.key = null;
          }
          if (selectItem.value === value) {
            selectItem.key = index;
          }
        });
      }
      let SelectStatusNumber = selectItems.length;
      selectItems.forEach(item => {
        if (item.isSelected) {
          SelectStatusNumber -= 1;
        }
      });

      setSelectStatusCopd(SelectStatusNumber);
      setSelectScopeItem(item);
    } else {
      let item = selectScopetItem;
      item[index].name = '';
      item[index].startValue = null;
      item[index].endValue = null;
      setSelectScopeItem(item);
      setNumFlush(numFlush + 1);
    }

    let labelArr = [];
    selectScopetItem.forEach(item => {
      if (item.name) {
        labelArr.push(item.name);
      }
    });
    selectItems.forEach(item => {
      if (labelArr.includes(item.value)) {
        item.isSelected = true;
      } else {
        item.isSelected = false;
      }
    });
    setNumFlush(numFlush + 1);
    setSelectListCoped(selectItems);
  };

  const handleSearchSelects = (value, index, type) => {
    let arrs = selectListItem;
    arrs.forEach((items, indexs) => {
      if (indexs === index) {
        items.itemValue = value;
      }
    });

    setSelectListItem(arrs);

    setselectParams({ ...selectParams, [selectOptionText]: value.join(',') });

    let item = selectListItem;
    item.forEach(items => {
      if (items.key === index) {
        setSelectItemRturn({ ...selectItemRturn, [items.name]: value });
      }
    });
  };

  //选择查询列表查询结构
  const [selectListItem, setSelectListItem] = useState([
    { name: '', key: 0, itemValue: [], itemOption: [] },
  ]);

  //区间查询列表查询结构
  const [selectScopetItem, setSelectScopeItem] = useState([
    { name: '', key: null, type: '', startValue: '', endValue: '' },
  ]);

  const [serchParamDate, setSerchParamDate] = useState({
    startValue: null,
    endValue: null,
  });

  //自定义模板方法
  const downloadCustom = () => {
    setVisibleModal(true);
  };
  //默认导出全部数据
  const downloadCustomAll = async () => {
    let labels = [];
    let values = [];
    plainOptions.forEach(items => {
      labels.push(items.label);
      values.push(items.value);
    });

    let selectParams = {};
    let numberParams = {};
    selectListItem.forEach(item => {
      if (item.name && item.itemValue.length > 0) {
        selectParams[item.name] = item.itemValue.join(',');
      }
    });
    selectScopetItem.forEach(item => {
      if (item.name) {
        let labelStart = null;
        let labelEnd = null;
        if (item.startValue) {
          labelStart = item.name + '_' + 'start';
        }
        if (item.endValue) {
          labelEnd = item.name + '_' + 'end';
        }
        if (labelStart) {
          numberParams[labelStart] = item.startValue;
        }
        if (labelEnd) {
          numberParams[labelEnd] = item.endValue;
        }
      }
    });

    let params = {
      isAsc: parmas.isAsc,
      isFollow: parmas.isFollow,
      itemName: openStatus ? searchParmas.name : searchParmasS.name,
      itemNum: openStatus ? searchParmas.itemNum : searchParmasS.itemNum,
      page: parmas.page,
      startTime: openStatus
        ? searchParmas.start_time
        : searchParmasS.start_time,
      endTime: openStatus ? searchParmas.end_time : searchParmasS.end_time,
      searchItemName: values.join('##'),
      searchItemText: labels.join('##'),
      type: parmas.type,
      orderByField: parmas.orderByField,
      limit: parmas.limit,
      standYear: openStatus ? searchParmas.standYear : searchParmasS.standYear,
    };
    //额外搜索条件
    let otherParam = {};
    if (openStatus) {
      otherParam = {
        item_stage: searchParmas.item_stage ? searchParmas.item_stage : '',
      };
    } else {
      otherParam = selectParams;
      // if (selectParams.dept_id) {
      //   otherParam = JSON.parse(
      //     JSON.stringify(selectParams).replace(/dept_id/g, 'dept'),
      //   );
      // }
      otherParam = Object.assign(otherParam, numberParams);
    }
    let resp = await downProjectListFile({ ...params, ...otherParam });
    console.log('respss', resp);
    const blob = new Blob([resp], {
      type: 'application/vnd.ms-excel;charset=uft-8',
    });
    console.log('blob', blob);
    const blobUrl = window.URL.createObjectURL(blob);
    console.log('blobUrl', blobUrl);
    download(blobUrl);
  };
  //自定义下载
  const handleOk = async () => {
    let labelAarr = [];
    plainOptions.forEach(items => {
      checkedLists.forEach(item => {
        if (item == items.value) {
          labelAarr.push(items.label);
        }
      });
    });

    let searchItemNames = checkedLists.join('##');
    let searchItemTexts = labelAarr.join('##');

    //openStatus==false 动态查询

    let selectParams = {};
    let numberParams = {};
    selectListItem.forEach(item => {
      if (item.name && item.itemValue.length > 0) {
        selectParams[item.name] = item.itemValue.join(',');
      }
    });
    selectScopetItem.forEach(item => {
      if (item.name) {
        let labelStart = null;
        let labelEnd = null;
        if (item.startValue) {
          labelStart = item.name + '_' + 'start';
        }
        if (item.endValue) {
          labelEnd = item.name + '_' + 'end';
        }
        if (labelStart) {
          numberParams[labelStart] = item.startValue;
        }
        if (labelEnd) {
          numberParams[labelEnd] = item.endValue;
        }
      }
    });

    let params = {
      isAsc: parmas.isAsc,
      isFollow: parmas.isFollow,
      itemName: openStatus ? searchParmas.name : searchParmasS.name,
      itemNum: openStatus ? searchParmas.itemNum : searchParmasS.itemNum,
      page: parmas.page,
      startTime: openStatus
        ? searchParmas.start_time
        : searchParmasS.start_time,
      endTime: openStatus ? searchParmas.end_time : searchParmasS.end_time,
      searchItemName: searchItemNames,
      searchItemText: searchItemTexts,
      type: parmas.type,
      orderByField: parmas.orderByField,
      limit: parmas.limit,
      standYear: openStatus ? searchParmas.standYear : searchParmasS.standYear,
    };
    console.log(params);
    //额外搜索条件
    let otherParam = {};
    if (openStatus) {
      otherParam = {
        item_stage: searchParmas.item_stage ? searchParmas.item_stage : '',
      };
    } else {
      otherParam = selectParams;
      // if (selectParams.dept_id) {
      //   otherParam = JSON.parse(
      //     JSON.stringify(selectParams).replace(/dept_id/g, 'dept'),
      //   );
      // }
      otherParam = Object.assign(otherParam, numberParams);
    }
    let resp = await downProjectListFile({ ...params, ...otherParam });
    console.log('resp', resp);
    const blob = new Blob([resp], {
      type: 'application/vnd.ms-excel;charset=uft-8',
    });
    console.log('blob', blob);
    const blobUrl = window.URL.createObjectURL(blob);
    console.log('blobUrl', blobUrl);
    download(blobUrl);
  };

  function download(blobUrl) {
    const a = document.createElement('a');
    a.download = '项目列表导出文件';
    a.href = blobUrl;
    a.click();
    setCheckAll(false);
    setCheckedLists([]);
    setIndeterminate(false);
    setVisibleModal(false);
  }

  const handleOkUpdate = () => {
    setVisibleModalUpdate(false);
  };

  const handleCancel = () => {
    setVisibleModal(false);
    setCheckAll(false);
    setIndeterminate(false);
    setCheckedLists([]);
  };

  const handleCancelUpdate = () => {
    setVisibleModalUpdate(false);
  };

  //方法--新增选择查询列表
  const addItem = () => {
    const item = { name: '', key: null, itemValue: [], itemOption: [] };
    let newItem = selectListItem;
    newItem.push(item);
    setNumFlush(numFlush + 1);
    setSelectListItem(newItem);
  };

  //方法--新增区间查询列表
  const addItems = () => {
    const item = { name: '', key: null, type: '' };
    let newItem = selectScopetItem;
    newItem.push(item);
    setNumFlush(numFlush + 1);
    setSelectScopeItem(newItem);
  };
  const optionsList = [
    '-',
    '立项部门',
    '项目名称',
    '项目编号',
    '项目状态',
    '项目类型',
    '项目级别',
    '收入分类',
    '项目把握度',
    '计划收入',
    '已确认收入',
    '确认收入占比',
    '计划成本',
    '已核成本',
    '已核成本占比',
    '当前里程碑',
    '当前里程碑进度百分比',
    '项目进度百分比',
    '计划起止时间',
    '协作部门',
    '项目经理',
  ];
  const initParmas = {
    page: 1,
    limit: 10,
    orderByField: 'item_plan_income',
    isAsc: 'true',
    type: '',
    isFollow: '',
    dept: '',
    level: '',
    in_come_type: '',
    item_state: '',
    item_grasp: '',
    item_stage_type: '',
  };
  const [checkedList, setCheckedList] = useState(optionsList);

  const [checkedLists, setCheckedLists] = useState(plainOptions);

  const [loading, setLoading] = useState(true);
  const [parmas, setParmas] = useState(initParmas);
  // 点击按钮查询
  const [searchParmas, setSearchParmas] = useState({
    name: '',
    itemNum: '',
    start_time: '',
    end_time: '',
    standYear: '',
  });

  // 动态查询
  const [searchParmasS, setSearchParmasS] = useState({
    name: '',
    itemNum: '',
    start_time: '',
    end_time: '',
    standYear: null,
  });

  // 独立的年份状态
  const [selectedYear, setSelectedYear] = useState(null);

  // 用于标记是否是动态查询分页操作，避免触发useEffect
  const isDynamicPagingRef = useRef(false);

  const operation = {
    title: '操作',
    key: 'operation',
    align: 'center',
    fixed: 'right',
    width: 40,
    render: (text, record) => {
      return (
        <div>
          {(buttonList.includes('/projectList/track') ||
            buttonList.includes('admin')) &&
            !(
              record.item_stage === 'completed_item' ||
              record.item_stage === 'terminated'
            ) && (
              <Link
                to={{
                  pathname: '/projectList/track',
                  query: {
                    itemNo: record.item_num,
                    id: record.id,
                    UpEndType: record.UpEndType,
                  },
                }}
              >
                跟踪
              </Link>
            )}
          {(buttonList.includes('/updateProject') ||
            buttonList.includes('admin')) &&
          (record.item_stage === 'completed_item' ||
            record.item_stage === 'terminated') ? (
            <>
              {' '}
              <span style={{ marginLeft: 8 }}>
                <Link
                  to={{
                    pathname: '/projectList/track',
                    query: {
                      itemNo: record.itemNum,
                      id: record.id,
                      over: 'over',
                    },
                  }}
                >
                  已结项
                </Link>
              </span>
            </>
          ) : (
            (buttonList.includes('/updateProject') ||
              buttonList.includes('admin')) &&
            record.MemberType !== '1' &&
            record.UpEndType.MemberType === '0' && (
              <Link
                to={{
                  pathname: '/updateProject',
                  query: {
                    id: record.id,
                    type: 'update',
                  },
                }}
                style={{ marginLeft: 8 }}
              >
                变更
              </Link>
              // <span onClick={openUpdate} className={styles.spanStyle}>
              //   变更
              // </span>
            )
          )}
          {(buttonList.includes('/closure') || buttonList.includes('admin')) &&
            !(
              record.item_stage === 'completed_item' ||
              record.item_stage === 'terminated'
            ) &&
            record.MemberType !== '1' &&
            record.UpEndType.MemberType === '0' && (
              <Link
                to={{
                  pathname: '/closure',
                  query: {
                    itemNo: record.item_num,
                    id: record.id,
                  },
                }}
                // disabled
                style={{ marginLeft: 8 }}
              >
                结项
              </Link>
            )}
          {record.UpEndType.MemberType === '3' && (
            <Link
              to={{
                pathname: '/projectExamine',
                query: {
                  id: record.UpEndType.id,
                  trial_ids: record.trial_ids,
                  lookOver: '1',
                },
              }}
              style={{ marginLeft: 10 }}
            >
              变更中
            </Link>
          )}
          {record.UpEndType.MemberType === '5' && (
            <Link
              to={{
                pathname: '/closureApporval',
                query: {
                  id: record.UpEndType.id,
                  trial_ids: record.trial_ids,
                  type: 'lookOver',
                },
              }}
              style={{ marginLeft: 10 }}
            >
              结项中
            </Link>
          )}
          {record.UpEndType.MemberType === '2' && (
            <Link
              to={{
                pathname: '/updateProject',
                query: {
                  id: record.UpEndType.id,
                  type: 'look',
                },
              }}
              style={{ marginLeft: 10 }}
            >
              变更中
            </Link>
          )}
          {record.UpEndType.MemberType === '4' && (
            <Link
              to={{
                pathname: '/closure',
                query: {
                  itemNo: record.itemNum,
                  id: record.UpEndType.id,
                },
              }}
              style={{ marginLeft: 10 }}
            >
              结项中
            </Link>
          )}
        </div>
      );
    },
  };

  const gerlist = async value => {
    const resp = await getDynamicProjectListPage({
      ...value,
      itemName: value?.name,
      dept_id: value?.dept,
    });
    console.log(resp);
    if (resp.code === 200) {
      setLoading(false);
      const idList = [];
      setKeys([]);
      resp.data.records.map(item => {
        if (item.is_follow === '0') {
          idList.push(item.id);
        }
        item.key = item.id;
      });
      setRecordParams({ data: idList });
      setData(resp.data.records);
      setDataTotal(resp.data.total);
    } else {
      setLoading(false);
      setData(null);
      setDataTotal(0);
    }
  };
  useEffect(() => {
    // 如果是动态查询分页操作，跳过这个useEffect
    if (isDynamicPagingRef.current) {
      isDynamicPagingRef.current = false;
      return;
    }
    setKeys([]);
    setLoading(true);
    gerlist({ ...parmas, ...searchParmas });
  }, [parmas]);

  useEffect(() => {}, [selectListItem]);

  // 获取表列的所有种类
  const getTotalDorpList = async () => {
    try {
      const response = await getSearchListInfo();
      const { data } = response || {};
      const list = [];

      if (data && Array.isArray(data)) {
        data.forEach(item => {
          // 确保 item 存在且有必要的属性
          if (item && item.type && item.name && item.code !== undefined) {
            if (item.type === 'item_level') {
              list['item_level'] = !list['item_level']
                ? []
                : list['item_level'];
              list['item_level'].push({ label: item.name, value: item.code });
            }
            if (item.type === 'item_type') {
              list['item_type'] = !list['item_type'] ? [] : list['item_type'];
              list['item_type'].push({ label: item.name, value: item.code });
            }
            if (item.type === 'in_come_type') {
              list['in_come_type'] = !list['in_come_type']
                ? []
                : list['in_come_type'];
              list['in_come_type'].push({ label: item.name, value: item.code });
            }
            if (item.type === 'item_stage_type') {
              list['item_stage_type'] = !list['item_stage_type']
                ? []
                : list['item_stage_type'];
              list['item_stage_type'].push({
                label: item.name,
                value: item.code,
              });
            }
            if (item.type === 'item_state') {
              list['item_state'] = !list['item_state']
                ? []
                : list['item_state'];
              list['item_state'].push({ label: item.name, value: item.code });
            }
            if (item.type === 'item_grasp') {
              list['item_grasp'] = !list['item_grasp']
                ? []
                : list['item_grasp'];
              list['item_grasp'].push({ label: item.name, value: item.code });
            }
            if (item.type === 'dept_name') {
              list['dept_name'] = !list['dept_name'] ? [] : list['dept_name'];
              list['dept_name'].push({ label: item.name, value: item.code });
            }
          }
        });
      }
      setDorwnList(list);
    } catch (error) {
      console.error('获取下拉列表数据失败:', error);
      setDorwnList([]);
    }
  };

  const getSerchInfos = async () => {
    let resp = await getSearchFieldInfo();
    if (resp.code === 200) {
      let newArr = resp.data.map(item => {
        let { id, name } = item;
        return {
          value: id,
          label: name,
        };
      });
      let allValue = [];
      newArr.forEach(item => {
        allValue.push(item.value);
      });
      setAllValue(allValue);
      setPlainOptions(newArr);
    }
  };
  const getSerchInfosNew = async () => {
    let resp = await getDynamicSearchInfo();
    if (resp.code === 200) {
      let selectDate = resp.data.filter(item => {
        if (item.type == 'checkbox') {
          return item;
        }
      });
      let newSelectDate = selectDate.map(items => {
        let { name, field, item, id } = items;
        return {
          name: name,
          value: field,
          isSelected: false,
          id: id,
          item: item,
        };
      });
      setSelectList(newSelectDate);

      let numberDate = resp.data.filter(item => {
        if (item.type == 'number') {
          return item;
        }
      });
      let newNumberDate = numberDate.map(items => {
        let { name, field, id } = items;
        return {
          name: name,
          value: field,
          isSelected: false,
          id: id,
        };
      });

      setSelectListCoped(newNumberDate);
      setSelectStatusCopd(newNumberDate.length);
      setSelectStatus(newSelectDate.length);
    }
  };

  useEffect(() => {
    getSerchInfos();
    getSerchInfosNew();
    getTotalDorpList();
    getDeptListInfos();
  }, []);

  const onChangePageNumber = (value, size) => {
    console.log('🔄 分页操作:', { value, size, openStatus });
    console.log('📊 当前状态:', {
      selectedYear,
      'searchParmasS.standYear': searchParmasS.standYear,
      searchParmas: searchParmas,
    });

    if (openStatus) {
      // 收起模式：调用 handleOnSearchTJ 并传递收起模式的查询参数
      handleOnSearchTJ({
        page: value,
        limit: size,
        // 传递收起模式的查询参数
        ...searchParmas,
      });
    } else {
      // 展开模式（动态查询模式）：调用 handleOnSearchTJ 来保持查询条件
      handleOnSearchTJ({ page: value, limit: size });
    }
  };

  //必须等有list才能显示columns
  useEffect(() => {
    setColumns(totalColumns);
  }, [dorwnList]);

  const {
    item_level,
    item_type,
    in_come_type,
    item_stage_type,
    item_state,
    item_grasp,
    dept_name,
  } = dorwnList;

  // 搜索icon
  const handleGetIcon = filtered => {
    return (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    );
  };
  //  表列所有下拉框
  const radioGrouop = (key, list) => {
    return (
      <div>
        <Radio.Group
          style={{ padding: '5px 10px' }}
          onChange={handleSelectVal.bind(this, key)}
        >
          <Radio
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
            value=""
          >
            所有
          </Radio>
          {list
            ? list.map(item => {
                return (
                  <Radio
                    key={item.value}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                    value={item.value}
                  >
                    {item.label}
                  </Radio>
                );
              })
            : null}
        </Radio.Group>
      </div>
    );
  };
  //   表列下拉框改变值
  const handleSelectVal = (key, e) => {
    setParmas(parmas => {
      return { ...parmas, [key]: e.target.value };
    });
  };
  //动态表头
  const totalColumns = [
    {
      title: '-',
      dataIndex: '12312323',
      key: '233',
      fixed: 'left',
      width: 10,
      align: 'center',
      ellipsis: {
        showTitle: false,
      },
      render: (name, record) => (
        <Tooltip placement="topLeft" title="我关注的项目">
          {record.is_follow === '0' ? <img src={follow_icon} alt="" /> : ''}
        </Tooltip>
      ),
    },
    {
      title: '项目名称',
      ellipsis: true,
      width: 30,
      fixed: 'left',
      dataIndex: 'name',
      key: '100',
    },
    {
      title: '项目编号',
      dataIndex: 'item_num',
      key: '2',
      width: 40,
      align: 'left',
      render: itemNum => (
        <Tooltip placement="topLeft" title={itemNum}>
          {itemNum}
        </Tooltip>
      ),
    },
    {
      title: '里程碑进度',
      dataIndex: 'scheduleSpeed',
      key: '5-2',
      align: 'center',
      width: 30,
      render: value => (
        <div className={styles.colorDiv}>
          {value == 0 ? (
            <div style={{ backgroundColor: 'green' }}></div>
          ) : value == 1 ? (
            <div style={{ backgroundColor: 'yellow' }}></div>
          ) : (
            <div style={{ backgroundColor: 'red' }}></div>
          )}
        </div>
      ),
    },
    {
      title: '项目收入',
      dataIndex: 'itemIncomeLight',
      key: '5-3',
      align: 'center',
      width: 30,
      render: value => (
        <div className={styles.colorDiv}>
          {value == 0 ? (
            <div style={{ backgroundColor: 'green' }}></div>
          ) : value == 1 ? (
            <div style={{ backgroundColor: 'yellow' }}></div>
          ) : (
            <div style={{ backgroundColor: 'red' }}></div>
          )}
        </div>
      ),
    },
    {
      title: '完工率',
      dataIndex: 'completionRate',
      key: '5-4',
      render: value => Number(value) + '%',
      align: 'center',
      width: 30,
    },
    {
      title: '立项部门',
      align: 'left',
      dataIndex: 'dept',
      ellipsis: true,
      key: '3',
      width: 30,
      filterIcon: filtered => handleGetIcon(filtered),
      filterDropdown: radioGrouop('dept', deptList),
    },
    {
      title: '项目状态',
      dataIndex: 'item_state',
      key: '4',
      width: 35,
      align: 'left',
      filterIcon: filtered => handleGetIcon(filtered),
      filterDropdown: radioGrouop('item_state', item_state),
    },
    {
      title: '项目类型',
      dataIndex: 'type',
      key: '5',
      width: 30,
      align: 'left',
    },
    {
      title: '项目级别',
      dataIndex: 'level',
      filterIcon: filtered => handleGetIcon(filtered),
      filterDropdown: radioGrouop('level', item_level),
      key: '6',
      filterMultiple: false,
      width: 25,
      align: 'left',
    },
    {
      title: '收入分类',
      dataIndex: 'in_come_type',
      key: '7',
      width: 45,
      align: 'left',
      filterIcon: filtered => handleGetIcon(filtered),
      filterDropdown: radioGrouop('in_come_type', in_come_type),
    },
    {
      title: '项目把握度',
      dataIndex: 'item_grasp',
      key: '8',
      align: 'left',
      width: 40,
      filterIcon: filtered => handleGetIcon(filtered),
      filterDropdown: radioGrouop('item_grasp', item_grasp),
    },
    {
      title: '计划收入',
      dataIndex: 'item_plan_income',
      sorter: (a, b) => a.item_plan_income - b.item_plan_income,
      key: '9',
      align: 'left',
      render: item_plan_income => <span>{item_plan_income}W</span>,
      width: 30,
    },
    {
      title: '已确认收入',
      dataIndex: 'inComeVer',
      key: '10',
      align: 'left',
      sorter: (a, b) => a.inComeVer - b.inComeVer,
      render: inComeVer => <span>{inComeVer}W</span>,
      width: 30,
    },
    {
      title: '确认收入占比',
      dataIndex: 'inComeZB',
      key: '11',
      align: 'left',
      sorter: (a, b) => a.inComeZB - b.inComeZB,
      render: inComeZB => (
        <div style={{ overflow: 'hidden' }}>
          <Tooltip
            placement="topLeft"
            title={`${(inComeZB !== 0 && Number(inComeZB).toFixed(2)) || 0}%`}
          >
            <Progress
              percent={inComeZB}
              status="normal"
              format={() =>
                `${(inComeZB !== 0 && Number(inComeZB).toFixed(2)) || 0}%`
              }
              style={{ width: '70%' }}
            />
          </Tooltip>
        </div>
      ),
      width: 40,
    },
    {
      title: '计划成本',
      dataIndex: 'item_cost_budgeting',
      key: '12',
      align: 'left',
      sorter: (a, b) => a.item_cost_budgeting - b.item_cost_budgeting,
      render: item_cost_budgeting => <span>{item_cost_budgeting}W</span>,
      width: 30,
    },
    {
      title: '已核成本',
      dataIndex: 'costVer',
      key: '13',
      align: 'left',
      sorter: (a, b) => a.costVer - b.costVer,
      render: costVer => <span>{costVer}W</span>,
      width: 30,
    },
    {
      title: '已核成本占比',
      dataIndex: 'costZB',
      key: '14',
      align: 'left',
      render: costZB => (
        <div style={{ overflow: 'hidden' }}>
          <Tooltip
            placement="topLeft"
            title={`${(costZB !== 0 && Number(costZB).toFixed(2)) || 0}%`}
          >
            <Progress
              percent={costZB}
              status="normal"
              format={() =>
                `${(costZB !== 0 && Number(costZB).toFixed(2)) || 0}%`
              }
              style={{ width: '70%' }}
            />
          </Tooltip>
        </div>
      ),

      width: 40,
    },
    {
      title: '当前里程碑',
      dataIndex: 'item_stage_type',
      key: '15',
      align: 'left',
      filterIcon: filtered => handleGetIcon(filtered),
      filterDropdown: radioGrouop('item_stage_type', item_stage_type),
      width: 30,
    },
    {
      title: '当前里程碑进度百分比',
      dataIndex: 'stage_type_percent',
      key: '16',
      align: 'left',
      render: stage_type_percent => (
        <div style={{ overflow: 'hidden' }}>
          <Tooltip
            placement="topLeft"
            title={`${(stage_type_percent !== 0 &&
              Number(stage_type_percent).toFixed(2)) ||
              0}%`}
          >
            <Progress
              percent={stage_type_percent}
              status="normal"
              format={() =>
                `${(stage_type_percent !== 0 &&
                  Number(stage_type_percent).toFixed(2)) ||
                  0}%`
              }
              style={{ width: '60%' }}
            />
          </Tooltip>
        </div>
      ),
      width: 55,
    },
    {
      title: '项目进度百分比',
      dataIndex: 'schedule',
      key: '17',
      align: 'left',
      render: schedule => (
        <div style={{ overflow: 'hidden' }}>
          <Tooltip
            placement="topLeft"
            title={`${(schedule !== 0 && Number(schedule).toFixed(2)) || 0}%`}
          >
            <Progress
              percent={schedule}
              status="normal"
              format={() =>
                `${(schedule !== 0 && Number(schedule).toFixed(2)) || 0}%`
              }
              style={{ width: '70%' }}
            />
          </Tooltip>
        </div>
      ),
      width: 40,
    },

    {
      title: `计划起止时间`,
      key: '18',
      align: 'left',
      sorter: (a, b) => {
        const aTime = new Date(a.start_time).getTime();
        const bTime = new Date(b.start_time).getTime();
        return aTime - bTime;
      },
      width: 60,
      render: render => (
        <div>
          {render.start_time} 至 {render.end_time}
        </div>
      ),
    },
    {
      title: '协作部门',
      dataIndex: 'co_dept',
      key: 'co_dept',
      align: 'left',
      width: 20,
      ellipsis: true,
    },
    {
      title: '项目经理',
      dataIndex: 'manager',
      key: '19',
      align: 'left',
      width: 20,
      ellipsis: true,
    },
    {
      title: '立项时间',
      dataIndex: 'itemTime',
      key: '19',
      align: 'left',
      width: 20,
      ellipsis: true,
    },
    operation,
  ];

  // 改变列的值  以后用pro/table自带过滤
  const handleChangeMenu = checkedValues => {
    if (optionsList.length === checkedValues && checkedValues.length) {
      setColumns(totalColumns);
      setCheckAll(true);
      setCheckedList(optionsList);
    } else {
      const newList = totalColumns;
      const nameList = [];
      const res = newList.filter(item => {
        const flag = checkedValues && checkedValues.indexOf(item.title) !== -1;
        if (flag) {
          nameList.push(item.title);
        }
        setCheckedList(optionsList);
        return flag;
      });
      setColumns([...res, operation]);
      setCheckedList(nameList);
      setCheckAll(false);
    }
  };

  const rowSelection = {
    // 设置key值单选框才能被选中
    onChange: (selectedRowKeys, selectedRows) => {
      const idArr = [];
      selectedRows.map(item => {
        idArr.push(item.id);
      });
      setRecordParams(recordParams => {
        return { ...recordParams, data: idArr };
      });
      setKeys(selectedRowKeys);
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      const idList = [];
      const currentList = selected ? selectedRows : changeRows;
      currentList.map(item => {
        idList.push(item.id);
      });
      setRecordParams(setRecordParams => {
        return { ...setRecordParams, data: idList, selected };
      });
    },
    selectedRowKeys: keys,
  };

  /**
   * 搜索值变化
   */
  const handleSearchParams = (key, value) => {
    setSearchParmas({ ...searchParmas, [key]: value });
  };

  //动态查询
  const handleSearchParamsS = (key, value) => {
    setSearchParmasS({ ...searchParmasS, [key]: value });
  };
  const handleSearchParamsSDate = (key, value, date) => {
    setSearchParmasS({ ...searchParmasS, [key]: value });
  };

  // 搜索
  const handleOnSearch = () => {
    setParmas({ ...parmas, page: 1 });
  };

  //多维条件查询
  const handleOnSearchTJ = async pag => {
    console.log('🔍 handleOnSearchTJ 调用:', { pag, openStatus });
    console.log('📊 查询前状态:', {
      selectedYear,
      searchParmasS,
      searchParmas,
    });

    // 根据模式选择正确的查询参数源
    let baseSearchParams;
    if (openStatus) {
      // 收起模式：使用 searchParmas 和传入的 pag 参数
      baseSearchParams = {
        name: pag?.name || searchParmas.name,
        itemNum: pag?.itemNum || searchParmas.itemNum,
        start_time: pag?.start_time || searchParmas.start_time,
        end_time: pag?.end_time || searchParmas.end_time,
        standYear: pag?.standYear || searchParmas.standYear,
        item_stage: pag?.item_stage || searchParmas.item_stage,
      };
    } else {
      // 展开模式：使用 searchParmasS
      baseSearchParams = {
        name: searchParmasS.name,
        itemNum: searchParmasS.itemNum,
        start_time: searchParmasS.start_time,
        end_time: searchParmasS.end_time,
        standYear:
          selectedYear || searchParmasS.standYear || searchParmas.standYear,
      };
    }

    let paramsNew = {
      page: pag?.page || parmas.page,
      limit: pag?.limit || parmas.limit,
      isAsc: parmas.isAsc,
      isFollow: parmas.isFollow,
      orderByField: parmas.orderByField,
      type: parmas.type,
      dept: parmas.dept,
      level: parmas.level,
      in_come_type: parmas.in_come_type,
      item_state: parmas.item_state,
      item_grasp: parmas.item_grasp,
      item_stage_type: parmas.item_stage_type,
      // 使用根据模式选择的查询参数
      ...baseSearchParams,
      itemName: baseSearchParams.name,
    };

    console.log('📋 paramsNew 构建完成:', paramsNew);

    let selectParams = {};
    let numberParams = {};
    selectListItem.forEach(item => {
      if (item.name && item.itemValue.length > 0) {
        selectParams[item.name] = item.itemValue.join(',');
      }
    });
    selectScopetItem.forEach(item => {
      if (item.name) {
        let labelStart = null;
        let labelEnd = null;
        if (item.startValue) {
          labelStart = item.name + '_' + 'start';
        }
        if (item.endValue) {
          labelEnd = item.name + '_' + 'end';
        }
        if (labelStart) {
          numberParams[labelStart] = item.startValue;
        }
        if (labelEnd) {
          numberParams[labelEnd] = item.endValue;
        }
      }
    });

    // 过滤掉 searchParmasS 中的 null 和 undefined 值，避免覆盖 paramsNew 中的正确值
    const filteredSearchParmasS = Object.fromEntries(
      Object.entries(searchParmasS).filter(
        ([key, value]) => value !== null && value !== undefined && value !== '',
      ),
    );

    let serchParam = {
      ...numberParams,
      ...selectParams,
      ...paramsNew,
      ...filteredSearchParmasS,
    };

    console.log('📤 最终请求参数:', serchParam);
    let resp = await getDynamicProjectListPage({
      ...serchParam,
      itemName: serchParam?.name,
      dept_id: serchParam?.dept,
    });

    if (resp.code === 200) {
      setLoading(false);
      const idList = [];
      setKeys([]);
      resp.data.records.map(item => {
        if (item.is_follow === '0') {
          idList.push(item.id);
        }
        item.key = item.id;
      });
      setRecordParams({ data: idList });
      setData(resp.data.records);
      setDataTotal(resp.data.total);

      // 如果是分页操作，更新分页状态（但不触发useEffect）
      if (pag?.page && pag.page !== parmas.page) {
        isDynamicPagingRef.current = true;
        setParmas(prev => ({
          ...prev,
          page: pag.page,
          limit: pag.limit || prev.limit,
        }));
      }
    } else {
    }
  };

  //  类别
  const handleOnChangeType = key => {
    const { type } = parmas;
    if (
      (key === 'manage' && type == 'manage') ||
      (key === 'no_manage' && type == 'no_manage')
    ) {
      setParmas({ ...parmas, type: '' });
    } else {
      setParmas({ ...parmas, type: key });
    }

    if (key == 'no_manage') {
      handleChangeMenu([
        '-',
        '立项部门',
        '项目名称',
        '项目编号',
        '项目状态',
        '项目类型',
        '项目级别',
        '项目把握度',
        '计划成本',
        '已核成本',
        '已核成本占比',
        '当前里程碑',
        '当前里程碑进度百分比',
        '项目进度百分比',
        '计划起止时间',
        '协作部门',
        '项目经理',
        '里程碑进度',
        '项目收入',
        '完工率',
      ]);
    } else {
      handleChangeMenu([
        '-',
        '立项部门',
        '项目名称',
        '项目编号',
        '项目状态',
        '项目类型',
        '项目级别',
        '收入分类',
        '项目把握度',
        '计划收入',
        '已确认收入',
        '确认收入占比',
        '计划成本',
        '已核成本',
        '已核成本占比',
        '当前里程碑',
        '当前里程碑进度百分比',
        '项目进度百分比',
        '计划起止时间',
        '协作部门',
        '项目经理',
        '里程碑进度',
        '项目收入',
        '完工率',
      ]);
    }
  };
  // 关注
  const handleInterestChange = () => {
    const { isFollow } = parmas;
    if (isFollow === '0') {
      setParmas({ ...parmas, isFollow: '' });
    } else {
      setParmas({ ...parmas, isFollow: '0' });
    }
  };
  //点击关注项目
  const selectFocus = async () => {
    const { data } = recordParams;
    const { code } = await updateFollowState({
      item_id: data.toString(),
      state: '0',
    });
    if (code === 200) {
      message.success('关注成功');
      gerlist({ ...parmas, ...searchParmas });
    } else {
      message.error('关注失败');
    }
  };
  //点击取消关注
  const cancelFocus = async () => {
    const { data } = recordParams;
    const { code } = await updateFollowState({
      item_id: data.toString(),
      state: '-1',
    });
    if (code === 200) {
      message.success('取消关注成功');
      gerlist({ ...parmas, ...searchParmas });
    } else {
      message.error('取消关注失败');
    }
  };
  //改变每页显示条数
  const pageSizeChange = value => {
    setParmas({ ...parmas, limit: value });
  };
  const { limit, page, type, isFollow } = parmas;

  let timeout;
  function fetch(value, callback) {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    function fake() {
      const getItemNameLists = async value => {
        const { code, data } = await getItemNameList({
          page: 1,
          limit: 10,
          name: value,
        });
        if (code === 200) {
          callback([{ name: value }, ...data]);
        }
      };
      getItemNameLists(value);
    }
    timeout = setTimeout(fake, 300);
  }
  //获取部门下拉
  const getDeptListInfos = async value => {
    const { code, data } = await getDeptListInfo(value);
    if (code && code === 200) {
      const addDeptList = [];
      data.forEach(item => {
        let repeat = false;
        addDeptList.forEach(value => {
          if (value.deptFullName === item.deptFullName) {
            repeat = true;
          }
        });
        if (!repeat) {
          item.deptFullName &&
            addDeptList.push({ label: item.deptFullName, value: item.id });
        }
      });
      setDeptList(addDeptList);
    }
  };
  const changeOpen = value => {
    if (value == 'close') {
      setOpenStatus(true);
    } else {
      setOpenStatus(false);
    }
  };

  // 展开模式重置
  const serchReset = () => {
    setSearchParmasS({
      name: '',
      itemNum: '',
      start_time: null,
      end_time: null,
      standYear: null,
    });
    setSelectedYear(null);

    setSelectScopeItem([
      {
        name: '',
        key: null,
        type: '',
        startValue: '',
        endValue: '',
        standYear: null,
      },
    ]);
    setSelectListItem([{ name: '', key: 0, itemValue: [], itemOption: [] }]);
    let selectItemsCoped = selectListCoped;
    let selectItems = selectList;
    selectItemsCoped.forEach(selectItem => {
      selectItem.isSelected = false;
    });
    selectItems.forEach(selectItem => {
      selectItem.isSelected = false;
    });
    setSelectListCoped(selectItemsCoped);
    setSelectList(selectItems);
    let parmas = {
      page: 1,
      limit: 10,
    };
    getSerchInfosNew();
    gerlist({ ...parmas });
  };

  // 收起模式重置
  const searchReset = () => {
    console.log('🔄 收起模式重置');
    setSearchParmas({
      name: '',
      itemNum: '',
      start_time: '',
      end_time: '',
      standYear: '',
      item_stage: '',
    });
    setParmas({ ...initParmas, page: 1 });
  };

  return (
    <div className="content">
      <div className={styles.card}>
        <div className={styles.searchInput}>
          {openStatus && (
            <>
              <div className={styles.searchItema}>
                <p>项目名称</p>
                <Input
                  allowClear
                  value={searchParmas.name}
                  onChange={e => handleSearchParams('name', e.target.value)}
                  placeholder="项目名称"
                  className={styles.selectSerch}
                ></Input>
              </div>

              <div className={styles.searchItema}>
                <p>项目编号</p>
                <Input
                  allowClear
                  value={searchParmas.itemNum}
                  onChange={e => handleSearchParams('itemNum', e.target.value)}
                  placeholder="请输入"
                ></Input>
              </div>
              <div className={styles.esarchItema}>
                <p>项目状态</p>
                <Select
                  allowClear
                  value={searchParmas.item_stage}
                  style={{ width: '100%' }}
                  placeholder="请选择"
                  className={styles.selects}
                  onChange={value => handleSearchParams('item_stage', value)}
                >
                  <Option value="running">进行中</Option>
                  <Option value="end">已结项</Option>
                </Select>
              </div>
              <div className={styles.esarchItemb} style={{ display: 'flex' }}>
                <div style={{ marginRight: 20 }}>
                  <p>项目开始时间</p>
                  <DatePicker
                    allowClear
                    value={
                      searchParmas.start_time
                        ? moment(searchParmas.start_time)
                        : null
                    }
                    onChange={(date, dateString) =>
                      handleSearchParams('start_time', dateString)
                    }
                    format={dateFormat}
                    suffixIcon={<ClockCircleOutlined />}
                    disabledDate={current => {
                      return (
                        searchParmas.end_time &&
                        current > moment(searchParmas.end_time)
                      );
                    }}
                    className={styles.rangePicker}
                  />
                </div>
                <div>
                  <p>项目结束时间</p>
                  <DatePicker
                    allowClear
                    value={
                      searchParmas.end_time
                        ? moment(searchParmas.end_time)
                        : null
                    }
                    onChange={(date, dateString) =>
                      handleSearchParams('end_time', dateString)
                    }
                    format={dateFormat}
                    suffixIcon={<ClockCircleOutlined />}
                    disabledDate={current => {
                      return (
                        searchParmas.start_time &&
                        current < moment(searchParmas.start_time)
                      );
                    }}
                    className={styles.rangePicker}
                  />
                </div>
              </div>
              <div>
                <p>立项时间年份</p>
                <DatePicker
                  allowClear
                  value={
                    searchParmas.standYear
                      ? moment(searchParmas.standYear)
                      : null
                  }
                  onChange={(date, dateString) =>
                    handleSearchParams('standYear', dateString)
                  }
                  picker="year"
                  suffixIcon={<ClockCircleOutlined />}
                  className={styles.rangePicker}
                />
              </div>
              <div className={styles.esarchItemc}>
                <p></p>
                <br />
                <Button onClick={searchReset} style={{ marginRight: 8 }}>
                  重置
                </Button>
                <Button type="primary" onClick={handleOnSearch}>
                  查询
                </Button>
              </div>

              <span
                onClick={() => changeOpen('open')}
                className={styles.openModal}
              >
                展开
                <svg
                  t="1629343479823"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="1190"
                  width="16"
                  height="16"
                >
                  <path
                    d="M512 672c-6.4 0-14.933333-2.133333-21.333333-6.4L157.866667 392.533333c-10.666667-8.533333-10.666667-23.466667 0-34.133333 10.666667-8.533333 29.866667-8.533333 40.533333 0l313.6 256 313.6-256c10.666667-8.533333 29.866667-8.533333 40.533333 0s10.666667 23.466667 0 34.133333L533.333333 665.6c-6.4 4.266667-14.933333 6.4-21.333333 6.4z"
                    fill="#1296db"
                    p-id="1191"
                  ></path>
                </svg>
              </span>
            </>
          )}
          {!openStatus && (
            <>
              <div className={styles.searchBox}>
                <div className={styles.searchBoxName}>搜索查询</div>
                <div className={styles.searchBoxContent}>
                  <div className={styles.searchBoxGroup}>
                    <Input
                      value={searchParmasS.name}
                      onChange={e =>
                        handleSearchParamsS('name', e.target.value)
                      }
                      placeholder="项目名称"
                      className={styles.selects}
                    ></Input>

                    <Input
                      value={searchParmasS.itemNum}
                      onChange={e =>
                        handleSearchParamsS('itemNum', e.target.value)
                      }
                      placeholder="项目编号"
                    ></Input>
                  </div>
                </div>
              </div>
              <div className={styles.searchBox}>
                <div className={styles.searchBoxName}>选择查询</div>
                <div className={styles.searchBoxContent}>
                  {selectListItem.length > 0 &&
                    selectListItem.map((item, index) => {
                      return (
                        <>
                          <div className={styles.searchBoxGroup}>
                            <Select
                              value={item.name}
                              showSearch
                              allowClear={true}
                              placeholder="请输入"
                              style={{ width: '160px' }}
                              className={styles.searchGruopSelect}
                              onChange={value =>
                                handleSearchSelect(value, index)
                              }
                              filterOption={(input, option) =>
                                option.children
                                  .toLowerCase()
                                  .indexOf(input.toLowerCase()) >= 0
                              }
                            >
                              {selectList &&
                                selectList.length > 0 &&
                                selectList.map((item, index) => (
                                  <Option
                                    value={item.value}
                                    key={index}
                                    disabled={item.isSelected}
                                  >
                                    {item.name}
                                  </Option>
                                ))}
                            </Select>
                            <span>-</span>
                            <Select
                              value={item.itemValue}
                              mode="multiple"
                              maxTagCount="responsive"
                              className={styles.searchGruopSelects}
                              showSearch
                              allowClear={true}
                              placeholder="请输入"
                              style={{ width: '345px' }}
                              onChange={value =>
                                handleSearchSelects(value, index)
                              }
                              filterOption={(input, option) =>
                                option.children
                                  .toLowerCase()
                                  .indexOf(input.toLowerCase()) >= 0
                              }
                            >
                              {item.itemOption &&
                                item.itemOption.length > 0 &&
                                item.itemOption.map((item, index) => (
                                  <Option
                                    value={item.id}
                                    key={item.name + item.id}
                                  >
                                    {item.name}
                                  </Option>
                                ))}
                            </Select>
                            {index + 1 == selectListItem.length &&
                              selectStatus > 0 && (
                                <span
                                  onClick={addItem}
                                  className={styles.addSlelect}
                                >
                                  <svg
                                    t="1629441922155"
                                    viewBox="0 0 1024 1024"
                                    version="1.1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    p-id="1198"
                                    width="20"
                                    height="20"
                                  >
                                    <path
                                      d="M512.8 101.2C283.7 101.2 98 286.1 98 514.2s185.7 413 414.8 413c229.1 0 414.8-184.9 414.8-413s-185.7-413-414.8-413z m0 768.6c-197.2 0-357.1-159.2-357.1-355.6 0-196.4 159.9-355.5 357.1-355.5 197.2 0 357.1 159.2 357.1 355.5-0.1 196.4-159.9 355.6-357.1 355.6z"
                                      fill="#2680F0"
                                      p-id="1199"
                                    ></path>
                                    <path
                                      d="M536.5 728c0 5.5-4.5 10-10 10l-28.5 0.1c-5.5 0-10-4.5-10-10l1.1-427.7c0-5.5 4.5-10 10-10l28.5-0.1c5.5 0 10 4.5 10 10L536.5 728z"
                                      fill="#2680F0"
                                      p-id="1200"
                                    ></path>
                                    <path
                                      d="M726.4 487.1c5.5 0 10 4.5 10 10l0.1 35.4c0 5.5-4.5 10-10 10l-427.4-1.3c-5.5 0-10-4.5-10-10l-0.1-35.4c0-5.5 4.5-10 10-10l427.4 1.3z"
                                      fill="#2680F0"
                                      p-id="1201"
                                    ></path>
                                  </svg>
                                </span>
                              )}
                          </div>
                        </>
                      );
                    })}
                </div>
              </div>
              <div className={styles.searchBox}>
                <div className={styles.searchBoxName}>区间查询</div>
                <div className={styles.searchBoxContent}>
                  {selectScopetItem.length > 0 &&
                    selectScopetItem.map((item, index) => {
                      return (
                        <div className={styles.searchBoxGroup}>
                          <Select
                            value={item.name}
                            showSearch
                            allowClear={true}
                            placeholder="请输入"
                            style={{ width: '185px' }}
                            className={styles.searchGruopSelects}
                            onChange={value =>
                              handleSearchSelectScoped(value, index)
                            }
                            filterOption={(input, option) =>
                              option.children
                                .toLowerCase()
                                .indexOf(input.toLowerCase()) >= 0
                            }
                          >
                            {selectListCoped &&
                              selectListCoped.length > 0 &&
                              selectListCoped.map((item, index) => (
                                <Option
                                  value={item.value}
                                  key={item.id}
                                  disabled={item.isSelected}
                                >
                                  {item.name}
                                </Option>
                              ))}
                          </Select>
                          <span>-</span>
                          <div className={styles.copedItemRight}>
                            {item.type === 'number' ? (
                              <>
                                <InputNumber
                                  value={item.startValue}
                                  style={{ width: '120px' }}
                                  min={0}
                                  disabled={true}
                                  formatter={value =>
                                    `$ ${value}`.replace(
                                      /\B(?=(\d{3})+(?!\d))/g,
                                      ',',
                                    )
                                  }
                                  parser={value =>
                                    value.replace(/\$\s?|(,*)/g, '')
                                  }
                                  onChange={value =>
                                    handleSearchSelectCoped(
                                      value,
                                      index,
                                      'start',
                                    )
                                  }
                                />
                                <span className={styles.textSpan}>至</span>
                                <InputNumber
                                  value={item.endValue}
                                  min={0}
                                  style={{ width: '120px' }}
                                  formatter={value => `${value}`}
                                  parser={value => value.replace('W', '')}
                                  className={styles.searchGruopSelects}
                                  onChange={value =>
                                    handleSearchSelectCoped(value, index, 'end')
                                  }
                                />
                              </>
                            ) : item.type === 'double' ? (
                              <>
                                <InputNumber
                                  style={{ width: '120px' }}
                                  min={0}
                                  max={100}
                                  formatter={value => `${value}%`}
                                  parser={value => value.replace('%', '')}
                                  className={styles.searchGruopSelects}
                                />
                                <span className={styles.textSpan}>至</span>
                                <InputNumber
                                  style={{ width: '120px' }}
                                  min={0}
                                  max={100}
                                  formatter={value => `${value}%`}
                                  parser={value => value.replace('%', '')}
                                  className={styles.searchGruopSelects}
                                />
                              </>
                            ) : (
                              <>
                                <InputNumber
                                  style={{ minWidth: '120px' }}
                                  min={0}
                                  value={item.startValue}
                                  formatter={value => `${value}`}
                                  parser={value => value.replace('W', '')}
                                  className={styles.searchGruopSelects}
                                  onChange={value =>
                                    handleSearchSelectCoped(
                                      value,
                                      index,
                                      'start',
                                    )
                                  }
                                />
                                <span className={styles.textSpan}>至</span>
                                <InputNumber
                                  value={item.endValue}
                                  min={0}
                                  style={{ minWidth: '120px' }}
                                  formatter={value => `${value}`}
                                  parser={value => value.replace('W', '')}
                                  className={styles.searchGruopSelects}
                                  onChange={value =>
                                    handleSearchSelectCoped(value, index, 'end')
                                  }
                                />
                              </>
                            )}
                          </div>
                          {index + 1 == selectScopetItem.length &&
                            selectStatusCopd > 1 && (
                              <span
                                onClick={addItems}
                                className={styles.addSlelect}
                              >
                                <svg
                                  t="1629441922155"
                                  class="icon"
                                  viewBox="0 0 1024 1024"
                                  version="1.1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  p-id="1198"
                                  width="20"
                                  height="20"
                                >
                                  <path
                                    d="M512.8 101.2C283.7 101.2 98 286.1 98 514.2s185.7 413 414.8 413c229.1 0 414.8-184.9 414.8-413s-185.7-413-414.8-413z m0 768.6c-197.2 0-357.1-159.2-357.1-355.6 0-196.4 159.9-355.5 357.1-355.5 197.2 0 357.1 159.2 357.1 355.5-0.1 196.4-159.9 355.6-357.1 355.6z"
                                    fill="#2680F0"
                                    p-id="1199"
                                  ></path>
                                  <path
                                    d="M536.5 728c0 5.5-4.5 10-10 10l-28.5 0.1c-5.5 0-10-4.5-10-10l1.1-427.7c0-5.5 4.5-10 10-10l28.5-0.1c5.5 0 10 4.5 10 10L536.5 728z"
                                    fill="#2680F0"
                                    p-id="1200"
                                  ></path>
                                  <path
                                    d="M726.4 487.1c5.5 0 10 4.5 10 10l0.1 35.4c0 5.5-4.5 10-10 10l-427.4-1.3c-5.5 0-10-4.5-10-10l-0.1-35.4c0-5.5 4.5-10 10-10l427.4 1.3z"
                                    fill="#2680F0"
                                    p-id="1201"
                                  ></path>
                                </svg>
                              </span>
                            )}
                        </div>
                      );
                    })}
                </div>
              </div>
              <div className={styles.searchBox}>
                <div className={styles.searchBoxName}>时间查询</div>
                <div className={styles.searchBoxContent}>
                  <DatePicker
                    format="YYYY/MM/DD"
                    placeholder="项目开始时间"
                    value={
                      searchParmasS.start_time
                        ? moment(searchParmasS.start_time, dateFormat)
                        : ''
                    }
                    style={{ width: '210px', marginRight: '20px' }}
                    onChange={(date, dateString) =>
                      handleSearchParamsSDate('start_time', dateString, date)
                    }
                    suffixIcon={<ClockCircleOutlined />}
                    disabledDate={current => {
                      return (
                        searchParmasS.end_time &&
                        current > moment(searchParmasS.end_time)
                      );
                    }}
                    className={styles.rangePicker}
                  />
                  <DatePicker
                    placeholder="项目结束时间"
                    style={{ width: '210px' }}
                    value={
                      searchParmasS.end_time
                        ? moment(searchParmasS.end_time, dateFormat)
                        : ''
                    }
                    onChange={(date, dateString) =>
                      handleSearchParamsSDate('end_time', dateString, date)
                    }
                    format={dateFormat}
                    suffixIcon={<ClockCircleOutlined />}
                    disabledDate={current => {
                      return (
                        searchParmasS.start_time &&
                        current < moment(searchParmasS.start_time)
                      );
                    }}
                    className={styles.rangePicker}
                  />
                </div>
              </div>
              <div className={styles.searchBox}>
                <div className={styles.searchBoxName}>年份查询</div>
                <div className={styles.searchBoxContent}>
                  <div className={styles.searchBoxContentTime}>
                    <div className={styles.ContentTimeLeft}>
                      <DatePicker
                        allowClear
                        placeholder="请选择年份"
                        value={
                          selectedYear ? moment(selectedYear, 'YYYY') : null
                        }
                        onChange={(date, dateString) => {
                          console.log('🗓️ 年份选择器变化:', {
                            date,
                            dateString,
                          });
                          setSelectedYear(dateString);
                          setSearchParmasS(prev => {
                            const newState = { ...prev, standYear: dateString };
                            console.log('📊 更新后的 searchParmasS:', newState);
                            return newState;
                          });
                        }}
                        picker="year"
                        suffixIcon={<ClockCircleOutlined />}
                        className={styles.rangePicker}
                      />
                    </div>
                    <div className={styles.ContentTimeRight}>
                      <Button onClick={serchReset}>重置</Button>
                      <Button onClick={handleOnSearchTJ} type="primary">
                        查询
                      </Button>
                      <span
                        onClick={() => changeOpen('close')}
                        className={styles.openModal}
                      >
                        收起
                        <svg
                          t="1629451343926"
                          class="icon"
                          viewBox="0 0 1024 1024"
                          version="1.1"
                          xmlns="http://www.w3.org/2000/svg"
                          p-id="2617"
                          width="16"
                          height="16"
                        >
                          <path
                            d="M818.393225 712.230324c12.824073 14.09502 34.658358 15.126512 48.752354 2.303462 14.09502-12.843516 15.126512-34.678824 2.302439-48.752354l-332.676845-364.835266c-12.844539-14.114462-34.659381-15.127536-48.753377-2.302439-0.815575 0.733711-1.588171 1.486864-2.302439 2.302439l-0.080841 0.078795-0.13917 0.13917L153.018046 665.780409c-12.824073 14.074553-11.791557 35.909861 2.302439 48.752354 14.09502 12.824073 35.930327 11.792581 48.753377-2.303462l307.168891-336.845795 307.149449 336.845795L818.393225 712.230324 818.393225 712.230324z"
                            p-id="2618"
                            fill="#1296db"
                          ></path>
                        </svg>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      <div className={styles.card}>
        <ProTable
          columns={columns}
          dataSource={data}
          scroll={{ x: (checkedList.length + 3) * 160 }}
          pagination={false}
          rowSelection={{ columnWidth: 10, ...rowSelection }}
          loading={loading}
          className={styles.anTdTable}
          search={false}
          filters={true}
          options={{
            fullScreen: false,
            reload: false,
            setting: true,
            density: false,
          }}
          toolBarRender={() => [
            <div className={styles.project}>
              <div>
                <Button
                  className={classNames(
                    type === 'manage' ? styles.active : null,
                    styles.manage,
                  )}
                  type={type === 'manage' ? 'primary' : null}
                  onClick={handleOnChangeType.bind(this, 'manage')}
                  style={{ width: 88 }}
                >
                  经营类
                </Button>
                <Button
                  className={classNames(
                    type === 'no_manage' ? styles.active : null,
                    styles.no_manage,
                  )}
                  type={type === 'no_manage' ? 'primary' : null}
                  onClick={handleOnChangeType.bind(this, 'no_manage')}
                >
                  非经营类
                </Button>
                <Button
                  className={isFollow === '0' ? styles.active : null}
                  type={isFollow === '0' ? 'primary' : null}
                  onClick={handleInterestChange}
                >
                  我关注的项目
                </Button>
              </div>
            </div>,
            <div className={styles.focus}>
              <Button
                className={isFollow === '0' ? styles.active : null}
                type="primary"
                icon={<DownloadOutlined />}
                onClick={downloadCustomAll}
              >
                导出
              </Button>
              <Button
                className={isFollow === '0' ? styles.active : null}
                type="primary"
                icon={<DownloadOutlined />}
                onClick={downloadCustom}
              >
                自定义导出
              </Button>
              {isFollow && (
                <Button className={styles.cancelFocus} onClick={cancelFocus}>
                  取消关注
                </Button>
              )}
              {!isFollow && (
                <Button className={styles.Focus} onClick={selectFocus}>
                  关注
                </Button>
              )}
            </div>,
          ]}
        />
        <div className={styles.splitPigination}>
          <div>
            <Select
              defaultValue="10"
              style={{ width: 150 }}
              className={styles.selects}
              onChange={pageSizeChange}
            >
              <Option value="10">显示结果：10条</Option>
              <Option value="20">显示结果：20条</Option>
              <Option value="50">显示结果：50条</Option>
            </Select>
            <span className={styles.total}>共{dataTotal}条</span>
          </div>
          <Pagination
            total={dataTotal || 0}
            pageSize={parmas.limit}
            showSizeChanger={false}
            current={page}
            key={67}
            onChange={onChangePageNumber}
          />
        </div>
      </div>
      <Modal
        width="700px"
        title="自定义导出"
        visible={visibleModal}
        onOk={handleOk}
        maskClosable={false}
        onCancel={handleCancel}
      >
        <div className={styles.ModalBox}>
          <>
            <Checkbox
              indeterminate={indeterminate}
              onChange={onCheckAllChange}
              checked={checkAll}
            >
              全选
            </Checkbox>
            <Divider />
            <CheckboxGroup
              options={plainOptions}
              value={checkedLists}
              onChange={onChangeCheckbox}
            />
          </>
        </div>
      </Modal>

      <Modal
        width="600px"
        title="项目变更"
        visible={visibleModalUpdate}
        onOk={handleOkUpdate}
        maskClosable={false}
        onCancel={handleCancelUpdate}
        footer={null}
      >
        <div className={styles.ModalBox}>
          <Form
            name="basic"
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
            onFinish={onFinish}
            autoComplete="off"
          >
            <Form.Item
              label="变更原因"
              name="username"
              rules={[
                {
                  required: true,
                  message: '请输入变更原因：如变更起因，由谁提出等',
                },
              ]}
            >
              <TextArea autoSize />
            </Form.Item>

            <Form.Item
              label="变更内容"
              name="password"
              rules={[
                {
                  required: true,
                  message: '请输入变更变更内容：如需求，设计，代码等',
                },
              ]}
            >
              <TextArea autoSize />
            </Form.Item>

            <Form.Item
              label="变更影响"
              name="password"
              rules={[
                {
                  required: true,
                  message: '请输入变更影响：如进度，收益，质量等',
                },
              ]}
            >
              <TextArea autoSize />
            </Form.Item>

            <div className={styles.formButtons}>
              <Button key="back" onClick={handleCancelUpdate}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </div>
          </Form>
        </div>
      </Modal>
    </div>
  );
};
