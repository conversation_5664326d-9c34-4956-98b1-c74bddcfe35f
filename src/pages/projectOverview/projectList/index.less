.card {
  width: 100%;
  height: auto;
  background: #ffffff;
  border: 1px solid #edeff2;
  box-sizing: border-box;
  padding: 13px 24px;
  box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
  border-radius: 8px;
  margin-bottom: 24px;
  min-width: 857px;

  .project {
    position: absolute;
    left: 0;
    top: 12px;
    display: flex;
    justify-content: space-between;

    button {
      border-radius: 0;
      transition: all 0s ease;
      position: relative;
      top: 0;
      vertical-align: middle;
    }

    .manage {
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
    }

    .cancelFocus,
    .Focus {
      margin-left: 5px;
    }

    .no_manage {
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }

    button:nth-child(3) {
      font-family: OPPOSans;
      font-style: normal;
      font-size: 14px;
      border: 1px solid #edeff2;
      box-sizing: border-box;
      border-radius: 6px;
      margin-left: 5px;
    }

    .active {
      background: #3d7bf8;
      color: #ffffff;
    }

    .focus {
      button {
        font-family: OPPOSans;
        font-style: normal;
        font-size: 14px;
        border: 1px solid #edeff2;
        box-sizing: border-box;
        border-radius: 6px;
      }
    }
  }

  p {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    /* identical to box height, or 157% */
    /* Netural Color/Grey 600 */
    color: #525252;
  }

  input,
  button {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
  }
}

.rangePicker {
  border: 1px solid #edeff2;
  box-sizing: border-box;
  border-radius: 6px;

  input {
    border: 0;
  }
}

.splitPigination {
  display: flex;
  justify-content: space-between;
  padding: 0 12px 0 12px;
  margin-top: 10px;

  .pageSelect {
    box-sizing: border-box;
    border-radius: 6px;
    border: 0px solid #edeff2;

    > .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 6px;
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
      border-top-left-radius: 6px;
      border-top-left-radius: 6px;
    }

    > .ant-select-selector {
      border-radius: 6px;
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
      border-top-left-radius: 6px;
      border-top-left-radius: 6px;
    }
  }

  .total {
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    margin-left: 8px;
    color: #7a7a7a;
  }

  input,
  button,
  li,
  li > button,
  .ant-pagination-item-link {
    box-sizing: border-box;
    border-radius: 6px;
  }
}

.searchInput {
  display: flex;
  justify-content: space-between;
  position: relative;
  flex-wrap: wrap;

  .searchItema {
    width: 15%;
  }

  .searchItemb {
    width: 30%;
  }
  .esarchItemc {
    width: 10%;
  }
}

.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;

  > div {
    display: flex;

    > div {
      width: 144px;
      height: 32px;
      left: 0px;
      top: 0px;
      background: #ffffff;
      border: 1px solid #edeff2;
      box-sizing: border-box;
      border-radius: 6px;
    }
  }

  p {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    margin: 6px 0 0 10px;
    /* identical to box height, or 167% */

    /* Netural Color/Grey 500 */

    color: #7a7a7a;
  }

  // width: 100%;
  // float: right;
  // >li:last-child {
  //   float: left;
  //   margin-right: 10px;
  //   margin-left: 0;
  // }
  > li {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
  }
}

.anTdTable {
  margin-top: -10px;
}

.anTdTable > .ant-card > .ant-card-body {
  padding: 0 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.anTdTable tr > th,
.anTdTable tr > td {
  border-bottom: 0px;
  padding-left: 5px;
}

.anTdTable tr > th {
  font-size: 12px;
  line-height: 20px;
  color: #7a7a7a;

  > div > div {
    padding: 10px 16px;
  }
}

.checkboxs {
  > label {
    margin-left: 0px !important;
  }
}

.is_follow {
  color: #3d7bf8;
  // color: #73d13d
}

.break {
  word-break: break-all;
  word-wrap: break-word;
  overflow: hidden;
  max-height: 22px;
}

input,
button,
li,
textarea,
.selects,
.ant-input-affix-wrapper,
.selects > div {
  border-color: #edeff2 !important;
  box-sizing: border-box;
  border-radius: 6px !important;
}

.searchGruopSelect,
.searchGruopSelects {
  border-color: #edeff2 !important;
  box-sizing: border-box;
  border-radius: 6px !important;
  :global {
    .ant-select-selector {
      border-color: #edeff2 !important;
      box-sizing: border-box;
      border-radius: 6px !important;
    }
  }
}

.openModal {
  display: block;
  position: absolute;
  right: -20px;
  bottom: 0;
  font-size: 12px;
  color: #3d7bf8;
  line-height: 28px;
  svg {
    transform: translateY(4px);
  }
}
.openModal:hover {
  cursor: pointer;
}
.searchBox {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding-top: 5px;
  padding-bottom: 5px;
  margin-top: 10px;
  .searchBoxName {
    width: 70px;
    line-height: 30px;
    font-weight: 500;
  }
  .searchBoxContent {
    width: calc(~'100% - 60px');
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    .searchBoxGroup {
      width: 47%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-right: 20px;
      margin-bottom: 10px;
      position: relative;
      input:nth-child(1) {
        width: 350px;
        margin-right: 20px;
      }
    }
  }
}
.ContentTimeLeft {
  width: 47%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.addSlelect {
  display: block;
  margin-left: 5px;
  transform: translateY(2px);
  position: absolute;
  right: -28px;
  bottom: 2px;
}
.addSlelect:hover {
  cursor: pointer;
}
.copedItemRight {
  width: 365px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  :global {
    .ant-input-number {
      // overflow: hidden;
      // border:none;
      .ant-input-number-input-wrap {
        input {
          max-width: 120px;
          border: none;
        }
      }
    }
  }
}
.textSpan {
  display: block;
  padding-left: 5px;
  padding-right: 5px;
}
.searchBoxContentTime {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.ContentTimeRight {
  button:nth-child(1) {
    margin-right: 15px;
  }
  button:nth-child(2) {
    margin-right: 40px;
  }
}

//自定义导出模态框样式
.ModalBox {
  width: 100%;
  height: auto;
  :global {
    .ant-divider-horizontal {
      margin: 14px 0;
    }
    .ant-checkbox-group {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      .ant-checkbox-wrapper {
        width: 31%;
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        margin-bottom: 20px;
      }
    }
  }
}
.spanStyle {
  color: #1890ff;
  margin-left: 8px;
}
.spanStyle:hover {
  cursor: pointer;
}
.formButtons {
  border-radius: 0 0 2px 2px;
  padding: 10px 16px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  button {
    margin-left: 6px;
  }
}

.colorDiv {
  width: 100%;
  display: flex;
  justify-content: center;
  div {
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
}
