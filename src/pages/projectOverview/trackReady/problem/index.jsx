import React, { useState, useEffect } from 'react';
import styles from './index.less';
import classNames from 'classnames';
import {
  Button,
  Modal,
  Form,
  Input,
  message,
  Spin,
  Popconfirm,
  DatePicker,
  Radio,
  Tag,
} from 'antd';
import refresh_icon from '@/assets/refresh_icon.svg';
import greyDot_icon from '@/assets/greyDot_icon.svg';
import edit_icon from '@/assets/edit_icon.svg';
import delete_icon from '@/assets/delete_icon.svg';
import wrench_icon from '@/assets/wrench_icon.svg';

import error_icon from '@/assets/error_icon.svg';
import { CheckOutlined, ClockCircleOutlined } from '@ant-design/icons';

import {
  getProjectAscTopic,
  AddProjectRisks,
  updateItemRisk,
  AddProjectAscTopic,
  deleteProjectPromble,
  InProjectRisks,
  deleteProjectCurrent,
  UpdateProjectCurrent,
} from './service';
import moment from 'moment';
const Problem = props => {
  const [problemForm] = Form.useForm();

  const { itemNumber, itemId, over, memberType } = props;
  //近期工作列表
  const [current, setCurrent] = useState([]);
  //问题列表
  const [risk, setRisk] = useState([]);
  const [isResolved, setIsResolved] = useState([]);
  const [noResolved, setNoResolved] = useState([]);
  const [initData, setInitData] = useState([]);

  //近期工作新增编辑弹窗
  const [visibleRecent, setVisibleRecent] = useState(false);
  const [visibleProblem, setVisibleProblem] = useState(false);
  const [visibleHandleProblem, setVisibleHandleProblem] = useState(false);

  //是否必填
  const [formReuqire, setFormReuqire] = useState(true);

  const [recentFromData, setRecentFromData] = useState({});
  const [problemFromData, setProblemFromData] = useState({});
  const [handleProblemData, setHandleProblemData] = useState({});

  const [ID, setID] = useState();

  const [edit, setEdit] = useState(false);

  const [showMore, setShowMore] = useState(false);
  const [problemDate, setProblemDate] = useState();

  //弹窗标题
  const [modalTitle, setModalTitle] = useState('');

  //展示近期工作更多数据
  const [currentShowData, setCurrentShowData] = useState([]);

  //是否解决
  const [radioValue, setRadioValue] = useState(false);

  //问题切换选项
  const problemType = [
    { key: 0, lable: '全部', value: 'all' },
    { key: 1, lable: '待解决', value: 'all' },
    { key: 2, lable: '已解决', value: 'all' },
  ];

  //切换问题选项
  const [checkedTabs, setCheckedTabs] = useState(1);

  //初始化获取信息
  const getAscTopic = async value => {
    const { code, data } = await getProjectAscTopic(value);
    if (code === 200) {
      setCurrent(data[0].Current);
      if (data[0].Current.length > 0) {
        setCurrentShowData([data[0].Current[0]]);
      }
      const isRisk = [];
      const noRisk = [];
      data[1].Risk.forEach(item => {
        if (item.is_resolved === 'Y') {
          isRisk.push(item);
        } else {
          noRisk.push(item);
        }
      });
      setIsResolved(isRisk.reverse());
      setNoResolved(noRisk.reverse());
      setRisk(noRisk.reverse());
      setShowMore(false);
    }
  };

  //初始化获取信息
  useEffect(() => {
    getAscTopic(itemNumber);
  }, [itemNumber]);

  //切换tabs
  const changeTabs = key => {
    setCheckedTabs(key);
    if (key === 0) {
      setRisk([...noResolved, ...isResolved]);
    }
    if (key === 1) {
      setRisk(noResolved);
    }
    if (key === 2) {
      setRisk(isResolved);
    }
  };

  const handleOk = () => {
    setVisibleRecent(false);
    setVisibleProblem(false);
  };

  const handleCancel = () => {
    setVisibleRecent(false);
    setVisibleProblem(false);
  };
  //打开新增近期工作弹窗
  const showRecentMoodal = ({ type, item }) => {
    if (type === 'add') {
      setEdit(false);
      setModalTitle('新增近期工作');
    } else {
      setModalTitle('编辑近期工作');
      setEdit(true);
      setRecentFromData(item);
    }
    setVisibleRecent(true);
  };

  const [itemInfo, setItmeInfo] = useState({});
  //打开新增问题弹窗
  const showProblemMoodal = params => {
    if (params) {
      const { title, item } = params;
      setItmeInfo(item);
      setModalTitle(title);
      problemForm.setFieldsValue({
        occurred_time: moment(item.occurred_time),
        risk: item.risk,
        solutions: item.solutions,
      });
      setVisibleProblem(true);
    } else {
      setModalTitle('新增项目问题');
      problemForm.setFieldsValue({
        occurred_time: '',
        risk: '',
        solutions: '',
      });
      setProblemFromData({});
      setProblemDate();
      setVisibleProblem(true);
    }
  };
  //近期工作表单提交
  const onFinishRecentFromData = values => {
    if (edit) {
      UpdateProjectCurrents({
        ...recentFromData,
        ...values,
        item_num: itemNumber,
      });
    } else {
      AddProjectAscTopics({ ...values, item_num: itemNumber });
    }
  };
  //项目问题风险提交
  const onFinishProblemFromData = values => {
    if (modalTitle === '编辑项目问题') {
      console.log(values, 'valuesvalues');
      console.log(itemInfo, 'itemInfo');
      EditProjectRisk({
        ...values,
        itemNum: itemNumber,
        itemId: itemId,
        id: itemInfo.id,
        occurredTime:
          problemDate || moment(values.occurred_time).format('YYYY-MM-DD'),
        item_num: itemNumber,
      });
    } else {
      AddProjectRisk({
        ...values,
        occurred_time: problemDate,
        item_num: itemNumber,
      });
    }
  };
  const onFinishHandleProblemData = value => {
    InProjectRisk({ ...value, resolved_time: problemDate, id: ID });
  };
  //新增项目问题
  const AddProjectRisk = async value => {
    const { code, data } = await AddProjectRisks(value);
    if (code === 200) {
      message.success({
        content: '新增成功!',
        key: 'AddProjectRisk',
        duration: 2,
      });
      setVisibleProblem(false);
      getAscTopic(itemNumber);
    } else {
      message.error({
        content: '新增失败!',
        key: 'AddProjectRisk',
        duration: 2,
      });
    }
  };
  //编辑项目问题
  const EditProjectRisk = async value => {
    const { code, data } = await updateItemRisk(value);
    if (code === 200) {
      message.success({
        content: '编辑成功!',
        key: 'AddProjectRisk',
        duration: 2,
      });
      setVisibleProblem(false);
      getAscTopic(itemNumber);
    } else {
      message.error({
        content: '编辑失败!',
        key: 'AddProjectRisk',
        duration: 2,
      });
    }
  };
  //新增近期工作重点、计划
  const AddProjectAscTopics = async value => {
    const { code, data } = await AddProjectAscTopic(value);
    if (code === 200) {
      message.success({
        content: '新增成功!',
        key: 'AddProjectAscTopic',
        duration: 2,
      });
      setVisibleRecent(false);
      getAscTopic(itemNumber);
    }
  };
  //编辑近期工作
  const UpdateProjectCurrents = async value => {
    const { code, data } = await UpdateProjectCurrent(value);
    if (code === 200) {
      message.success({
        content: '编辑成功!',
        key: 'AddProjectAscTopic',
        duration: 2,
      });
      setVisibleRecent(false);
      getAscTopic(itemNumber);
    }
  };

  //处理问题
  const InProjectRisk = async value => {
    const { code, data } = await InProjectRisks(value);
    if (code === 200) {
      message.success({
        content: '处理成功!',
        key: 'InProjectRisk',
        duration: 2,
      });
      setVisibleHandleProblem(false);
      getAscTopic(itemNumber);
    }
  };
  //展示更多 点击
  const showMoreClick = value => {
    if (value) {
      setCurrentShowData(current);
    } else {
      setCurrentShowData([current[0]]);
    }
    setShowMore(value);
  };

  //删除项目问题点击
  const deleteProblemClick = id => {
    message.loading({ content: '删除项目问题中', key: 'deleteProblemClick' });
    deleteProjectPrombles(id);
  };
  //删除项目问题
  const deleteProjectPrombles = async value => {
    const { code } = await deleteProjectPromble(value);
    if (code === 200) {
      message.success({
        content: '删除成功!',
        key: 'deleteProblemClick',
        duration: 2,
      });
      getAscTopic(itemNumber);
    }
  };
  //删除近期工作点击
  const deleteCurrentClick = id => {
    message.loading({ content: '删除项目工作中', key: 'deleteProjectCurrent' });
    deleteProjectCurrents({ id: id, item_num: itemNumber });
  };

  //删除近期工作
  const deleteProjectCurrents = async value => {
    const { code } = await deleteProjectCurrent(value);
    if (code === 200) {
      message.success({
        content: '删除成功!',
        key: 'deleteProjectCurrent',
        duration: 2,
      });
      getAscTopic(itemNumber);
    }
  };

  // 日期选择框发生变化
  const onDateChange = (date, dateString) => {
    setProblemDate(dateString);
  };

  const changeRadio = e => {
    if (e.target.value === 'N') {
      setRadioValue(false);
    } else {
      setRadioValue(true);
    }
  };
  return (
    <div className={classNames(styles.problem, 'trackContent')}>
      <div className={styles.top}>
        <div className={styles.title}>
          <div>
            <p>近期工作</p>
          </div>
          <div>
            {over !== 'over' && memberType !== '1' && (
              <Button
                type="primary"
                onClick={() => showRecentMoodal({ type: 'add' })}
              >
                新增
              </Button>
            )}
          </div>
        </div>
        {currentShowData.length > 0 ? (
          currentShowData.map((item, index) => (
            <div className={styles.workBox} key={index}>
              <div className={styles.boxTitle}>
                <div>{item.create_time}</div>
                {index === 0 && (
                  <>
                    {over !== 'over' && memberType !== '1' && (
                      <div>
                        <span
                          className={styles.editBottun}
                          onClick={() =>
                            showRecentMoodal({ type: 'edit', item: item })
                          }
                        >
                          <img src={edit_icon} alt="" /> 编辑
                        </span>
                        <Popconfirm
                          title="是否删除？"
                          okText="是"
                          cancelText="否"
                          onConfirm={() => deleteCurrentClick(item.id)}
                        >
                          <span className={styles.deleteBottun}>
                            <img src={delete_icon} alt="" /> 删除
                          </span>
                        </Popconfirm>
                      </div>
                    )}
                  </>
                )}
              </div>
              <div className={styles.boxContent}>
                <div>
                  <div>工作重点</div>
                  <div className={styles.boxContentOver}>
                    {item.current_week_content}
                  </div>
                </div>
                <div>
                  <div>工作计划 </div>
                  <div className={styles.boxContentOver}>
                    {item.next_week_content}
                  </div>
                </div>
                <div>
                  <div>创建人员 </div>
                  <div className={styles.boxContentOver}>{item.username}</div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <p style={{ textAlign: 'center', width: '100%' }}>暂无数据</p>
        )}
        {showMore
          ? current.length > 1 && (
              <div
                className={styles.showMore}
                onClick={() => showMoreClick(false)}
              >
                收起
              </div>
            )
          : current.length > 1 && (
              <div
                className={styles.showMore}
                onClick={() => showMoreClick(true)}
              >
                查看更多
              </div>
            )}
      </div>
      <div>
        <div className={styles.title}>
          <div>
            <p className={styles.problemList}>
              项目问题
              {problemType.map(item => (
                <span
                  key={item.key}
                  className={checkedTabs === item.key ? styles.checked : ''}
                  onClick={() => changeTabs(item.key)}
                >
                  {item.lable}·
                  {item.key === 0
                    ? noResolved.length + isResolved.length
                    : item.key === 1
                    ? noResolved.length
                    : item.key === 2
                    ? isResolved.length
                    : 0}
                </span>
              ))}
            </p>
          </div>
          <div>
            {over !== 'over' && memberType !== '1' && (
              <Button type="primary" onClick={() => showProblemMoodal()}>
                新增
              </Button>
            )}
          </div>
        </div>
        <div className={styles.problemBox}>
          {risk.length > 0 ? (
            risk.map((item, index) => (
              <div key={index}>
                <div>
                  <div className={styles.boxTitle}>
                    <div>
                      {item.occurred_time}
                      {item.is_resolved === 'N' ? (
                        <span className={styles.problemError}>
                          <img src={error_icon} alt="" />
                          未解决
                        </span>
                      ) : (
                        <span className={styles.problemOver}>
                          <CheckOutlined />
                          已解决
                        </span>
                      )}
                    </div>
                    <div>
                      {item.is_resolved === 'N' &&
                        over !== 'over' &&
                        memberType !== '1' && (
                          <>
                            <span
                              className={styles.editBottun}
                              onClick={() => {
                                showProblemMoodal({
                                  title: '编辑项目问题',
                                  item,
                                });
                              }}
                            >
                              <img
                                src={edit_icon}
                                alt=""
                                style={{ cursor: 'pointer' }}
                              />{' '}
                              编辑
                            </span>
                            <span
                              className={styles.editBottun}
                              onClick={() => {
                                setVisibleHandleProblem(true);
                                setID(item.id);
                                setModalTitle('处理项目问题');
                              }}
                            >
                              <img src={wrench_icon} alt="" /> 处理
                            </span>
                            <Popconfirm
                              title="是否删除？"
                              okText="是"
                              cancelText="否"
                              onConfirm={() => deleteProblemClick(item.id)}
                            >
                              <span className={styles.deleteBottun}>
                                <img src={delete_icon} alt="" /> 删除
                              </span>
                            </Popconfirm>
                          </>
                        )}
                    </div>
                  </div>
                  <div className={styles.boxContent}>
                    <div>
                      <div>问题及风险</div>
                      <div className={styles.boxContentOver}>{item.risk}</div>
                    </div>
                    <div>
                      <div>应对措施</div>
                      <div className={styles.boxContentOver}>
                        {item.solutions}
                      </div>
                    </div>
                    <div>
                      <div>创建人员</div>
                      <div className={styles.boxContentOver}>
                        {item.create_username}
                      </div>
                    </div>
                    <div>
                      <div>处理人员</div>
                      <div className={styles.boxContentOver}>
                        {item.hand_username}
                      </div>
                    </div>
                  </div>
                </div>
                <div></div>
              </div>
            ))
          ) : (
            <p style={{ textAlign: 'center', width: '100%' }}>暂无数据</p>
          )}
        </div>
      </div>

      <Modal
        title={modalTitle}
        visible={visibleRecent}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
      >
        <Form
          initialValues={recentFromData}
          layout="vertical"
          name="nest-messages"
          onFinish={onFinishRecentFromData}
        >
          <Form.Item
            label="工作重点"
            name="current_week_content"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input.TextArea />
          </Form.Item>
          <Form.Item
            label="工作计划"
            name="next_week_content"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input.TextArea />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginBottom: 0 }}
            >
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={modalTitle}
        visible={visibleProblem}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
      >
        <Form
          initialValues={problemFromData}
          layout="vertical"
          name="nest-messages"
          form={problemForm}
          onFinish={onFinishProblemFromData}
        >
          <Form.Item
            label="发生时间"
            name="occurred_time"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <DatePicker
              className={styles.selects}
              suffixIcon={<ClockCircleOutlined />}
              onChange={onDateChange}
            />
          </Form.Item>
          <Form.Item
            label="风险/问题描述"
            name="risk"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input.TextArea />
          </Form.Item>
          <Form.Item
            label="应对措施"
            name="solutions"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input.TextArea />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={modalTitle}
        visible={visibleHandleProblem}
        onOk={handleOk}
        onCancel={() => {
          setVisibleHandleProblem(false);
        }}
        footer={null}
      >
        <Form
          initialValues={handleProblemData}
          layout="vertical"
          name="nest-messages"
          onFinish={onFinishHandleProblemData}
        >
          <Form.Item
            label="是否解决"
            name="is_resolved"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Radio.Group onChange={changeRadio}>
              <Radio value={'Y'}>已解决</Radio>
              <Radio value={'N'}>未解决</Radio>
            </Radio.Group>
          </Form.Item>
          {radioValue && (
            <Form.Item
              label="解决时间"
              name="resolved_time"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <DatePicker onChange={onDateChange} />
            </Form.Item>
          )}
          <Form.Item
            label={radioValue ? '解决情况' : '跟踪情况'}
            name="resolved_condition"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input.TextArea />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Problem;
