import React, { useState, useEffect } from 'react';
import moment from 'moment';
import 已完成 from '@/assets/已完成.svg';
import styles from './index.less';
import {
  Button,
  Modal,
  Anchor,
  Table,
  Collapse,
  Checkbox,
  Form,
  Tooltip,
  Input,
  Select,
  Radio,
  Row,
  Col,
  DatePicker,
  message,
  Upload,
  Progress,
  Popconfirm,
  Steps,
} from 'antd';
import {
  InfoCircleOutlined,
  QuestionCircleOutlined,
  UserOutlined,
  CloseOutlined,
  ClockCircleOutlined,
  PlusOutlined,
  UpCircleOutlined,
  DownCircleOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { getItemEndTitle, getItemEnd } from './service';
import { history } from 'umi';
import reqwest from 'reqwest';
import classNames from 'classnames';
const { RangePicker } = DatePicker;
const { TextArea, Search } = Input;
const { Link } = Anchor;
const { Step } = Steps;
const { Panel } = Collapse;
const { confirm } = Modal;
const { Option } = Select;
const dateFormat = 'YYYY-MM-DD';
export default props => {
  const { id } = props;
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [modalTitle, setModalTitle] = useState('');
  const [modalFrom] = Form.useForm();

  const [uploadProps, setUploadProps] = useState({
    onRemove: file => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: file => {
      setFileList([...fileList, file]);
      return false;
    },
    fileList,
  });

  useEffect(() => {
    setUploadProps({
      onRemove: file => {
        const index = fileList.indexOf(file);
        const newFileList = fileList.slice();
        newFileList.splice(index, 1);
        setFileList(newFileList);
      },
      beforeUpload: file => {
        setFileList([...fileList, file]);
        return false;
      },
      fileList,
    });
  }, [fileList]);

  const [contractList, setContractList] = useState([
    {
      itemNum: '', //项目编号
      type: '', //合同类型：0收入合同、1成本合同
      conNum: '', //合同编号
      conName: '', //合同名称
      groupNum: '', //呈批件编号
      groupName: '', //呈批件名称
      startTime: '', //合同开始时间
      endTime: '', //合同结束时间
      conState: '', //执行情况：0执行完成、1未执行完成
      remarks: '', //合同备注
      fileList: [],
    },
  ]);

  const oucomeColumns = [
    {
      title: '费用类别',
      dataIndex: 'cost_type',
      // filterIcon: filtered => handleGetIcon(filtered),
      // filterDropdown: radioGrouop(TypeData),
    },
    {
      title: '实际成本',
      dataIndex: 'plan_cost',
      render: item => item && <span>{item}W</span>,
    },
    {
      title: '项目预算',
      dataIndex: 'actual_cost',
      render: item => item && <span>{item}W</span>,
    },
    {
      title: '成本占比',
      dataIndex: 'zb',
      render: item =>
        item && (
          <Tooltip
            placement="topLeft"
            title={`${(item !== 0 && Number(item).toFixed(2)) || 0}%`}
          >
            <Progress
              percent={item}
              status="normal"
              format={() => `${(item !== 0 && Number(item).toFixed(2)) || 0}%`}
              style={{ width: '70%' }}
            />
          </Tooltip>
        ),
    },
  ];
  const [oucomeInfo, setOucomeInfo] = useState([]);
  const [titleInfo, setTitleInfo] = useState({});
  const [itemTrials, setItemTrials] = useState([]);
  const [data, setData] = useState(data);
  useEffect(() => {
    getItemEndInfo({
      item_id: id,
    });
  }, []);

  const getItemEndInfo = async value => {
    const { data, code } = await getItemEnd(value);
    if (code === 200 && data) {
      if (data.itemNum) {
        getTitleInfo(data.itemNum);
      }
      setData(data);
      setItemTrials(data.itemTrials);
      const list = [];
      const itemContrat = {};
      if (data.itemContrat) {
        data.itemContrat.forEach(item => {
          list.push({
            ...item,
            planTimes: [moment(data.startTime), moment(data.endTime)],
          });
        });
        setContractList(list);
        data.itemContrat.forEach((item, index) => {
          const keysList = Object.keys(item);
          keysList.forEach(key => {
            itemContrat[`${key}${index}`] = item[key];
          });
          itemContrat[`planTimes${index}`] = [
            moment(item.startTime),
            moment(item.endTime),
          ];
        });
      }
      form.setFieldsValue({ ...data, ...itemContrat });
    }
  };

  const getTitleInfo = async value => {
    const { data, code } = await getItemEndTitle(value);
    if (code === 200) {
      setTitleInfo(data);
      setOucomeInfo(data.costInfo);
    }
  };

  const contractListChange = porps => {
    const { key, value, index } = porps;
    const list = contractList;
    list[index][key] = value;
    setContractList([...list]);
  };

  const onFinishFormData = value => {};

  return (
    <div>
      <Form
        className={styles.forms}
        // initialValues={fromData}
        colon={false}
        // layout="vertical"
        form={form}
        labelAlign="right"
        name="nest-messages"
        scrollToFirstError={true}
        onFinish={onFinishFormData}
        labelCol={{ span: 7 }}
        wrapperCol={{ span: 13 }}
      >
        <Collapse
          defaultActiveKey={['1']}
          expandIconPosition="right"
          ghost={true}
        >
          <Panel
            header={<div className={styles.titles}>项目执行概况</div>}
            key="1"
            id="22"
            className={classNames(styles.card)}
          >
            <Form.Item label="申请结项类型" name="type">
              <Radio.Group disabled className={styles.grasp_Box}>
                <Radio value={0}>项目完成</Radio>
                <Radio value={1}>项目终止</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="完成概述" name="overview">
              <Input disabled placeholder="无数据" />
            </Form.Item>
            <Form.Item label="实施过程评价" name="implEvaluate">
              <Input disabled placeholder="无数据" />
            </Form.Item>
            <Form.Item label="客户满意度及意见" name="userOpinion">
              <Input disabled placeholder="无数据" />
            </Form.Item>
            <div className={styles.uplod_list}>
              <div>
                {data && data.itemFile.length > 0 ? (
                  data.itemFile.map(item => (
                    <div>
                      {' '}
                      <a href={fileItem.url}>{item.name}</a>
                    </div>
                  ))
                ) : (
                  <div>无附件</div>
                )}
              </div>
            </div>
            <Form.Item label="质量进度成本控制情况" name="situation">
              <Input disabled placeholder="无数据" />
            </Form.Item>
            <Form.Item label="滞留问题及影响" name="influence">
              <Input disabled placeholder="无数据" />
            </Form.Item>
            <Form.Item label="有待提高的方面" name="hoist">
              <Input disabled placeholder="无数据" />
            </Form.Item>
            <Form.Item label="经验与教训" name="experience">
              <Input disabled placeholder="无数据" />
            </Form.Item>
          </Panel>
        </Collapse>

        <Collapse
          defaultActiveKey={['1']}
          expandIconPosition="right"
          ghost={true}
        >
          <Panel
            header={<div className={styles.titles}>项目合同执行情况</div>}
            key="1"
            id="22"
            className={classNames(styles.card)}
          >
            <div className={styles.scroll_x}>
              <div
                className={styles.scroll_x_boxs}
                style={{
                  width: `${
                    contractList.length > 1 ? contractList.length * 60 : 100
                  }%`,
                }}
              >
                {contractList.length > 0 &&
                  contractList.map((item, index) => {
                    return (
                      <div
                        key={index}
                        style={{
                          width: `${
                            contractList.length > 1
                              ? contractList.length * 50 * 0.6
                              : 100
                          }%`,
                          border: contractList.length === 1 && 0,
                        }}
                        className={
                          contractList.length > 1
                            ? styles.margin_right
                            : styles.margin_auto
                        }
                      >
                        <Form.Item label="合同类型" name={`type` + index}>
                          <Radio.Group className={styles.grasp_Box} disabled>
                            <Radio value={0}>收入合同</Radio>
                            <Radio value={1}>成本合同</Radio>
                          </Radio.Group>
                        </Form.Item>
                        <Form.Item label="合同编号" name={`conNum` + index}>
                          <Input placeholder="无数据" disabled />
                        </Form.Item>
                        <Form.Item label="呈批件编号" name={`groupNum` + index}>
                          <Input
                            disabled
                            onChange={e =>
                              contractListChange({
                                value: e.target.value,
                                index,
                                key: 'groupNum',
                              })
                            }
                            placeholder="无数据"
                          />
                        </Form.Item>
                        <Form.Item
                          label="呈批件名称"
                          name={`groupName` + index}
                        >
                          <Input
                            disabled
                            onChange={e =>
                              contractListChange({
                                value: e.target.value,
                                index,
                                key: 'groupName',
                              })
                            }
                            placeholder="无数据"
                          />
                        </Form.Item>
                        <Form.Item label="合同名称" name={`conName` + index}>
                          <Input
                            disabled
                            onChange={e =>
                              contractListChange({
                                value: e.target.value,
                                index,
                                key: 'conName',
                              })
                            }
                            placeholder="无数据"
                          />
                        </Form.Item>
                        <Form.Item
                          label="合同有效期"
                          name={`planTimes` + index}
                        >
                          <RangePicker
                            className={styles.selects}
                            suffixIcon={<ClockCircleOutlined />}
                            disabled
                            format="YYYY/MM/DD"
                            onChange={value =>
                              contractListChange({
                                value,
                                index,
                                key: 'planTimes',
                              })
                            }
                          />
                        </Form.Item>
                        <Form.Item
                          label="合同执行情况"
                          name={`conState` + index}
                        >
                          <Radio.Group
                            className={styles.grasp_Box}
                            disabled
                            onChange={e =>
                              contractListChange({
                                value: e.target.value,
                                index,
                                key: 'conState',
                              })
                            }
                          >
                            <Radio value={'0'}>执行完成</Radio>
                            <Radio value={'1'}>未执行完成</Radio>
                          </Radio.Group>
                        </Form.Item>
                        <Form.Item label="合同备注" name={`remarks` + index}>
                          <Input
                            placeholder="无数据"
                            disabled
                            onChange={value =>
                              contractListChange({
                                value,
                                index,
                                key: 'remarks',
                              })
                            }
                          />
                        </Form.Item>
                        <Form.Item label="合同附件">
                          {contractList[index].itemFile &&
                          item.itemFile.length > 0 ? (
                            item.itemFile.map(fileItem => (
                              <div>
                                <a
                                  href={fileItem.url}
                                  download={`${fileItem.name}`}
                                  target="_blank"
                                >
                                  {fileItem.name}
                                </a>
                              </div>
                            ))
                          ) : (
                            <div>无</div>
                          )}
                        </Form.Item>
                      </div>
                    );
                  })}
              </div>
            </div>
          </Panel>
        </Collapse>
        <div className={styles.buttom_box}>
          <div>
            <div className={styles.titles}>项目回款执行情况</div>
            <div className={styles.progress_box}>
              <div>
                <div>项目合同总收入</div>
                <span>{titleInfo.refund_number}元</span>
                <div>实际回款金额</div>
                <span>{titleInfo.refund_number}元</span>
              </div>
              <div>
                <Progress
                  type="circle"
                  percent={titleInfo.incomeZb}
                  width={80}
                  status="normal"
                  format={value => `${value}%`}
                />
                <p className={styles.progressName}>项目整体</p>
              </div>
            </div>
          </div>
          <div>
            <div className={styles.titles}>项目成本情况</div>
            <Table
              columns={oucomeColumns}
              dataSource={oucomeInfo}
              pagination={false}
              className={styles.anTdTable}
            />
          </div>
        </div>
      </Form>
      <div className={styles.card} style={{ marginBottom: 100 }}>
        <div className={styles.titles}>项目结项审核意见及评定建议</div>
        <div className={styles.proposal_box}>
          {itemTrials.length > 0 ? (
            itemTrials.map(item => (
              <div>
                <div className={styles.dept_title}>
                  <div>{item.trialName}</div>
                  <div>
                    <img src={已完成} alt="" style={{ width: 20 }} />
                  </div>
                </div>
                <div className={styles.content}>{item.trace}</div>
                <div>
                  <div>审核人：{item.username}</div>
                  <div>{item.updateTime}</div>
                </div>
              </div>
            ))
          ) : (
            <p style={{ textAlign: 'center', width: '100%' }}>暂无数据</p>
          )}
        </div>
      </div>
    </div>
  );
};
