import request from '@/utils/request';
import { BASE_URl } from '../../../../utils/constant';

//10.查询团队列表
export function getProjectTeam(params) {
  return request(`${BASE_URl}/projectTrack/getProjectTeam`, {
    method: 'POST',
    data: params,
  });
}

export function AddProjectTeamUser(params) {
  const {
    itemID,
    name,
    dept,
    character,
    dutie,
    nameIdCard,
    deptId,
    startTime,
    endTime,
  } = params;
  return request(
    `${BASE_URl}/projectTrack/AddProjectTeamUser?itemId=${itemID}&name=${name}&nameIdCard=${nameIdCard}&startTime=${startTime}&endTime=${endTime}&dept=${dept}&deptId=${deptId}&character=${character}&dutie=${dutie}`,
    {
      method: 'POST',
      // data: params,
    },
  );
}

export function UpdateProjectTeamUser(params) {
  const { id, name, dept, character, dutie } = params;
  return request(
    `${BASE_URl}/projectTrack/UpdateProjectTeamUser?id=${id}&name=${name}&dept=${dept}&character=${character}&dutie=${dutie}`,
    {
      method: 'POST',
      data: params,
    },
  );
}
//获取部门下拉框数据
export function getDeptListInfo() {
  return request(`${BASE_URl}/dept/SWCARESdeptAll`, {
    method: 'POST',
  });
}

// //获取下拉框数据
// export function getRoleListInfo() {
//   return request(`${BASE_URl}/role/roleAll`, {
//     method: 'POST',
//   });
// }

//获取下拉框数据
export function getNameListInfo(params) {
  return request(`${BASE_URl}/user/selectSysUserPageData`, {
    method: 'POST',
    data: params,
  });
}

export function delProjectTeamUser(id) {
  return request(`${BASE_URl}/projectTrack/delProjectTeamUser?id=${id}`, {
    method: 'POST',
  });
}

export function getFileUrl(parmas) {
  return request(`${BASE_URl}/File/getFileUrl`, {
    method: 'post',
    data: parmas,
  });
}
