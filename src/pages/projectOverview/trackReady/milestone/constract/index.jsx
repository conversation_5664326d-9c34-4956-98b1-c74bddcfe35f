import React, { useEffect, useState } from 'react';
import styles from './index.less';
import echarts from 'echarts';

const Constract = props => {
  useEffect(() => {
    let chartDom = document.getElementById('main');
    let myChart = echarts.init(chartDom);
    let option = {
      tooltip: {
        trigger: 'axis',
        formatter: (params, ticket, callback) => {
          console.log('params', params);
          let formatterHtml = `${params[0].axisValueLabel}:<br/>`;
          params.map((item, index) => {
            let dates = '';
            if (item.value === 1) {
              dates = '合同确立';
            } else if (item.value === 2) {
              dates = '项目实施';
            } else if (item.value === 3) {
              dates = '项目验收';
            } else if (item.value === 4) {
              dates = '结项备案';
            }
            formatterHtml += `
                ${item.marker}${item.seriesName}: ${dates} <br/>
                  `;
          });
          return formatterHtml;
        },
      },
      color: ['#E8684A', '#F6BD16'],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: [
          '2021-05',
          '2021-06',
          '2021-07',
          '2021-08',
          '2021-09',
          '2021-10',
          '2021-11',
        ],
      },
      yAxis: {
        type: 'category',
        axisLabel: {
          formatter: value => {
            console.log('value', value);
            let dates = '';
            if (value === 1) {
              dates = '合同确立';
            } else if (value === 2) {
              dates = '项目实施';
            } else if (value === 3) {
              dates = '项目验收';
            } else if (value === 4) {
              dates = '结项备案';
            }
            return dates;
          },
        },
      },
      series: [
        {
          name: '计划里程碑',
          type: 'line',
          stack: '总量',
          data: [1, 2, 2, 2, 3, 3, 4],
        },
        {
          name: '实际里程碑',
          type: 'line',
          stack: '总量',
          data: [2, 3, 3, 3, 4, 4, 4],
        },
      ],
    };
    myChart.setOption(option);
  }, []);

  return (
    <div className={styles.constract}>
      <div className={styles.constractMain} id="main"></div>
      <div className={styles.constractBottom}>
        <div className={styles.textItem}>
          <div
            className={styles.textItemColor}
            style={{ backgroundColor: 'aquamarine' }}
          ></div>
          <div className={styles.textItemText}>计划里程碑</div>
        </div>
        <div className={styles.textItem}>
          <div className={styles.textItemColor}></div>
          <div className={styles.textItemText}>实际里程碑</div>
        </div>
      </div>
    </div>
  );
};

export default Constract;
