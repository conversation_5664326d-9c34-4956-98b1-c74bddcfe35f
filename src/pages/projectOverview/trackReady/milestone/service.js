import request from '@/utils/request';
import { BASE_URl } from "../../../../utils/constant";

//查询里程碑信息
export function getProjectMilepost(params) {
  return request(
    `${BASE_URl}/projectTrack/getProjectMilepost?item_num=${params}`,
    {
      method: 'POST',
    },
  );
}

//17.修改里程碑完成百分比
export function updateProjPercent(params) {
  const {
    item_num,
    milestone_name_id,
    stage_type_percent,
    end_time,
    project_describe,
  } = params;
  return request(
    `${BASE_URl}/projectTrack/updateProjPercent?item_num=${item_num}&milestone_name_id=${milestone_name_id}&stage_type_percent=${stage_type_percent ||
      0}&end_time=${end_time || ''}&project_describe=${project_describe || ''}`,
    {
      method: 'POST',
    },
  );
}

//17.修改里程碑完成百分比
export function delItemFile(params) {
  const { id, itemNum } = params;
  return request(
    `${BASE_URl}/projectTrack/delItemFile?item_num=${itemNum}&id=${id}`,
    {
      method: 'POST',
    },
  );
}
