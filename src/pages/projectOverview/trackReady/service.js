import request from '@/utils/request';
import { BASE_URl } from "../../../utils/constant";

//查询项目跟踪台头信息
export function getProjectTrackTitle(params) {
  return request(
    `${BASE_URl}/projectTrack/getProjectTrackTitle?item_num=${params}`,
    {
      method: 'POST',
    },
  );
}

//5.查询项目概况信息  GF-Q-015-2020
export function getProjectFacts(params) {
  return request(`${BASE_URl}/projectTrack/getProjectFacts?item_num=${params}`, {
    method: 'POST',
    data: params,
  });
}

// export function getProjectFacts(params) {
//   return request(`${BASE_URl}/projectTrack/getItemTrackRecord?itemNum=${params}`, {
//     method: 'POST',
//     data: params,
//   });
// }

export function updateProjOverview(params) {
  const { project_overview, item_num } = params;
  return request(
    `${BASE_URl}/projectTrack/updateProjOverview?item_num=${item_num}&project_overview=${project_overview}`,
    {
      method: 'POST',
      data: params,
    },
  );
}
