import request from '@/utils/request';
import { BASE_URl } from "../../../../utils/constant";

//查询项目状态-把握度
export function getProjectStateAndGrasp(params) {
  return request(
    `${BASE_URl}/projectTrack/getProjectStateAndGrasp?item_num=${params}`,
    {
      method: 'POST',
    },
  );
}

//修改项目状态/把握度
export function updateStateAndGrasp(params) {
  const { item_num, item_state, item_grasp } = params;
  return request(
    `${BASE_URl}/projectTrack/updateStateAndGrasp?item_num=${item_num}&item_grasp=${item_grasp ||
      ''}&item_state=${item_state || ''}`,
    {
      method: 'POST',
    },
  );
}

//获取下拉框数据
export function getSearchListInfo() {
  return request(`${BASE_URl}/projectList/getSearchListInfo`, {
    method: 'POST',
  });
}
