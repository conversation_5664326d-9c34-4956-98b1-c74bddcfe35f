import React, { useState, useEffect } from 'react';
import styles from './index.less';
import classNames from 'classnames';
import refresh_icon from '@/assets/refresh_icon.svg';
import greyDot_icon from '@/assets/greyDot_icon.svg';
import edit_icon from '@/assets/edit_icon.svg';
import {
  getProjectStateAndGrasp,
  getSearchListInfo,
  updateStateAndGrasp,
} from './service';
import { Button, Modal, Form, Input, message, Select } from 'antd';
const { Option } = Select;
const State = props => {
  const { itemNumber, changePower, over, memberType } = props;

  const [stateInfo, setStateInfo] = useState([]);
  const [graspInfo, setGraspInfo] = useState([]);
  const [stateList, setStateList] = useState([]);
  const [graspList, setGraspList] = useState([]);
  const [form] = Form.useForm();

  //弹窗标题
  const [modalTitle, setModalTitle] = useState('');

  const [visibleModal, setVisibleModal] = useState(false);
  const [fromData, setFromData] = useState({});

  //获取信息
  const getStateAndGrasp = async value => {
    const { code, data } = await getProjectStateAndGrasp(value);
    if (code === 200) {
      data[0].state.forEach(item => {
        item.key = `${item.create_time}${item.item_state}`;
      });
      setStateInfo(data[0].state);
      setGraspInfo(data[1].grasp);
    }
  };

  //获取下拉
  const getSearchListInfos = async value => {
    const { code, data } = await getSearchListInfo(value);
    if (code === 200) {
      const stateList = [];
      const graspList = [];
      data.forEach(item => {
        if (item.type === 'item_state') {
          stateList.push(item);
        }
        if (item.type === 'item_grasp') {
          graspList.push(item);
        }
      });
      setStateList(stateList);
      setGraspList(graspList);
    }
  };
  useEffect(() => {
    getStateAndGrasp(itemNumber);
    getSearchListInfos();
  }, [itemNumber]);

  const showMoald = title => {
    console.log(title, 'titletitle');
    form.setFieldsValue({
      selectValue: '',
    });
    setModalTitle(title);
    setVisibleModal(true);
  };

  const handleOk = () => {
    setVisibleModal(false);
  };

  const handleCancel = () => {
    setVisibleModal(false);
  };

  //表单提交
  const onFinishFormData = value => {
    console.log(value, '123');
    if (modalTitle === '状态') {
      updateStateGrasp({ item_state: value.selectValue, item_num: itemNumber });
    } else {
      updateStateGrasp({ item_grasp: value.selectValue, item_num: itemNumber });
    }
  };
  //更新
  const updateStateGrasp = async value => {
    const { code, data } = await updateStateAndGrasp(value);
    if (code === 200) {
      message.success({
        content: '更新成功!',
        key: 'updateStateGrasp',
        duration: 2,
      });
      getStateAndGrasp(itemNumber);
      changePower();
    }
    setVisibleModal(false);
  };
  return (
    <div className={classNames(styles.state, 'trackContent')}>
      <div className={styles.left}>
        <div className={styles.title}>
          <div>
            <p>状态</p>
          </div>
          <div>
            {over !== 'over' && memberType !== '1' && (
              <Button onClick={() => showMoald('状态')}>
                {' '}
                <img src={edit_icon} alt="" />
                更新状态
              </Button>
            )}
          </div>
        </div>
        <div className={styles.inFoBox}>
          {stateInfo.length > 0 ? (
            stateInfo.map((item, index) => (
              <div key={index}>
                <div className={styles.inFoBoxTitle}>
                  <img src={greyDot_icon} alt="" />
                  {item.create_time}
                </div>
                <div className={styles.inFoBoxContent}>
                  <p>
                    {item.item_state}{' '}
                    {index === 0 && (
                      <span color="processing" className={styles.tag}>
                        当前状态
                      </span>
                    )}
                  </p>
                  <p>{item.update_id_card}</p>
                </div>
              </div>
            ))
          ) : (
            <div>
              <p>暂无数据</p>
            </div>
          )}
        </div>
      </div>
      <div>
        <div className={styles.title}>
          <div>
            <p>把握度</p>
          </div>
          <div>
            {over !== 'over' && memberType !== '1' && (
              <Button onClick={() => showMoald('把握度')}>
                {' '}
                <img src={edit_icon} alt="" />
                更新把握度
              </Button>
            )}
          </div>
        </div>
        <div className={styles.inFoBox}>
          {graspInfo.length > 0 ? (
            graspInfo.map((item, index) => (
              <div key={index}>
                <div className={styles.inFoBoxTitle}>
                  <img src={greyDot_icon} alt="" />
                  {item.create_time}
                </div>
                <div className={styles.inFoBoxContent}>
                  <p>
                    {item.item_state}{' '}
                    {index === 0 && (
                      <span color="processing" className={styles.tag}>
                        当前状态
                      </span>
                    )}
                  </p>
                  <p>{item.update_id_card}</p>
                </div>
              </div>
            ))
          ) : (
            <div>
              <p>暂无数据</p>
            </div>
          )}
        </div>
      </div>
      <Modal
        title={`更新${modalTitle}`}
        visible={visibleModal}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
      >
        <Form
          // initialValues={fromData}
          layout="vertical"
          form={form}
          name="nest-messages"
          onFinish={onFinishFormData}
        >
          <Form.Item
            label={modalTitle}
            name="selectValue"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Select style={{ width: 300 }} className={styles.selects}>
              {modalTitle === '状态'
                ? stateList.map(item => (
                    <Option value={item.code} key={item.code}>
                      {item.name}
                    </Option>
                  ))
                : graspList.map(item => (
                    <Option value={item.code} key={item.code}>
                      {item.name}
                    </Option>
                  ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default State;
