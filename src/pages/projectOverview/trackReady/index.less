.track {
  input,
  button {
    // border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
  }

  nav {
    display: flex;
    justify-content: space-between;
    padding: 16px 24px;
    background: #ffffff;
    /* 分割线/底部 */
    height: 150px;
    box-shadow: inset 0px -1px 0px #edeff2;

    .NavLeft {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .navTitle {
      font-family: OPPOSans;
      font-style: normal;
      font-weight: bold;
      font-size: 20px;
      line-height: 28px;
      color: #333333;
    }

    .navName {
      font-family: OPPOSans;
      font-style: normal;
      font-weight: 500px;
      font-size: 14px;
      line-height: 22px;
      color: #7a7a7a;

      > span {
        margin-right: 16px;
      }
    }

    .navTime {
      font-family: OPPOSans;
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #333333;
    }

    .navType {
      font-family: OPPOSans;
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #333333;

      > span {
        margin-right: 16px;
      }
    }

    .NavRight {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      white-space: nowrap;

      .tags {
        > div {
          text-align: center;
          padding: 5px 12px;
          border: 1px solid #e0e0e0;
          border-radius: 6px;
          font-style: normal;
          font-weight: 500;
          font-size: 14px;
          line-height: 22px;
          color: #333333;
          margin-right: 10px;
        }

        img {
          margin-right: 5px;
        }

        display: flex;
      }

      > div:last-child {
        display: flex;
        justify-content: flex-end;
      }

      .selectBox {
        font-size: 14px;
        line-height: 22px;
        color: #333333;
        margin-left: 16px;

        > div {
          border-radius: 6px;

          > span {
            font-size: 14px;
            color: #333333;
          }
        }
      }
    }
  }

  section {
    padding-top: 24px;
  }

  .tabs {
    width: 1024px;
    display: flex;
    background: #ffffff;
    border: 1px solid #edeff2;
    box-sizing: border-box;
    box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
    border-radius: 8px;
    margin: 0 auto 16px auto;
    justify-content: space-between;
    padding: 5px 5px;
    height: 48px;

    .checked {
      background: #f7f8fa;
      border-radius: 6px;
      font-weight: bold;
      font-size: 14px;
      line-height: 22px;
      color: #3d7bf8;
    }

    > div {
      text-align: center;
      width: 144px;
      border-radius: 6px;
      padding: 8px 8px;
      cursor: pointer;
    }

    > div:first {
      background: #f7f8fa;
    }
  }

  .content {
    display: flex;
    justify-content: space-between;
    width: 1024px;
    margin: 0 auto;
  }

  .formInfo {
    display: flex;
    flex-direction: column;
    width: 675px;

    > div {
      padding: 12px 24px;
      background: #ffffff;
      border: 1px solid #edeff2;
      box-sizing: border-box;
      box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
      border-radius: 8px;
    }

    .info {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: 16px;

      p {
        font-size: 14px;
        line-height: 22px;
        color: #333333;
      }

      > div {
        font-size: 12px;
        line-height: 20px;
        color: #7a7a7a;
      }

      .title {
        width: 100%;
        display: flex;
        justify-content: space-between;
        font-weight: bold;
        font-size: 16px;
        line-height: 32px;
        color: #333333;
      }

      > div {
        width: 50%;
      }

      > div:last-child {
        width: 100%;
      }
    }
  }

  .progress {
    display: flex;
    flex-direction: column;
    width: 325px;

    .progressName {
      font-size: 12px;
      line-height: 20px;
      text-align: center;
      color: #7a7a7a;
      padding-top: 10px;
    }

    > div {
      background: #ffffff;
      border: 1px solid #edeff2;
      box-sizing: border-box;
      box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
      border-radius: 8px;
      margin-bottom: 16px;
    }

    .title {
      font-family: OPPOSans;
      font-style: normal;
      font-weight: bold;
      font-size: 16px;
      line-height: 24px;
      padding: 12px 16px;
      color: #333333;
    }

    .describe {
      text-align: left;
      padding-left: 16px;

      > div {
        margin-top: 12px;
        font-size: 12px;
        line-height: 20px;
        color: #7a7a7a;
      }

      p {
        margin-bottom: 0;
        font-size: 14px;
        line-height: 22px;
        color: #333333;
      }
    }

    .content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      text-align: center;

      > div:first-child {
        margin-bottom: 12px;
        border-right: 1px solid #edeff2;
      }

      > div {
        width: 50%;
      }
    }

    .summary {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      padding: 12px 16px;

      > div {
        width: 50%;
      }

      > div:first-of-type {
        width: 100%;
        font-family: OPPOSans;
        font-style: normal;
        font-weight: bold;
        font-size: 16px;
        line-height: 24px;
        color: #333333;
        padding-bottom: 24px;
      }
    }
  }

  .iconTitle {
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    color: #333333;
    padding-bottom: 24px;

    img {
      margin-right: 6.5px;
    }
  }

  .status {
    > div {
      > div {
        font-size: 12px;
        line-height: 20px;
        color: #7a7a7a;
      }

      > p {
        font-size: 14px;
        line-height: 22px;
        color: #333333;
      }
    }
  }

  .circle {
    height: 6px;
    width: 6px;
    border-radius: 50%;
    background-color: #34b682;
  }
}

.project_overview {
  word-break: break-all;
  word-wrap: break-word;
}

.tags2 {
  text-align: center;
  padding: 5px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  margin-right: 10px;
  background-color: #3d7bf8;
  color: #fbfbfb;

  img {
    margin-right: 5px;
  }

  display: flex;
}
