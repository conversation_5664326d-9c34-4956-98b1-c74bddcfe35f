import React, { useState, useEffect } from 'react';
import {
  getProjectCheck,
  getProjectGrass,
  deleteCome,
  insertIncome,
  insertOutcome,
  uploadExcelInCome,
  uploadExcelOutCome,
  getInComeType,
  getItemTrial,
} from './service';
import {
  Button,
  Modal,
  Pagination,
  Table,
  Form,
  Tooltip,
  Input,
  InputNumber,
  Select,
  Slider,
  Popconfirm,
  Radio,
  Row,
  Col,
  DatePicker,
  message,
  Upload,
} from 'antd';
import styles from './index.less';
import { Link } from 'umi';
import moment from 'moment';
import {
  UnorderedListOutlined,
  ClockCircleOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { MyContext } from '../../home';

import Procedure from '../../puview/procedure';
import { stringify } from 'querystring';

const { Option } = Select;
const RadioButton = Radio.Button;
export default props => {
  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  useEffect(() => {
    setButtonList(context);
  }, [context]);
  useEffect(() => {
    setColumns(totalColumns1);
  }, [buttonList]);

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [data, setData] = useState([]);
  const [dataTotal, setDataTotal] = useState(0);
  const [tab, setTab] = useState('a');
  setInComeTypeList;
  const [loading, setLoading] = useState(true);
  const [parmas, setParmas] = useState({ page: 1, limit: 10 });

  //弹窗标题
  const [modalTitle, setModalTitle] = useState('');
  const [visibleModal, setVisibleModal] = useState(false);

  const [form] = Form.useForm();
  const dateFormat = 'YYYY/MM/DD';

  const [inComeTypeList, setInComeTypeList] = useState([]);

  // 点击按钮查询
  const [searchParmas, setSearchParmas] = useState({});
  const getlist = async (parmas, tab) => {
    setLoading(true);
    if (tab == 'a') {
      const resp1 = await getProjectCheck(parmas);
      if (resp1.code === 200) {
        setLoading(false);
        resp1.data.records.map(item => {
          item.key = item.id + item.is_stand;
        });
        setData(resp1.data.records);
        setDataTotal(resp1.data.total);
      } else {
      }
    } else {
      const resp2 = await getProjectGrass(parmas);
      if (resp2.code === 200) {
        setLoading(false);
        resp2.data.records.map(item => {
          item.key = item.id + item.is_stand;
        });
        setData(resp2.data.records);
        setDataTotal(resp2.data.total);
      } else {
      }
    }
  };

  const [processData, setProcessData] = useState();
  const [showProcedureModal, setShowProcedureModal] = useState(false);

  const showProcess = async item => {
    const { data, code } = await getItemTrial(item.id);
    if (code === 200) {
      const nodes = [];
      const edges = [];
      data[0].TrialInfo.forEach(item => {
        nodes.push({
          id: String(item.trial_id),
          label: String(item.name),
          status: String(item.is_status),
          ShUserName: item.ShUserName,
        });
      });
      data[1].TrialCom.forEach(item => {
        edges.push({
          source: String(item.trial_id),
          target: String(item.lower_id),
        });
      });
      setProcessData({ nodes: nodes, edges: edges });
      setShowProcedureModal(true);
    }
  };
  //获取费用类分类
  const getInComeTypes = async () => {
    const { data, code } = await getInComeType();
    if (code === 200) {
      setInComeTypeList(data);
    }
  };
  useEffect(() => {
    getInComeTypes();
  }, []);
  useEffect(() => {
    getlist(parmas, tab);
  }, [parmas, tab]);
  //动态表头
  const totalColumns1 = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: '1',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '部门名称',
      dataIndex: 'dept',
      ellipsis: {
        showTitle: false,
      },
      key: '2',
      align: 'left',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '项目经理',
      dataIndex: 'manager',
      key: '3',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: value => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '客户',
      align: 'left',
      dataIndex: 'project_client',
      ellipsis: {
        showTitle: false,
      },
      key: 'project_client',
      render: value => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: '6',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      filterIcon: filtered => handleGetIcon(filtered),
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num && moment(item_num).format('YYYY-MM-DD')}
        </Tooltip>
      ),
    },
    {
      title: '结束时间',
      dataIndex: 'end_time',
      key: '5',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num && moment(item_num).format('YYYY-MM-DD')}
        </Tooltip>
      ),
      align: 'left',
    },
    {
      title: '当前流程',
      dataIndex: 'tr_name',
      key: '5',
      ellipsis: {
        showTitle: false,
      },
      render: (value, item) => (
        <a onClick={() => showProcess(item)}>查看流程</a>
      ),
      align: 'left',
    },
    {
      title: '审核类型',
      dataIndex: 'is_stands',
      key: 'is_stands',
      ellipsis: {
        showTitle: false,
      },
      render: value => {
        if (value === '3') {
          return '变更审核';
        } else if (value === '5') {
          return '结项审核';
        } else {
          return '立项审核';
        }
      },
      align: 'left',
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: 80,
      render: (item, record) => {
        return (
          <div>
            {record.is_stand === '1' && (
              <Link
                to={{
                  pathname: '/projectApproval',
                  query: {
                    trial_ids: record.trial_ids,
                    id: record.id,
                  },
                }}
              >
                立项审核
              </Link>
            )}
            {record.is_stand === '3' && (
              <Link
                to={{
                  pathname: '/projectExamine',
                  query: {
                    trial_ids: record.trial_ids,
                    id: record.id,
                  },
                }}
              >
                变更审核
              </Link>
            )}
            {record.is_stand === '1111' && <Link>审核完成</Link>}
            {record.is_stand === '5' && (
              <Link
                to={{
                  pathname: '/closureApporval',
                  query: {
                    id: record.id,
                    trial_ids: record.trial_ids,
                    type: 'approval',
                  },
                }}
              >
                结项审核
              </Link>
            )}
            {((buttonList.includes('/projectApprovalLookOver') ||
              buttonList.includes('admin')) &&
              record.is_stand !== '1' &&
              record.is_stand !== '3' &&
              record.is_stands !== '5' && (
                <div>
                  <Link
                    to={{
                      pathname: '/projectApprovalLookOver',
                      query: {
                        id: record.id,
                        type: 'lookOver',
                      },
                    }}
                  >
                    审核中
                  </Link>
                </div>
              )) ||
              (record.is_stand !== '1' &&
                record.is_stand !== '3' &&
                record.is_stand !== '5' &&
                record.is_stand !== '1111' &&
                record.is_stands === '5' && (
                  <div>
                    <Link
                      to={{
                        pathname: '/closureApporval',
                        query: {
                          id: record.id,
                          type: 'lookOver',
                        },
                      }}
                    >
                      审核中
                    </Link>
                  </div>
                ))}
          </div>
        );
      },
    },
  ];
  const totalColumns2 = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: '1',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '部门名称',
      dataIndex: 'dept',
      ellipsis: {
        showTitle: false,
      },
      key: '2',
      align: 'left',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '项目经理',
      dataIndex: 'manager',
      key: '3',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '客户',
      align: 'left',
      dataIndex: 'project_client',
      key: '4',
      ellipsis: {
        showTitle: false,
      },
      render: value => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: '6',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      filterIcon: filtered => handleGetIcon(filtered),
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num && moment(item_num).format('YYYY-MM-DD')}
        </Tooltip>
      ),
    },
    {
      title: '结束时间',
      dataIndex: 'end_time',
      key: '5',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num && moment(item_num).format('YYYY-MM-DD')}
        </Tooltip>
      ),
      align: 'left',
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: 80,
      render: (text, record) => {
        return (
          <div>
            {(buttonList.includes('/createProject?id') ||
              buttonList.includes('admin')) &&
              record.is_stand == '0' && (
                <Link
                  to={{
                    pathname: '/createProject',
                    query: {
                      id: record.id,
                    },
                  }}
                >
                  草稿编辑
                </Link>
              )}
            {(buttonList.includes('/updateProject') ||
              buttonList.includes('admin')) &&
              record.is_stand != '0' && (
                <Link
                  to={{
                    pathname: '/updateProject',
                    query: {
                      id: record.id,
                      type: 'updateDraft',
                    },
                  }}
                >
                  变更编辑
                </Link>
              )}
          </div>
        );
      },
    },
  ];
  const onChangePageNumber = (value, size) => {
    setParmas({ page: value, limit: size });
  };

  const deleteItem = value => {
    const { name, id } = value;
    deleteComes({
      name: name,
      id: id,
    });
  };

  const deleteComes = async value => {
    const { data, code } = await deleteCome(value);
    if (code === 200) {
      message.success({
        content: '删除成功!',
        key: 'deleteComes',
        duration: 2,
      });
      if (value.name === 'out') {
        getlist({ ...parmas }, 'b');
      } else {
        getlist({ ...parmas }, 'a');
      }
    }
  };

  const [columns, setColumns] = useState(totalColumns1);
  //选择表格行
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };
  //弹窗
  // const handleOnOk = async () => {
  // };
  //改变每页条数
  const pageSizeChange = value => {
    setParmas({ ...parmas, limit: value });
  };
  //tab切换
  const tabChange = e => {
    switch (e.target.value) {
      case 'a':
        setTab('a');
        setColumns(totalColumns1);
        setParmas({ page: 1, limit: 10 });

        break;
      case 'b':
        setTab('b');
        setColumns(totalColumns2);
        setParmas({ page: 1, limit: 10 });

        break;
    }
  };

  const handleOk = () => {
    setVisibleModal(false);
  };

  const handleCancel = () => {
    setVisibleModal(false);
  };

  //表单提交
  const onFinishFormData = value => {
    if (tab === 'a') {
      const planBillingTime = moment(value.planBillingTime).format(
        'YYYY-MM-DD',
      );
      const refundTime = moment(value.refundTime).format('YYYY-MM-DD');
      addProjectItem({
        ...value,
        planBillingTime,
        refundTime,
      });
    }
    if (tab === 'b') {
      addProjectItem({
        ...value,
      });
    }
  };
  const addProjectItem = async parmas => {
    if (tab === 'a') {
      const resp1 = await insertIncome(parmas);
      if (resp1.code === 200) {
        message.success({
          content: '新增成功!',
          key: 'addProjectItem',
          duration: 2,
        });
        setVisibleModal(false);
        getlist(parmas, tab);
      } else {
        message.error({
          content: '新增失败!',
          key: 'addProjectItem',
          duration: 2,
        });
      }
    }
    if (tab === 'b') {
      const resp2 = await insertOutcome(parmas);
      if (resp2.code === 200) {
        message.success({
          content: '新增成功!',
          key: 'addProjectItem',
          duration: 2,
        });
        getlist(parmas, tab);
        setVisibleModal(false);
      } else {
        message.error({
          content: '新增失败!',
          key: 'addProjectItem',
          duration: 2,
        });
      }
    }
  };

  /**
   * 搜索值变化
   */
  const handleSearchParams = props => {
    const { key, value } = props;
    setSearchParmas({ ...searchParmas, [key]: value });
  };
  // 搜索
  const handleOnSearch = () => {
    setParmas({ ...parmas, ...searchParmas, page: 1 });
  };
  const { limit, page } = parmas;

  return (
    <div className={styles.contentBox}>
      <div className={styles.card}>
        <div className={styles.searchInput}>
          <div>
            <p>项目名称</p>
            <Input
              onChange={e =>
                handleSearchParams({ value: e.target.value, key: 'itemName' })
              }
              placeholder="请输入"
            ></Input>
          </div>
          <div style={{ display: 'flex' }}>
            <div style={{ marginRight: 20 }}>
              <p>项目开始时间</p>
              <DatePicker
                onChange={(date, deteString) =>
                  handleSearchParams({ value: deteString, key: 'startTime' })
                }
                format={dateFormat}
                suffixIcon={<ClockCircleOutlined />}
                disabledDate={current => {
                  return (
                    searchParmas.endTime &&
                    current > moment(searchParmas.endTime)
                  );
                }}
                className={styles.rangePicker}
              />
            </div>
            <div>
              <p>项目结束时间</p>
              <DatePicker
                onChange={(date, deteString) =>
                  handleSearchParams({ value: deteString, key: 'endTime' })
                }
                disabledDate={current => {
                  return (
                    searchParmas.startTime &&
                    current < moment(searchParmas.startTime)
                  );
                }}
                format={dateFormat}
                suffixIcon={<ClockCircleOutlined />}
                className={styles.rangePicker}
              />
            </div>
          </div>
          <div>
            <p></p>
            <br />
            <Button type="primary" onClick={handleOnSearch}>
              查询
            </Button>
          </div>
          <div></div>
        </div>
      </div>

      <div className={styles.card}>
        <div className={styles.cardOption}>
          <div className={styles.tabSwitch}>
            <Radio.Group
              defaultValue="a"
              buttonStyle="solid"
              onChange={e => tabChange(e)}
            >
              <RadioButton value="a">待审核</RadioButton>
              <RadioButton value="b">待提交</RadioButton>
            </Radio.Group>
          </div>
        </div>
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          // rowSelection={rowSelection}
          loading={loading}
          size="middle"
          className={styles.anTdTable}
        />
        <div className={styles.splitPigination}>
          <div>
            <Select
              defaultValue="10"
              style={{ width: 150 }}
              className={styles.selects}
              onChange={pageSizeChange}
            >
              <Option value="10">显示结果：10条</Option>
              <Option value="20">显示结果：20条</Option>
              <Option value="50">显示结果：50条</Option>
            </Select>
            <span className={styles.total}>共{dataTotal}条</span>
          </div>
          <Pagination
            total={dataTotal || 0}
            pageSize={limit}
            showSizeChanger={false}
            current={page}
            key={67}
            onChange={onChangePageNumber}
          />
        </div>
        <Modal
          title={`${modalTitle}`}
          visible={visibleModal}
          onOk={handleOk}
          onCancel={handleCancel}
          footer={null}
        >
          <Form
            layout="vertical"
            form={form}
            name="nest-messages"
            onFinish={onFinishFormData}
          >
            {tab === 'a' && (
              <>
                <Form.Item
                  label="实际开票金额"
                  name="actualBillingAmount"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <InputNumber
                    min={0}
                    formatter={value => `${value}W`}
                    parser={value => value.replace('W', '')}
                    style={{ width: 300 }}
                  />
                </Form.Item>
                <Form.Item
                  label="开票时间"
                  name="planBillingTime"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <DatePicker style={{ width: 300 }} />
                </Form.Item>
                <Form.Item
                  label="项目编号"
                  name="itemNum"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Input style={{ width: 300 }} />
                </Form.Item>
                <Form.Item
                  label="回款金额"
                  name="refundNumber"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <InputNumber
                    min={0}
                    style={{ width: 300 }}
                    formatter={value => `${value}W`}
                    parser={value => value.replace('W', '')}
                  />
                </Form.Item>
                <Form.Item
                  label="回款时间"
                  name="refundTime"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <DatePicker style={{ width: 300 }} />
                </Form.Item>
              </>
            )}
            {tab === 'b' && (
              <>
                <Form.Item
                  label="项目编号"
                  name="itemNum"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Input style={{ width: 300 }} />
                </Form.Item>
                <Form.Item
                  label="费用类别"
                  name="costType"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    style={{ width: 300 }}
                    showSearch
                    filterOption={(input, option) =>
                      option.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {inComeTypeList.length > 0 &&
                      inComeTypeList.map((item, index) => (
                        <Option value={item.name} key={index}>
                          {item.name}
                        </Option>
                      ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  label="实际费用"
                  name="actualCost"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <InputNumber
                    min={0}
                    style={{ width: 300 }}
                    formatter={value => `${value}W`}
                    parser={value => value.replace('W', '')}
                  />
                </Form.Item>
              </>
            )}
            <Form.Item style={{ marginBottom: 0 }}>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Form.Item>
          </Form>
        </Modal>
        <Modal
          // title="Basic Modal"
          visible={showProcedureModal}
          onOk={() => setShowProcedureModal(false)}
          onCancel={() => setShowProcedureModal(false)}
          width={1100}
        >
          <Procedure processData={processData} />
        </Modal>
      </div>
    </div>
  );
};
