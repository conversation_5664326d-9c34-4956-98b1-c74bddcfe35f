import request from '@/utils/request';
import { BASE_URl } from "../../../utils/constant";

//获取待审批list
export function getProjectCheck(parmas) {
  return request(`${BASE_URl}/projectList/getProjectCheck`, {
    method: 'post',
    data: parmas,
  });
}
//获取草稿list
export function getProjectGrass(parmas) {
  return request(`${BASE_URl}/projectList/getProjectGrass`, {
    method: 'post',
    data: parmas,
  });
}

//删除
export function deleteCome(parmas) {
  const { name, id } = parmas;
  return request(`${BASE_URl}/Finance/deleteCome?name=${name}&id=${id}`, {
    method: 'post',
  });
}

//新增收入
export function insertIncome(parmas) {
  return request(`${BASE_URl}/Finance/insertIncome`, {
    method: 'post',
    data: parmas,
  });
}
//新增成本
export function insertOutcome(parmas) {
  return request(`${BASE_URl}/Finance/insertOutcome`, {
    method: 'post',
    data: parmas,
  });
}

//上传项目收入
export function uploadExcelInCome(parmas) {
  const userInfo = JSON.parse(localStorage.getItem('userInfo'));

  return request(`${BASE_URl}/File/uploadExcelInCome`, {
    method: 'post',
    requestType: 'form',
    headers: {
      isToken: false,
      Authorization: `Bearer ${userInfo.access_token}`,
    },
    data: parmas,
  });
}

//上传项目成本
export function uploadExcelOutCome(parmas) {
  return request(`${BASE_URl}/File/uploadExcelOutCome`, {
    method: 'post',
    requestType: 'form',
    data: parmas,
  });
}

//费用类型下拉框信息
export function getInComeType() {
  return request(`${BASE_URl}/Finance/getInComeType`, {
    method: 'post',
  });
}

//查询项目审批流程图信息
export function getItemTrial(id) {
  return request(`${BASE_URl}/projectStand/getItemTrial?item_id=${id}`, {
    method: 'post',
  });
}
