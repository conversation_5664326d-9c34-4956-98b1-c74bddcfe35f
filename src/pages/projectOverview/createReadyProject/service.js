import request from '@/utils/request';
import { BASE_URl } from '../../../utils/constant';

//费用类型下拉框信息
export function getInComeType() {
  return request(`${BASE_URl}/Finance/getInComeType`, {
    method: 'post',
  });
}

//获取部门下拉框数据
export function getDeptListInfo() {
  return request(`${BASE_URl}/dept/SWCARESdeptAll`, {
    method: 'POST',
  });
}

//获取姓名下拉框数据
export function getNameListInfo(params) {
  return request(`${BASE_URl}/user/selectSysUserPageData`, {
    method: 'POST',
    data: params,
  });
}

//  项目列表标题行下拉框信息
export function getSearchListInfo() {
  return request(`${BASE_URl}/projectList/getSearchListInfo`, {
    method: 'POST',
  });
}

//  保存
export function inProjectStand(params) {
  return request(`${BASE_URl}/projectIntendStand/addIntendStandItem`, {
    method: 'POST',
    data: params,
  });
}

//把把预立项项目立项
export function intendItemToStandItem(params) {
  return request(
    `${BASE_URl}/projectIntendStand/intendItemToStandItem?id=${params.id}`,
    {
      method: 'POST',
      data: params,
    },
  );
}

//编辑信息
// export function gitItemInfo(params) {
//   const { type, id } = params;
//   if (type === 'update') {
//     return request(`${BASE_URl}/projectStand/getUpItemInfo?id=${id}`, {
//       method: 'post',
//     });
//   } else {
//     return request(`${BASE_URl}/projectStand/gitItemInfo?id=${id}`, {
//       method: 'post',
//     });
//   }
// }

export function gitItemInfo(params) {
  const { id } = params;
  return request(
    `${BASE_URl}/projectIntendStand/getIntendStandItemInfo?id=${id}`,
    {
      method: 'post',
    },
  );
}
