import request from '@/utils/request';
import { BASE_URl } from "../../utils/constant";

//获取工作台数据
export function getWorkBench(parmas) {
  return request(`${BASE_URl}/projectEnd/getWorkBench`, {
    method: 'post',
    data: parmas,
  });
}

//获取部门下拉框数据
export function getDeptListInfo() {
  return request(`${BASE_URl}/dept/SWCARESdeptAll`, {
    method: 'POST',
  });
}

export function getUser() {
  return request(`${BASE_URl}/user/info`, {
    method: 'get',
  });
}

//获取待审批list
export function getProjectCheck(parmas) {
  return request(`${BASE_URl}/projectList/getProjectCheck`, {
    method: 'post',
    data: parmas,
  });
}
//获取草稿list
export function getProjectGrass(parmas) {
  return request(`${BASE_URl}/projectList/getProjectGrass`, {
    method: 'post',
    data: parmas,
  });
}

//获取草稿list
export function getTrackListPage(parmas) {
  return request(`${BASE_URl}/projectList/getTrackListPage`, {
    method: 'post',
    data: parmas,
  });
}

//查询项目审批流程图信息
export function getItemTrial(id) {
  return request(`${BASE_URl}/projectStand/getItemTrial?item_id=${id}`, {
    method: 'post',
  });
}

//  查询项目列表名称
export function getItemNameList(params) {
  const { name } = params;
  return request(`${BASE_URl}/projectList/getItemNameList?name=${name}`, {
    method: 'POST',
  });
}

// 删除项目
export function delItemHis(params) {
  return request(`${BASE_URl}/projectStand/delItemHis?item_id=${params}`, {
    method: 'POST',
  });
}

// 撤回项目
export function upItemBack(params) {
  const { id, type } = params;
  return request(
    `${BASE_URl}/projectStand/upItemBack?item_id=${id}&type=${type}`,
    {
      method: 'POST',
    },
  );
}
