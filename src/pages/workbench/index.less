.contentBox {
  min-width: 900px;
  padding: 24px;
  background: #f7f8fa;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  > div {
    width: 100%;
  }
}

.card {
  width: 100%;
  height: 100%;
  background: #ffffff;
  border: 1px solid #edeff2;
  box-sizing: border-box;
  padding: 13px 24px;
  box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
  border-radius: 8px;
  margin-bottom: 24px;

  :global {
    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      background-color: #3d7bf8;
      border-color: #3d7bf8;
    }

    .ant-radio-button-wrapper:first-child {
      border-radius: 5px 0 0 5px;
    }

    .ant-radio-button-wrapper:last-child {
      border-radius: 0 5px 5px 0;
    }

    .ant-btn-primary {
      background-color: #3d7bf8;
    }
  }

  .cardOption {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .tabSwitch {
    width: 180px;
  }

  .cardRight {
    width: 280px;
    display: flex;
    justify-content: space-between;

    .spotIcon > div {
      width: 4px;
      height: 4px;
      background-color: #7a7a7a;
      border-radius: 50%;
    }

    .spotIcon > div:not(:first-child) {
      margin-top: 2px;
    }
  }

  .project {
    display: flex;
    justify-content: space-between;

    button {
      border-radius: 0;
      transition: all 0s ease;
      position: relative;
      top: 0;
      vertical-align: middle;
    }

    .manage {
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
    }

    .no_manage {
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }

    .active {
      background: #3d7bf8;
      color: #ffffff;
    }

    .focus {
      button {
        font-family: OPPOSans;
        font-style: normal;
        font-size: 14px;
        border: 1px solid #edeff2;
        box-sizing: border-box;
        border-radius: 6px;
      }
    }
  }

  p {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    /* identical to box height, or 157% */
    /* Netural Color/Grey 600 */
    color: #525252;
  }

  input,
  button {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
  }
}

.left_box {
}

.title {
  font-family: OPPOSans;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
  color: #333333;
  margin-bottom: 12px;
}

.medium {
  font-size: 12px;
  line-height: 20px;
  color: #7a7a7a;
}

.userinfo_box {
  box-shadow: inset 0px -1px 0px #edeff2;
  display: flex;
  justify-content: space-between;
  line-height: 32px;
  padding-bottom: 16px;
}

.project_box {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}

.project_item {
  cursor: pointer;
  padding: 12px 16px;
  //width: 19%;
  width: 24%;
  background: #ffffff;
  border: 1px solid #edeff2;
  box-sizing: border-box;
  box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
  border-radius: 8px;

  .itemLabels {
    display: flex;
    justify-content: space-between;
    div {
      font-size: 14px;
      line-height: 22px;
      color: #7a7a7a;
    }
  }

  > div:nth-child(1) {
    font-size: 14px;
    line-height: 22px;
    color: #7a7a7a;
  }

  > div:nth-child(2) {
    font-size: 18px;
    line-height: 26px;
    color: #333333;
  }
}

.progress_number {
  display: flex;
  justify-content: space-between;

  > div {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    // line-height: 20px;
    color: #666666;
    display: flex;
    align-items: center;
  }
}

.checked_box {
  background: #3d7bf8;
  border: 1px solid #edeff2;
  box-sizing: border-box;
  box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
  border-radius: 8px;
  color: #ffffff;

  > div:nth-child(1) {
    color: #ffffff;
    opacity: 0.6;
  }

  .itemLabels {
    div {
      color: #ffffff;
      opacity: 0.6;
    }
  }

  > div:nth-child(2) {
    font-size: 18px;
    color: #ffffff;
  }

  .progress_number {
    > div {
      color: #ffffff;
      opacity: 0.6;
    }
  }
}

.anTdTable tr > th,
.anTdTable tr > td {
  border-bottom: 0px;
}

.anTdTable tr > th {
  font-size: 12px;
  line-height: 20px;
  color: #7a7a7a;

  > div > div {
    padding: 10px 16px;
  }
}

.selects,
.selects > div {
  border-color: #edeff2 !important;
  box-sizing: border-box;
  border-radius: 6px !important;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;

  > div {
    display: flex;

    > div {
      width: 144px;
      height: 32px;
      left: 0px;
      top: 0px;
      background: #ffffff;
      border: 1px solid #edeff2;
      box-sizing: border-box;
      border-radius: 6px;
    }
  }

  p {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    margin: 6px 0 0 10px;
    color: #7a7a7a;
  }

  > li {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
  }
}

.splitPigination {
  display: flex;
  justify-content: space-between;
  padding: 0 0 0 0px;
  margin-top: 10px;

  .pageSelect {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
  }

  .total {
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    margin-left: 8px;
    color: #7a7a7a;
  }

  input,
  button,
  li {
    box-sizing: border-box;
    border-radius: 6px;
  }
}

.rigth_box {
  overflow-y: scroll;

  .item {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 20px;
    color: #1a1a1a;
    display: flex;
    white-space: nowrap;
    justify-content: space-between;
    margin-bottom: 5px;
    cursor: pointer;
  }

  .item:hover {
    color: #3d7bf8;
  }
}

.work_box {
}

.searchInput {
  display: flex;
  justify-content: space-between;
  margin: 25px 0;

  p {
    margin-bottom: 4px;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 22px;
    color: #525252;
  }

  > div {
    width: 20%;
  }

  > div:nth-child(4) {
    width: 10%;
  }
}

.searchInput2 {
  display: flex;
  justify-content: space-between;
  margin: 25px 0;

  p {
    margin-bottom: 4px;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 22px;
    color: #525252;
  }

  > div {
    width: 20%;
  }

  > div:nth-child(4) {
    width: 10%;
  }
}

.rangePicker {
  border: 1px solid #edeff2;
  box-sizing: border-box;
  border-radius: 6px;

  input {
    border: 0;
  }
}

.circle {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  margin-right: 5px;
}
