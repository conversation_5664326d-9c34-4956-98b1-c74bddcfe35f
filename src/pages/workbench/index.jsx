import React, { useState, useEffect } from 'react';
import styles from './index.less';
import classNames from 'classnames';
import {
  Button,
  Modal,
  Upload,
  Form,
  Input,
  Select,
  Table,
  Pagination,
  DatePicker,
  message,
  Popconfirm,
  Progress,
  Tooltip,
} from 'antd';
import {
  getWorkBench,
  getUser,
  getTrackListPage,
  getProjectGrass,
  getItemNameList,
  getItemTrial,
  getProjectCheck,
  getDeptListInfo,
  delItemHis,
  upItemBack,
} from './service';
import { Link } from 'umi';
import {
  HistoryOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CodeSandboxOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { history } from 'umi';
const { Option } = Select;
import moment from 'moment';
const dateFormat = 'YYYY/MM/DD';
import { MyContext } from '../home';
import Procedure from '../puview/procedure';

export default () => {
  const userInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
  //我审核的
  const columns3 = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: '1',
      fixed: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '部门名称',
      dataIndex: 'dept',
      ellipsis: {
        showTitle: false,
      },
      key: '2',
      align: 'left',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '项目经理',
      dataIndex: 'manager',
      key: '3',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: value => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '客户',
      align: 'left',
      dataIndex: 'project_client',
      ellipsis: {
        showTitle: false,
      },
      key: 'project_client',
      render: value => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: '6',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      filterIcon: filtered => handleGetIcon(filtered),
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num && moment(item_num).format('YYYY-MM-DD')}
        </Tooltip>
      ),
    },
    {
      title: '结束时间',
      dataIndex: 'end_time',
      key: '5',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num && moment(item_num).format('YYYY-MM-DD')}
        </Tooltip>
      ),
      align: 'left',
    },
    {
      title: '审核类型',
      dataIndex: 'is_stand',
      key: 'is_stand',
      ellipsis: {
        showTitle: false,
      },
      render: value => {
        if (value === '3') {
          return '变更审核';
        } else if (value === '5') {
          return '结项审核';
        } else {
          return '立项审核';
        }
      },
      align: 'left',
    },
    {
      title: '当前流程',
      dataIndex: 'tr_name',
      key: '5',
      ellipsis: {
        showTitle: false,
      },
      render: (value, item) => (
        <a onClick={() => showProcess(item)}>查看流程</a>
      ),
      align: 'left',
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: 80,
      render: (item, record) => {
        let url = {};
        if (record.is_stand === '3') {
          url = {
            pathname: '/projectExamine',
            query: {
              trial_ids: record.trial_ids,
              id: record.id,
            },
          };
        } else if (record.is_stand === '5') {
          url = {
            pathname: '/closureApporval',
            query: {
              id: record.id,
              trial_ids: record.trial_ids,
              type: 'approval',
            },
          };
        } else if (record.is_stand === '1') {
          url = {
            pathname: '/projectApproval',
            query: {
              trial_ids: record.trial_ids,
              id: record.id,
            },
          };
        }
        // const lookUrl =
        return (
          <div>
            {// (buttonList.includes('/createProject?id') ||
            //   buttonList.includes('admin')) &&
            item.isType === '4' && <Link to={{ ...url }}>审核</Link>}
            {item.isType === '5' && (
              <Link
                to={{
                  pathname: '/projectApproval',
                  query: {
                    trial_ids: record.trial_ids,
                    id: record.id,
                    type: 'lookOver',
                  },
                }}
              >
                查看
                {/*已审核*/}
              </Link>
            )}
          </div>
        );
      },
    },
  ];
  //我创建的
  const columns2 = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: '1',
      fixed: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '部门名称',
      dataIndex: 'dept',
      ellipsis: {
        showTitle: false,
      },
      key: '2',
      align: 'left',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '项目经理',
      dataIndex: 'manager',
      key: '3',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '客户',
      align: 'left',
      dataIndex: 'project_client',
      key: '4',
      ellipsis: {
        showTitle: false,
      },
      render: value => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '开始时间',
      dataIndex: 'start_time',
      key: '6',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      filterIcon: filtered => handleGetIcon(filtered),
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num && moment(item_num).format('YYYY-MM-DD')}
        </Tooltip>
      ),
    },
    {
      title: '结束时间',
      dataIndex: 'end_time',
      key: '5',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num && moment(item_num).format('YYYY-MM-DD')}
        </Tooltip>
      ),
      align: 'left',
    },
    {
      title: '类型',
      dataIndex: 'is_stand',
      key: 'is_stand',
      ellipsis: {
        showTitle: false,
      },
      render: (value, record) => {
        if (value === '3') {
          return '变更审核';
        } else if (value === '5') {
          return '结项审核';
        } else if (value === '1') {
          return '立项审核';
        }
        if (record.is_type === '0' && record.is_stand === '0') {
          return '立项草稿';
        } else if (record.is_type === '0' && record.is_stand === '2') {
          return '项目变更草稿';
        } else if (record.is_type === '0' && record.is_stand === '4') {
          return '结项草稿';
        }
      },
      align: 'left',
    },
    {
      title: '查看流程',
      dataIndex: 'is_type',
      key: '5',
      render: (item, value) => {
        return (
          value.is_stand !== '' && (
            <a onClick={() => showProcess(value)}>查看流程</a>
          )
        );
      },
      align: 'left',
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: 140,
      render: record => {
        let url = {};
        let name = '';
        if (record.is_type === '0' && record.is_stand === '0') {
          url = {
            pathname: '/createProject',
            query: {
              id: record.id,
            },
          };
          name = '编辑';
        } else if (record.is_type === '0' && record.is_stand === '2') {
          url = {
            pathname: '/updateProject',
            query: {
              id: record.id,
              type: 'updateDraft',
            },
          };
          name = '编辑';
        } else if (record.is_type === '0' && record.is_stand === '4') {
          url = {
            pathname: '/closure',
            query: {
              itemNo: record.item_num,
              id: record.id,
            },
          };
          name = '编辑';
        }
        return (
          <div>
            {record.is_type === '0' && (
              <>
                <Link to={url}>{name}</Link>
                <Popconfirm
                  title="是否删除？"
                  okText="是"
                  cancelText="否"
                  onConfirm={() => deleteProject(record.id)}
                >
                  <a style={{ marginLeft: 10 }}>删除</a>
                </Popconfirm>
              </>
            )}
            {record.is_type === '1' && record.is_stand === '1' && (
              <>
                {' '}
                <Link
                  to={{
                    pathname: '/projectApprovalLookOver',
                    query: {
                      id: record.id,
                      type: 'lookOver',
                    },
                  }}
                >
                  审核中
                </Link>
                <Popconfirm
                  title="是否撤销？"
                  okText="是"
                  cancelText="否"
                  onConfirm={() => recallClick({ id: record.id, type: '0' })}
                >
                  <a style={{ marginLeft: 10 }}>撤销</a>
                </Popconfirm>
              </>
            )}
            {record.is_type === '1' && record.is_stand === '5' && (
              <>
                <Link
                  to={{
                    pathname: '/closureApporval',
                    query: {
                      id: record.id,
                      trial_ids: record.trial_ids,
                    },
                  }}
                >
                  审核中
                </Link>
                <Popconfirm
                  title="是否撤销？"
                  okText="是"
                  cancelText="否"
                  onConfirm={() => recallClick({ id: record.id, type: '2' })}
                >
                  <a style={{ marginLeft: 10 }}>撤销</a>
                </Popconfirm>
              </>
            )}
            {record.is_type === '1' && record.is_stand === '3' && (
              <>
                {' '}
                <Link
                  to={{
                    pathname: '/projectExamine',
                    query: {
                      id: record.id,
                      trial_ids: record.trial_ids,
                      lookOver: '1',
                    },
                  }}
                >
                  审核中
                </Link>
                <Popconfirm
                  title="是否撤销？"
                  okText="是"
                  cancelText="否"
                  onConfirm={() => recallClick({ id: record.id, type: '1' })}
                >
                  <a style={{ marginLeft: 10 }}>撤销</a>
                </Popconfirm>
              </>
            )}

            {record.is_type === '2' && (
              <Link
                to={{
                  pathname: '/projectList/track',
                  query: {
                    itemNo: record.item_num,
                    id: record.id,
                  },
                }}
              >
                跟踪
              </Link>
            )}
            {// (buttonList.includes('/projectList/track') ||
            //   buttonList.includes('admin')) &&
            record.is_type === '3' && (
              <Link
                to={{
                  pathname: '/projectList/track',
                  query: {
                    itemNo: record.item_num,
                    id: record.id,
                  },
                }}
              >
                跟踪
              </Link>
            )}
          </div>
        );
      },
    },
  ];

  //我管理的 我参与的
  const columns0 = [
    {
      title: '项目名称',
      ellipsis: {
        showTitle: false,
      },
      width: 30,
      fixed: 'left',
      dataIndex: 'name',
      key: '100',
    },
    {
      title: '项目编号',
      dataIndex: 'item_num',
      key: '2',
      width: 40,
      ellipsis: {
        showTitle: false,
      },
      align: 'left',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '立项部门',
      align: 'left',
      dataIndex: 'dept',
      ellipsis: {
        showTitle: false,
      },
      key: '3',
      width: 30,
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '项目状态',
      dataIndex: 'item_state',
      key: '4',
      width: 35,
      align: 'left',
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '项目类型',
      dataIndex: 'type',
      key: '5',
      width: 30,
      ellipsis: {
        showTitle: false,
      },
      align: 'left',
    },
    {
      title: '项目级别',
      dataIndex: 'level',
      filterIcon: filtered => handleGetIcon(filtered),
      key: '6',
      filterMultiple: false,
      width: 25,
      ellipsis: {
        showTitle: false,
      },
      align: 'left',
    },
    {
      title: '收入分类',
      dataIndex: 'in_come_type',
      key: '7',
      width: 45,
      ellipsis: {
        showTitle: false,
      },
      align: 'left',
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '项目把握度',
      dataIndex: 'item_grasp',
      key: '8',
      align: 'left',
      width: 40,
      ellipsis: {
        showTitle: false,
      },
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '当前里程碑',
      dataIndex: 'item_stage_type',
      key: '15',
      ellipsis: {
        showTitle: false,
      },
      align: 'left',
      filterIcon: filtered => handleGetIcon(filtered),
      width: 30,
    },
    {
      title: `计划起止时间`,
      key: '18',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      sorter: (a, b) => {
        const aTime = new Date(a.start_time).getTime();
        const bTime = new Date(b.start_time).getTime();
        return aTime - bTime;
      },
      width: 60,
      render: render => (
        <div>
          {render.start_time} 至 {render.end_time}
        </div>
      ),
    },
    {
      title: '项目经理',
      dataIndex: 'manager',
      key: '19',
      align: 'left',
      width: 20,

      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: 20,
      render: (text, record) => {
        return (
          <div>
            {record.item_stage === 'completed_item' ||
            record.item_stage === 'terminated' ? (
              <>
                <span style={{ marginLeft: 8 }}>
                  <Link
                    to={{
                      pathname: '/projectList/track',
                      query: {
                        itemNo: record.item_num,
                        id: record.id,
                        over: 'over',
                      },
                    }}
                  >
                    已结项
                  </Link>
                </span>
              </>
            ) : (
              <Link
                to={{
                  pathname: '/projectList/track',
                  query: {
                    itemNo: record.item_num,
                    id: record.id,
                  },
                }}
              >
                跟踪
              </Link>
            )}
          </div>
        );
      },
    },
  ];

  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  useEffect(() => {
    setButtonList(context);
  }, [context]);
  const [workBenchInfo, setWorkBenchInfo] = useState({});
  const [user, setUser] = useState();
  const [checked, setChecked] = useState(0);
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState();
  const [dataTotal, setDataTotal] = useState(0);
  const [columns, setColumns] = useState(columns0);
  const [parmas, setParmas] = useState({ page: 1, limit: 10 });
  const [deptList, setDeptList] = useState([]);

  const getworkBenchInfo = async () => {
    const { data, code } = await getWorkBench();
    if (code === 200) {
      setWorkBenchInfo(data);
    }
  };
  const getUsers = async value => {
    const { code, data } = await getUser();
    if (code === 200) {
      setUser(data);
    }
  };
  const handeProjectBoxClick = value => {
    setParmas({ page: 1, limit: 10 });
    setChecked(value);
  };
  useEffect(() => {
    getworkBenchInfo();
    getUsers();
    getDeptListInfos();
  }, []);

  useEffect(() => {
    getTableData();
  }, [checked, buttonList]);
  const getTableData = async value => {
    setLoading(true);
    if (checked === 0) {
      const { code, data } = await getTrackListPage({
        ...parmas,
        type: '0',
        ...value,
      });
      if (code === 200) {
        setData(data.records);
        setColumns(columns0);
        setDataTotal(data.total);
        setLoading(false);
      }
    }
    if (checked === 1) {
      const { code, data } = await getTrackListPage({
        ...parmas,
        type: '1',
        ...value,
      });
      if (code === 200) {
        setData(data.records);
        setColumns(columns0);
        setDataTotal(data.total);
        setLoading(false);
      }
    }
    if (checked === 2) {
      const { code, data } = await getTrackListPage({
        ...parmas,
        type: '2',
        ...value,
      });
      if (code === 200) {
        setData(data.records);
        setColumns(columns2);
        setDataTotal(data.total);
        setLoading(false);
      }
    }
    if (checked === 3) {
      const { code, data } = await getTrackListPage({
        ...parmas,
        type: '3',
        ...value,
      });
      if (code === 200) {
        setData(data.records);
        setColumns(columns3);
        setDataTotal(data.total);
        setLoading(false);
      }
    }
  };
  /**
   * 搜索值变化
   */
  const handleSearchParams = porps => {
    const { value, key } = porps;
    setParmas({ ...parmas, [key]: value });
  };
  // 搜索
  const handleOnSearch = () => {
    getTableData({ ...parmas, page: 1 });
  };

  //改变每页条数
  const pageSizeChange = value => {
    setParmas({ ...parmas, limit: value });
    getTableData({ ...parmas });
  };
  //翻页
  const onChangePageNumber = (value, size) => {
    setParmas({ ...parmas, page: value, limit: size });
    getTableData({ ...parmas, page: value });
  };
  const { limit, page } = parmas;

  const [projectNameList, setProjectNameList] = useState([]);
  const handleSearch = value => {
    if (value) {
      fetch(value, data => setProjectNameList(data));
    }
  };
  let timeout;
  function fetch(value, callback) {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    function fake() {
      const getItemNameLists = async value => {
        const { code, data } = await getItemNameList({
          page: 1,
          limit: 10,
          name: value,
        });
        if (code === 200) {
          callback([{ name: value }, ...data]);
        }
      };
      getItemNameLists(value);
    }
    timeout = setTimeout(fake, 300);
  }
  //获取部门下拉
  const getDeptListInfos = async value => {
    const { code, data } = await getDeptListInfo(value);
    if (code && code === 200) {
      const addDeptList = [];
      data.forEach(item => {
        let repeat = false;
        addDeptList.forEach(value => {
          if (value.deptFullName === item.deptFullName) {
            repeat = true;
          }
        });
        if (!repeat) {
          item.deptFullName &&
            addDeptList.push({ label: item.deptFullName, value: item.id });
        }
      });
      setDeptList(addDeptList);
    }
  };

  const [processData, setProcessData] = useState();
  const [showProcedureModal, setShowProcedureModal] = useState(false);

  const showProcess = async item => {
    const { data, code } = await getItemTrial(item.id);
    if (code === 200) {
      const nodes = [];
      const edges = [];
      data[0].TrialInfo.forEach(item => {
        nodes.push({
          id: String(item.trial_id),
          label: String(item.name),
          status: String(item.is_status),
          trace: item.trace,
          update_time: item.update_time,
          username: item.username,
          ShUserName: item.ShUserName,
        });
      });
      data[1].TrialCom.forEach(item => {
        edges.push({
          source: String(item.trial_id),
          target: String(item.lower_id),
        });
      });
      setProcessData({ nodes: nodes, edges: edges });
      setShowProcedureModal(true);
    }
  };

  const deleteProject = async id => {
    const { data, code } = await delItemHis(id);
    if (code === 200) {
      message.success({
        content: '删除成功!',
        key: 'deleteProject',
        duration: 2,
      });
      setWorkBenchInfo({
        ...workBenchInfo,
        createSubmitNum: Number(workBenchInfo.createSubmitNum) - 1,
        createNum: Number(workBenchInfo.createNum) - 1,
      });
      console.log(
        workBenchInfo.createSubmitNum,
        'workBenchInfo.createSubmitNum',
      );
      handleOnSearch();
    }
  };

  const recallClick = async value => {
    const { data, code } = await upItemBack(value);
    if (code === 200) {
      message.success({
        content: '撤销成功!',
        key: 'recallClick',
        duration: 2,
      });
      handleOnSearch();
      setWorkBenchInfo({
        ...workBenchInfo,
        createTrNum: Number(workBenchInfo.createTrNum) - 1,
        createSubmitNum: Number(workBenchInfo.createSubmitNum) + 1,
      });
    }
  };
  return (
    <div className={styles.contentBox}>
      <div className={classNames(styles.card, styles.left_box)}>
        <div className={styles.title}>
          <UserOutlined style={{ marginRight: 8 }} />
          我的
        </div>
        <div>
          <span className={styles.bold} style={{ marginRight: 8 }}>
            {user && user.sysUser.employeeName}
          </span>
          <span className={styles.medium}>
            工号：{user && user.sysUser.employeeNumber}
          </span>
        </div>
        <div className={styles.userinfo_box}>
          <div>
            <span style={{ marginRight: 24 }}>
              部门：{user && user.deptName}
            </span>
            <span style={{ marginRight: 20 }}>岗位：-</span>
            <span style={{ marginRight: 20 }}>职责：-</span>
          </div>
          <div style={{ display: 'flex' }}>
            <Button
              type="primary"
              style={{ marginRight: 8 }}
              // disabled={!(buttonList.includes('/projectList/track') ||
              //   buttonList.includes('admin'))}
            >
              <Link to={'./createProject'}>
                <PlusOutlined style={{ marginRight: 5 }} />
                新建项目
              </Link>
            </Button>
            <Button>
              <Link to={'./projectList'}>
                <CodeSandboxOutlined style={{ marginRight: 5 }} />
                项目列表
              </Link>
            </Button>
          </div>
        </div>

        <div className={styles.project_box}>
          <div
            onClick={() => handeProjectBoxClick(0)}
            className={classNames(
              styles.project_item,
              checked === 0 && styles.checked_box,
            )}
          >
            <div>
              我管理的项目
              <Tooltip title={<>我是项目经理</>}>
                <QuestionCircleOutlined style={{ width: 20 }} />
              </Tooltip>
            </div>
            <div>{workBenchInfo.manageNum || 0}</div>
            <Progress
              percent={
                (Number(workBenchInfo.manageGoNum) /
                  Number(workBenchInfo.manageNum)) *
                  100 || 0
              }
              status="normal"
              showInfo={false}
              style={{ marginBottom: 5 }}
              trailColor={checked === 0 && 'rgba(255, 255, 255, 0.2)'}
              strokeColor={checked === 0 && '#FFFFFF'}
            />
            <div className={styles.progress_number}>
              <div>
                {checked === 0 ? (
                  <div
                    className={styles.circle}
                    style={{ backgroundColor: '#FFFFFF' }}
                  ></div>
                ) : (
                  <div
                    className={styles.circle}
                    style={{ backgroundColor: '#3d7bf8' }}
                  ></div>
                )}
                进行中：{workBenchInfo.manageGoNum || 0}
              </div>
              <div>已结项：{workBenchInfo.manageEndNum || 0}</div>
            </div>
          </div>
          <div
            className={classNames(
              styles.project_item,
              checked === 1 && styles.checked_box,
            )}
            onClick={() => handeProjectBoxClick(1)}
          >
            <div>
              我参与的项目
              <Tooltip title={<>我是协作人员或我是项目成员</>}>
                <QuestionCircleOutlined style={{ width: 20 }} />
              </Tooltip>
            </div>
            <div>{workBenchInfo.joinNum || 0}</div>
            <Progress
              percent={
                (Number(workBenchInfo.joinGoNum) /
                  Number(workBenchInfo.joinNum)) *
                  100 || 0
              }
              status="normal"
              showInfo={false}
              trailColor={checked === 1 && 'rgba(255, 255, 255, 0.2)'}
              strokeColor={checked === 1 && '#FFFFFF'}
              style={{ marginBottom: 5 }}
            />
            <div className={styles.progress_number}>
              <div>
                {checked === 1 ? (
                  <div
                    className={styles.circle}
                    style={{ backgroundColor: '#FFFFFF' }}
                  ></div>
                ) : (
                  <div
                    className={styles.circle}
                    style={{ backgroundColor: '#3d7bf8' }}
                  ></div>
                )}
                进行中：{workBenchInfo.joinGoNum || 0}
              </div>
              <div>已结项：{workBenchInfo.joinEndNum || 0}</div>
            </div>
          </div>
          <div
            className={classNames(
              styles.project_item,
              checked === 2 && styles.checked_box,
            )}
            onClick={() => handeProjectBoxClick(2)}
          >
            <div>我创建的项目</div>
            <div>{workBenchInfo.createNum || 0}</div>
            <Progress
              percent={
                (Number(workBenchInfo.createSubmitNum) /
                  Number(workBenchInfo.createNum)) *
                  100 || 0
              }
              status="normal"
              showInfo={false}
              trailColor={checked === 2 && 'rgba(255, 255, 255, 0.2)'}
              strokeColor={checked === 2 && '#FFFFFF'}
              style={{ marginBottom: 5 }}
            />
            <div className={styles.progress_number}>
              <div>
                {checked === 2 ? (
                  <div
                    className={styles.circle}
                    style={{ backgroundColor: '#FFFFFF' }}
                  ></div>
                ) : (
                  <div
                    className={styles.circle}
                    style={{ backgroundColor: '#3d7bf8' }}
                  ></div>
                )}
                待提交：{workBenchInfo.createSubmitNum || '0'}
              </div>
              <div>待审核：{workBenchInfo.createTrNum || '0'}</div>
            </div>
          </div>
          <div
            className={classNames(
              styles.project_item,
              checked === 3 && styles.checked_box,
            )}
            onClick={() => handeProjectBoxClick(3)}
          >
            <div>我审核的项目</div>
            <div>
              {Number(workBenchInfo.trialtrNum) +
                Number(workBenchInfo.trialStopNum) || 0}
            </div>
            <Progress
              percent={
                (Number(workBenchInfo.trialNum) /
                  (Number(workBenchInfo.trialtrNum) +
                    Number(workBenchInfo.trialStopNum))) *
                  100 || 0
              }
              status="normal"
              showInfo={false}
              trailColor={checked === 3 && 'rgba(255, 255, 255, 0.2)'}
              strokeColor={checked === 3 && '#FFFFFF'}
              style={{ marginBottom: 5 }}
            />
            <div className={styles.progress_number}>
              <div>
                {checked === 3 ? (
                  <div
                    className={styles.circle}
                    style={{ backgroundColor: '#FFFFFF' }}
                  ></div>
                ) : (
                  <div
                    className={styles.circle}
                    style={{ backgroundColor: '#3d7bf8' }}
                  ></div>
                )}
                待审核：{workBenchInfo.trialtrNum || 0}
              </div>
              <div>已审核：{workBenchInfo.trialStopNum || 0}</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>

      {/* <div className={classNames(styles.card, styles.rigth_box)}>
        <div className={styles.title}>
          <HistoryOutlined style={{ marginRight: 5 }} />
          项目跟踪
        </div>
        <div className={styles.work_box}>
          {workBenchInfo &&
            workBenchInfo.trackInfo &&
            workBenchInfo.trackInfo.length > 0 &&
            workBenchInfo.trackInfo.map((item, index) => {
              return (
                <div
                  className={styles.item}
                  onClick={() => {
                    linkToTrack(item);
                  }}
                >
                  <div>{item.createTime}</div>
                  <div>
                    {
                      <Tooltip placement="topLeft" title={item.name}>
                        {formatter(item.name, 4)}
                      </Tooltip>
                    }
                  </div>
                  <div>
                    {
                      <Tooltip placement="topLeft" title={item.content}>
                        {formatter(item.content, 5)}
                      </Tooltip>
                    }
                  </div>
                </div>
              );
            })}
        </div>
      </div> */}

      <div className={styles.card}>
        {(checked === 0 || checked === 1) && (
          <div className={styles.searchInput}>
            <div>
              <p>项目名称</p>
              <Select
                showSearch
                allowClear={true}
                defaultActiveFirstOption={false}
                placeholder="请输入"
                showArrow={false}
                style={{ width: '100%' }}
                className={styles.selects}
                onChange={value =>
                  handleSearchParams({ key: 'itemName', value })
                }
                filterOption={false}
                onSearch={handleSearch}
                notFoundContent={null}
              >
                {projectNameList &&
                  projectNameList.length > 0 &&
                  projectNameList.map((item, index) => (
                    <Option value={item.name} key={index}>
                      {item.name}
                    </Option>
                  ))}
              </Select>
            </div>
            <div>
              <p>项目编号</p>
              <Input
                onChange={e =>
                  handleSearchParams({
                    key: 'itemNum',
                    value: e.target.value,
                  })
                }
                placeholder="请输入"
              ></Input>
            </div>
            <div>
              <p>项目状态</p>
              <Select
                style={{ width: '100%' }}
                placeholder="请选择"
                allowClear={true}
                className={styles.selects}
                onChange={value =>
                  handleSearchParams({ key: 'item_stage', value })
                }
              >
                <Option value="running">进行中</Option>
                <Option value="end">已结项</Option>
              </Select>
            </div>
            <div>
              <p></p>
              <br />
              <Button type="primary" onClick={handleOnSearch}>
                查询
              </Button>
            </div>
          </div>
        )}
        {(checked === 2 || checked === 3) && (
          <div className={styles.searchInput2}>
            <div>
              <p>项目名称</p>
              <Input
                onChange={e =>
                  handleSearchParams({
                    value: e.target.value,
                    key: 'itemName',
                  })
                }
                placeholder="请输入"
              ></Input>
            </div>
            <div>
              <p>项目状态</p>
              {checked === 2 ? (
                <Select
                  style={{ width: '100%' }}
                  allowClear={true}
                  placeholder="请选择"
                  className={styles.selects}
                  onChange={value =>
                    handleSearchParams({ key: 'isType', value })
                  }
                >
                  <Option value="0">待提交</Option>
                  <Option value="1">审核中</Option>
                  <Option value="2">进行中</Option>
                  <Option value="3">已结项</Option>
                </Select>
              ) : (
                <Select
                  style={{ width: '100%' }}
                  placeholder="请选择"
                  className={styles.selects}
                  onChange={value =>
                    handleSearchParams({ key: 'isType', value })
                  }
                >
                  <Option value="4">待审核</Option>
                  <Option value="5">已审核</Option>
                </Select>
              )}
            </div>
            <div>
              <p></p>
              <br />
              <Button type="primary" onClick={handleOnSearch}>
                查询
              </Button>
            </div>
            <div></div>
          </div>
        )}
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          scroll={{ x: columns.length * 140 }}
          // rowSelection={rowSelection}
          loading={loading}
          size="middle"
          className={styles.anTdTable}
        />
        <div className={styles.splitPigination}>
          <div>
            <Select
              defaultValue="10"
              style={{ width: 150 }}
              className={styles.selects}
              onChange={pageSizeChange}
            >
              <Option value="10">显示结果：10条</Option>
              <Option value="20">显示结果：20条</Option>
              <Option value="50">显示结果：50条</Option>
            </Select>
            <span className={styles.total}>共{dataTotal}条</span>
          </div>
          <Pagination
            total={dataTotal || 0}
            pageSize={limit}
            showSizeChanger={false}
            current={page}
            key={67}
            onChange={onChangePageNumber}
          />
        </div>
      </div>

      <Modal
        // title="Basic Modal"
        visible={showProcedureModal}
        onOk={() => setShowProcedureModal(false)}
        onCancel={() => setShowProcedureModal(false)}
        width={1100}
      >
        <Procedure processData={processData} />
      </Modal>
    </div>
  );
};
