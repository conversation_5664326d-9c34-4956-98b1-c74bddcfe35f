import React, { useEffect, useState } from 'react';
import { HashRouter as Router, Link, Route } from 'react-router-dom';
import { Layout, ConfigProvider } from 'antd';
import cn from 'antd/es/locale/zh_CN';
import { getButtonObject } from '../services/user';
const { Header, Footer, Sider, Content } = Layout;
import './index.less';

import Sidebar from '../components/Sidebar/index.jsx';
import HeaderNav from '../components/HeaderNva';
export const MyContext = React.createContext();

const App = props => {
  useEffect(() => {
    getButton();
  }, []);
  const [buttonList, setButtonList] = useState([]);
  //获取费用类分类
  const getButton = async () => {
    const { data, code } = await getButtonObject();
    if (code === 200) {
      setButtonList(data);
    }
  };
  return (
    <MyContext.Provider value={buttonList}>
      <ConfigProvider locale={cn}>
        <Layout>
          <Header className="header">
            <HeaderNav />
          </Header>
          <Layout>
            <Sider width={256} className="sider">
              <Sidebar />
            </Sider>
            <Content id="content">{props.children}</Content>
          </Layout>
        </Layout>
      </ConfigProvider>
    </MyContext.Provider>
  );
};

export default App;
