#root {
  height: 100%;
  min-height: 600px;

  > section {
    height: 100%;
  }
}

.title {
  background: rgb(121, 242, 157);
}

.content {
  height: 100%;
  padding: 24px;
  background: #f7f8fa;
}

.header {
  background: #ffffff;
  /* 分割线/底部 */
  height: 56px;
  box-shadow: inset 0px -1px 0px #edeff2;
  padding: 0 24px;
}

.sider {
  z-index: 999;
}

.mt10 {
  margin-top: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.ml10 {
  margin-left: 10px;
}

.mr10 {
  margin-right: 10px;
}

.page404 {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30px;
}
