//预算
import React, { useEffect, useState } from 'react';
import style from './index.less';
import echarts from 'echarts';
import moment from 'moment';

const Level = props => {
  const { data } = props;
  const [totalCount, setTotalCount] = useState([]);
  const [total, setTotal] = useState([]);
  const [than, setThan] = useState([]);
  const [severData, setSaverData] = useState([]);
  const [xAxisData, setXAxisData] = useState([]);
  const [percentage, setPercentage] = useState([]);

  useEffect(() => {
    const dataList = [];
    const xAxisDataList = [];
    const totalCount = [];
    const total = [];
    const than = [];
    const percentage = [];
    data.forEach(item => {
      totalCount.push(item.totalCount);
      total.push(item.total);
      than.push(item.than);
      xAxisDataList.push(item.level_type);
      percentage.push(Number((item.than / item.total) * 100).toFixed(2));
    });
    setTotalCount(totalCount);
    setTotal(total);
    setThan(than);
    setSaverData(dataList);
    setXAxisData(xAxisDataList);
    setPercentage(percentage);
  }, [data]);

  useEffect(() => {
    if (data.length > 0) {
      const myChart = echarts.init(document.getElementById('Level'));
      myChart.resize({ height: 6 * 36 + 40 });
      const option = {
        tooltip: {
          formatter: (params, ticket, callback) => {
            let formatterHtml = '';
            params.map(item => {
              if (item.componentSubType !== 'line') {
                formatterHtml +=
                  item.componentIndex === 0
                    ? `<div style=';margin:2px 0 -12px 0;font-size:13px;font-weight:500'>${item.axisValue}</div><br/>${item.marker}${item.seriesName}: ${item.value}W <br/>`
                    : `${item.marker}${item.seriesName}: ${item.value}W <br/>`;
              } else {
                formatterHtml += `${item.marker}${item.seriesName}: ${item.value}% <br/>`;
              }
            });
            // const proportion = Number(
            //   (params[2] && params[2].value / params[0].value) ,
            // ).toFixed(2) + '%';
            // formatterHtml += `  已确认收入计划占总计划收入: ${proportion}`;
            return formatterHtml;
          },
          position: function(point, params, dom, rect, size) {
            return [point[0], point[1]];
          },
          trigger: 'axis',
          // backgroundColor: '#FFFFFF',
          confine: true,
          padding: 8,
          postion: 'bottom',
          // textStyle: {
          //   color: '#333333',
          //   fontSize: 11,
          // },
          // extraCssText:
          //   'box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.24);border-radius: 2px;',
        },
        color: ['#E8684A', '#F6BD16', '#3D7BF8'],
        grid: {
          left: 0,
          right: '1',
          top: 20,
          bottom: 40,
          containLabel: true,
        },
        axisLine: {
          lineStyle: {
            color: '#555555',
          },
        },
        legend: {
          data: ['计划收入', '当期累计计划收入', '已确定收入'],
          height: 20,
          itemGap: 12,
          itemWidth: 6,
          itemHeight: 6,
          icon: 'circle',
          textStyle: {
            color: 'rgba(85,85,85,1)',
          },
          bottom: 0,
        },
        // markLine: {
        //   silent: true,
        // },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisTick: { show: false },
          axisLine: { show: false },
          splitLine: {
            lineStyle: {
              color: '#EDEFF2',
            },
          },
        },
        yAxis: [
          {
            show: true,
            type: 'value',
            axisLine: { show: false },
          },
          {
            show: false,
            type: 'value',
            axisLine: { show: false },
          },
        ],

        series: [
          {
            name: '计划收入',
            type: 'bar',
            barWidth: 20,
            stack: '计划收入',
            label: {
              show: false,
              position: 'insideRight',
            },
            data: total,
            emphasis: {
              itemStyle: {
                color: '#E8684A',
              },
            },
          },
          {
            name: '当期累计计划收入',
            type: 'bar',
            barWidth: 20,
            stack: '当期累计计划收入',
            label: {
              show: false,
              position: 'insideRight',
            },
            data: totalCount,
            emphasis: {
              itemStyle: {
                color: '#E8684A',
              },
            },
          },
          {
            name: '已确定收入',
            type: 'bar',
            barWidth: 20,
            stack: '已确定收入',
            label: {
              show: false,
              position: 'insideRight',
            },
            data: than,
            emphasis: {
              itemStyle: {
                color: '#E8684A',
              },
            },
          },
          {
            name: '已确定收入占总计划收入',
            type: 'line',
            yAxisIndex: 1,
            data: percentage,
            smooth: true,
            itemStyle: {
              color: '#34B682',
            },
          },
        ],
      };
      myChart.setOption(option);
    }
  }, [severData]);

  return data.length > 0 ? (
    <div id="Level" className={style.budget} />
  ) : (
    <div className={style.noData}>暂无数据</div>
  );
};

export default Level;
