import React, { useEffect, useState } from 'react';
import style from './index.less';
import echarts from 'echarts';

const SectorRevenue = props => {
  const { sectorRevenue } = props;

  const [handleDeptList, setHandleDeptList] = useState([]);
  const [handleData, setHandleData] = useState({
    planIncome: [],
    planIncomeCount: [],
    incomePassNum: [],
  });
  useEffect(() => {
    const handleDeptList = [];
    const handleData = {
      planInComeData: [], //计划收入
      planIncomeCount: [], //累积收入
      incomePassNum: [], //已确定收入
    };
    sectorRevenue.map(item => {
      handleData.planInComeData.push(item.plan_income);
      handleData.planIncomeCount.push(item.plan_incomeCount);
      handleData.incomePassNum.push(item.incomePassNum);
      handleDeptList.push(item.dept);
    });
    setHandleDeptList(handleDeptList);
    setHandleData(handleData);
  }, [sectorRevenue]);
  useEffect(() => {
    const myChart = echarts.init(document.getElementById('sectorRevenue'));
    myChart.resize({ height: 300 });
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        padding: 8,
        // backgroundColor: 'rgba(255, 255, 255,1)',
        // textStyle: {
        //   color: '#333333',
        //   fontSize: 11,
        // },
        // extraCssText:'box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.24);border-radius: 2px;',
        formatter: (params, ticket, callback) => {
          let formatterHtml = '';
          params.map(item => {
            formatterHtml +=
              item.componentIndex === 0
                ? `<div style='margin:2px 0 -12px 0;font-size:13px;font-weight:500'>${item.axisValue}</div><br/>${item.marker}${item.seriesName}: ${item.value}W <br/>`
                : `${item.marker}${item.seriesName}: ${item.value} W<br/>`;
          });
          const proportion =
            params[2] &&
            `${
              Number((params[0].value / params[2].value) * 100).toFixed(2) ===
              'NaN'
                ? 0.0
                : Number((params[0].value / params[2].value) * 100).toFixed(2)
            }% `;
          formatterHtml += proportion
            ? `  已确认收入计划占总计划收入: ${proportion || ''}`
            : '';
          return formatterHtml;
        },
        position: function(point, params, dom, rect, size) {
          return [point[0], point[1]];
        },
      },
      color: ['#34B682', '#8FE8C4', '#D0FBE9'],
      legend: {
        data: ['已确定收入', '当期累计计划收入', '计划收入'],
        height: 20,
        itemGap: 12,
        itemWidth: 6,
        itemHeight: 6,
        icon: 'circle',
        textStyle: {
          color: 'rgba(85,85,85,1)',
        },
        bottom: 0,
      },
      grid: {
        left: 0,
        right: 20,
        top: 0,
        bottom: 40,
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        axisTick: { show: false },
        axisLine: { show: false },
        axisLabel: {
          formatter: value => {
            return `${value || 0}`;
          },
        },

        splitLine: {
          lineStyle: {
            color: '#EDEFF2',
          },
        },
      },
      yAxis: {
        type: 'category',
        axisTick: { show: false },
        axisLine: {
          lineStyle: {
            color: '#EDEFF2',
          },
        },
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontFamily: 'DINRegular',
          color: '#555555',
          formatter: function(value) {
            if (value === '四川分公司（筹）机场事业部') {
              return '四川分公司';
            }
            if (value === '四川分公司（筹）') {
              return '四川分公司';
            }
            if (value === '产品与解决方案中心（PSC）') {
              return 'PSC';
            }
            if (value.length > 5) {
              return value.substring(0, 5) + '...';
            } else {
              return value;
            }
          },
          //   formatter: function(val) {
          //     console.log('label汉字--', val);
          //     var strs = val.split(''); //字符串数组
          //     var str = '';
          //     for (var i = 0, s; (s = strs[i++]); ) {
          //       //遍历字符串数组
          //       str += s;
          //       if (!(i % 6)) str += '\n'; //按需要求余
          //     }
          //     return str;
          //   },
        },
        data: handleDeptList,
      },
      // dataZoom: [//给x轴设置滚动条
      //   {
      //     // start: 0,//默认为0
      //     // end: 100 - 1500 / 31,//默认为100
      //     type: 'slider',
      //     maxValueSpan: 14,//窗口的大小，显示数据的条数的
      //     show: true,
      //     yAxisIndex: [0],
      //     // handleSize: 0,//滑动条的 左右2个滑动条的大小
      //     height: '80%',//组件高度
      //     left: 650, //左边的距离
      //     right: 15,//右边的距离
      //     top: 50,//右边的距离
      //     borderColor: "rgba(43,48,67,.8)",
      //     fillerColor: '#33384b',
      //     backgroundColor: 'rgba(43,48,67,.8)',//两边未选中的滑动条区域的颜色
      //     showDataShadow: false,//是否显示数据阴影 默认auto
      //     showDetail: false,//即拖拽时候是否显示详细数值信息 默认true
      //     realtime: true, //是否实时更新
      //     filterMode: 'filter',

      //   },
      // ],
      barMaxWidth: 12,
      series: [
        {
          name: '已确定收入',
          // name: '计划收入',
          type: 'bar',
          stack: '总量',
          label: {
            show: false,
            position: 'insideRight',
          },
          // data: handleData.planInComeData,
          data: handleData.incomePassNum,

          // data: handleData.planInComeData,
          // emphasis: {
          //   itemStyle: {
          //     color: '#34B682',
          //   },
          // },
        },
        {
          name: '当期累计计划收入',
          type: 'bar',
          stack: '总量',
          label: {
            show: false,
            position: 'insideRight',
          },
          // emphasis: {
          //   itemStyle: {
          //     color: '#34B682',
          //   },
          // },
          data: handleData.planIncomeCount,
        },
        {
          name: '计划收入',
          // name: '已确定收入',
          type: 'bar',
          stack: '总量',
          label: {
            show: false,
            position: 'insideRight',
          },
          // data: handleData.incomePassNum,
          data: handleData.planInComeData,

          // emphasis: {
          //   itemStyle: {
          //     color: '#34B682',
          //   },
          // },
        },
      ],
    };
    myChart.setOption(option);
  });

  return <div id="sectorRevenue" className={style.sectorRevenue} />;
};

export default SectorRevenue;
