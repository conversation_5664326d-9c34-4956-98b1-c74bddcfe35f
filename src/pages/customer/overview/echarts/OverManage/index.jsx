import React, { useEffect } from 'react';
import style from './index.less';
import echarts from 'echarts';

const Index = props => {
  const { data } = props;
  const { value1, value2 } = data;
  useEffect(() => {
    const myChart = echarts.init(document.getElementById('OverManage'));
    const option = {
      tooltip: {
        show: false,
        trigger: 'none',
      },
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 2,
      },
      color: ['#3D7BF8', '#34B682'],
      series: [
        {
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['60%', '85%'],
          label: {
            show: false,
            position: 'center',
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: value1, name: '经营类' },
            { value: value2, name: '非经营类' },
          ],
        },
      ],
    };
    myChart.setOption(option);
  }, [data]);

  return <div id={'OverManage'} className={style.statisticsEcharts} />;
};

export default Index;
