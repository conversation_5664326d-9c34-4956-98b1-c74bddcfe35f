import React, { useEffect, useState } from 'react';
import style from './index.less';
import echarts from 'echarts';
import { connect } from 'dva';

const ProjectsNumber = props => {
  const { deptList } = props;
  const data = [];
  useEffect(() => {
    deptList.map(item => {
      data.push({
        name: `${item.type_name}`,
        value: `${item.ratio}`,
        label: {
          show: false,
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 2,
        },
        tooltip: {
          formatter: `<div style='font-size:13px;font-weight:500'>${
            item.type_name
          }</div><div style='margin:2px 0 -14px 0;font-size:13px;font-weight:500'>项目总数:${
            item.counts
          }项</div><br/>系统连接及运维服务: ${item.系统连接及运维服务 ||
            0}项 <br/>系统集成服务:${item.系统集成服务 ||
            0}项 <br/>产品和解决方案服务:${item.产品和解决方案服务 ||
            0}项<br/>软件外包开发服务:${item.软件外包开发服务 || 0}项`,
        },
      });
    });
    const myChart = echarts.init(document.getElementById('projectsNumber'));
    const option = {
      color: ['#34B682', '#F6BD16', '#E8684A', '#3D7BF8'],
      tooltip: {
        trigger: 'item',
        padding: 8,
        // backgroundColor: 'rgba(255, 255, 255,1)',
        // textStyle: {
        //   color: '#666666',
        //   fontSize: 11,
        // },
        // extraCssText:'box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.24);border-radius: 2px;',
        confine: true,
        position: function(point, params, dom, rect, size) {
          return [point[0], point[1]];
        },
      },
      grid: {
        left: 0,
        right: '1',
        top: 0,
        bottom: 40,
        containLabel: true,
      },
      label: {
        fontFamily: 'PingFang SC',
        fontSize: 10,
      },
      series: [
        {
          type: 'pie',
          radius: ['60%', '85%'],
          minAngle: 30,
          margin: 10,
          clockwise: false,
          left: 0,
          data: data,
          label: {
            show: false,
          },
        },
      ],
    };
    myChart.setOption(option);
  }, [deptList]);

  return <div id="projectsNumber" className={style.projects} />;
};

export default ProjectsNumber;
