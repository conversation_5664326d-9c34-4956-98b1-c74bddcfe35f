import React, { useEffect } from 'react';
import style from './index.less';
import echarts from 'echarts';

const Index = props => {
  const { data } = props;
  const {
    runningAndManageAndNormalProjectNum = 0,
    runningAndManageAndRiskEarlyWarningProjectNum = 0,
    runningAndManageAndHighRiskProjectNum = 0,
  } = data;
  useEffect(() => {
    const myChart = echarts.init(document.getElementById('statisticsEcharts'));
    const option = {
      tooltip: {
        show: false,
        trigger: 'none',
      },
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 2,
      },
      color: ['#3D7BF8', '#F6BD16', '#E8684A'],
      series: [
        {
          name: '访问来源',
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['60%', '85%'],
          label: {
            show: false,
            position: 'center',
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: runningAndManageAndNormalProjectNum, name: '进度正常' },
            {
              value: runningAndManageAndRiskEarlyWarningProjectNum,
              name: '风险预警',
            },
            { value: runningAndManageAndHighRiskProjectNum, name: '高风险' },
          ],
        },
      ],
    };
    myChart.setOption(option);
  }, [data]);

  return <div id="statisticsEcharts" className={style.statisticsEcharts} />;
};

export default Index;
