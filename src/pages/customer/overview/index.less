.customer {
  padding: 10px 20px;

  .customer_card {
    width: 100%;
    height: 90px;
    border-radius: 12px;
    overflow: hidden;
    margin: 30px 0;
    .customer_card_left {
      float: left;
      width: 230px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: white;

      .leftIcons {
        width: 40%;
        height: 80%;
        border-right: 1px solid rgb(230, 229, 229);
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .leftTitles {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 60%;
        flex-wrap: wrap;
        p {
          width: 100%;
          text-align: center;
          margin: 0;
          padding: 0;
        }
        .leftTitlesText {
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
    .customer_card_right {
      position: relative;
      width: calc(~'100%-260px');
      height: 100%;
      display: flex;
      justify-content: flex-start;
      .customer_card_main {
        width: 100%;
        overflow: hidden;
        .customer_items {
          float: left;
          width: 25%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: white;
          flex-wrap: wrap;
          .customer_items_main {
            width: 100%;
            height: auto;
            padding: 10px 0;
            p {
              width: 100%;
              margin: 0;
              padding: 0;
              text-align: center;
            }
            .leftTitlesText {
              font-size: 16px;
              font-weight: 600;
            }
          }
        }
        .customer_items:not(:last-child) {
          .customer_items_main {
            border-right: 1px solid #e3dede;
          }
        }
        .customer_items:nth-child(n + 4) {
          .customer_items_main {
            border-right: 1px solid white;
          }
        }
      }
      .customer_card_icons {
        position: absolute;
        left: 0;
        right: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        svg:hover {
          cursor: pointer;
        }
      }
    }
  }
}
