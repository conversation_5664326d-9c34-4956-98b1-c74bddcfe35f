import React, { useState, useEffect } from 'react';
import { Select, Radio } from 'antd';
import { projectFinancialNumByDept } from './service';
import { Link } from 'umi';
import styles from './index.less';

export default () => {
  const [flush, setFlush] = useState(1);
  const [titleListA, setTitleListA] = useState([
    {
      id: 1,
      text: '航司客户1',
      number: 29,
    },
    {
      id: 2,
      text: '航司客户2',
      number: 58,
    },
    {
      id: 3,
      text: '航司客户3',
      number: 87,
    },
    {
      id: 4,
      text: '航司客户4',
      number: 43,
    },
    {
      id: 5,
      text: '航司客户5',
      number: 29,
    },
  ]);

  const changeList = value => {
    let { type, direction } = value;
    if (direction == 'left') {
      if (type == 'titleListA') {
        let newItem = changeItems(titleListA, 'left');
        setTitleListA(newItem);
        setFlush(flush + 1);
      }
    } else {
      if (type == 'titleListA') {
        let newItem = changeItems(titleListA, 'right');
        setTitleListA(newItem);
        setFlush(flush + 1);
      }
    }
  };

  const changeItems = (item, type) => {
    if (type == 'left') {
      item.push(item[0]);
      item.splice(0, 1);
    } else {
      item.unshift(item[item.length - 1]);
      item.splice(item.length - 1, 1);
    }
    return item;
  };

  return (
    <div className={styles.customer}>
      <div className={styles.customer_card}>
        <div
          className={styles.customer_card_left}
          style={{ background: '#6bb5d9' }}
        >
          <div className={styles.leftIcons}>
            <svg
              t="1638171718334"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="11792"
              width="28"
              height="28"
            >
              <path
                d="M231.849057 0h96.603773v96.603774h-96.603773zM347.773585 0h96.603773v96.603774h-96.603773zM463.698113 0h96.603774v96.603774h-96.603774zM579.622642 0h96.603773v96.603774h-96.603773zM231.849057 115.924528h96.603773v96.603774h-96.603773zM0 0h96.603774v96.603774H0zM115.924528 0h96.603774v96.603774H115.924528zM0 115.924528h96.603774v96.603774H0zM0 809.539623h96.603774v96.603773H0zM927.396226 809.539623h96.603774v96.603773h-96.603774zM927.396226 115.924528h96.603774v96.603774h-96.603774zM811.471698 0h96.603774v96.603774h-96.603774zM927.396226 925.464151h96.603774v96.603774h-96.603774zM811.471698 925.464151h96.603774v96.603774h-96.603774zM115.924528 115.924528h96.603774v96.603774H115.924528zM347.773585 115.924528h96.603773v96.603774h-96.603773zM463.698113 115.924528h96.603774v96.603774h-96.603774zM579.622642 115.924528h96.603773v96.603774h-96.603773zM231.849057 231.849057h96.603773v96.603773h-96.603773zM347.773585 231.849057h96.603773v96.603773h-96.603773zM463.698113 231.849057h96.603774v96.603773h-96.603774zM579.622642 231.849057h96.603773v96.603773h-96.603773zM231.849057 347.773585h96.603773v96.603773h-96.603773zM347.773585 347.773585h96.603773v96.603773h-96.603773zM463.698113 347.773585h96.603774v96.603773h-96.603774zM579.622642 347.773585h96.603773v96.603773h-96.603773zM231.849057 463.698113h96.603773v96.603774h-96.603773zM347.773585 463.698113h96.603773v96.603774h-96.603773zM463.698113 463.698113h96.603774v96.603774h-96.603774zM579.622642 463.698113h96.603773v96.603774h-96.603773zM231.849057 579.622642h96.603773v96.603773h-96.603773zM347.773585 579.622642h96.603773v96.603773h-96.603773zM463.698113 579.622642h96.603774v96.603773h-96.603774zM579.622642 579.622642h96.603773v96.603773h-96.603773zM811.471698 115.924528h96.603774v96.603774h-96.603774zM231.849057 927.396226h96.603773v96.603774h-96.603773zM115.924528 927.396226h96.603774v96.603774H115.924528zM347.773585 927.396226h96.603773v96.603774h-96.603773zM463.698113 927.396226h96.603774v96.603774h-96.603774zM579.622642 927.396226h96.603773v96.603774h-96.603773zM695.54717 927.396226h96.603773v96.603774h-96.603773zM231.849057 811.471698h96.603773v96.603774h-96.603773zM115.924528 811.471698h96.603774v96.603774H115.924528zM347.773585 811.471698h96.603773v96.603774h-96.603773zM463.698113 811.471698h96.603774v96.603774h-96.603774zM579.622642 811.471698h96.603773v96.603774h-96.603773zM695.54717 811.471698h96.603773v96.603774h-96.603773zM811.471698 811.471698h96.603774v96.603774h-96.603774zM0 676.226415v-96.603773h96.603774v96.603773zM0 560.301887v-96.603774h96.603774v96.603774zM0 444.377358v-96.603773h96.603774v96.603773zM0 328.45283v-96.603773h96.603774v96.603773zM115.924528 676.226415v-96.603773h96.603774v96.603773zM115.924528 560.301887v-96.603774h96.603774v96.603774zM115.924528 444.377358v-96.603773h96.603774v96.603773zM115.924528 328.45283v-96.603773h96.603774v96.603773zM927.396226 792.150943v-96.603773h96.603774v96.603773zM927.396226 676.226415v-96.603773h96.603774v96.603773zM927.396226 560.301887v-96.603774h96.603774v96.603774zM927.396226 444.377358v-96.603773h96.603774v96.603773zM927.396226 328.45283v-96.603773h96.603774v96.603773zM811.471698 792.150943v-96.603773h96.603774v96.603773zM811.471698 676.226415v-96.603773h96.603774v96.603773zM811.471698 560.301887v-96.603774h96.603774v96.603774zM811.471698 444.377358v-96.603773h96.603774v96.603773zM811.471698 328.45283v-96.603773h96.603774v96.603773z"
                fill="#ffffff"
                p-id="11793"
              ></path>
            </svg>
          </div>
          <div className={styles.leftTitles}>
            <p className={styles.leftTitlesText}>航司客户</p>
            <p>68</p>
          </div>
        </div>
        <div className={styles.customer_card_right}>
          {titleListA.length > 4 && (
            <div className={styles.customer_card_icons}>
              <svg
                onClick={() =>
                  changeList({ type: 'titleListA', direction: 'left' })
                }
                t="1638162553398"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="8090"
                width="28"
                height="28"
              >
                <path
                  d="M452.266667 507.733333l-29.866667 29.866667 29.866667 29.866667 115.2 115.2 29.866666-29.866667-115.2-115.2L597.333333 422.4l-29.866666-29.866667-115.2 115.2z m81.066666 388.266667c200.533333 0 362.666667-162.133333 362.666667-362.666667S733.866667 170.666667 533.333333 170.666667 170.666667 332.8 170.666667 533.333333 332.8 896 533.333333 896z m0-42.666667C358.4 853.333333 213.333333 708.266667 213.333333 533.333333S358.4 213.333333 533.333333 213.333333 853.333333 358.4 853.333333 533.333333 708.266667 853.333333 533.333333 853.333333z"
                  fill="#bfbfbf"
                  p-id="8091"
                ></path>
              </svg>
              <svg
                onClick={() =>
                  changeList({ type: 'titleListA', direction: 'right' })
                }
                t="1638162608099"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="9100"
                width="30"
                height="30"
              >
                <path
                  d="M614.4 507.733333l29.866667 29.866667-29.866667 29.866667-115.2 115.2-29.866667-34.133334 115.2-115.2L469.333333 422.4l29.866667-29.866667 115.2 115.2zM533.333333 896C332.8 896 170.666667 733.866667 170.666667 533.333333S332.8 170.666667 533.333333 170.666667 896 332.8 896 533.333333 733.866667 896 533.333333 896z m0-42.666667c174.933333 0 320-145.066667 320-320S708.266667 213.333333 533.333333 213.333333 213.333333 358.4 213.333333 533.333333 358.4 853.333333 533.333333 853.333333z"
                  fill="#bfbfbf"
                  p-id="9101"
                ></path>
              </svg>
            </div>
          )}

          <div className={styles.customer_card_main}>
            {titleListA.map(item => {
              return (
                <div key={item.id} className={styles.customer_items}>
                  <div className={styles.customer_items_main}>
                    <p className={styles.leftTitlesText}>{item.text}</p>
                    <p>{item.number}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <div className={styles.customer_card}>
        <div
          className={styles.customer_card_left}
          style={{ background: 'rgb(99 205 107)' }}
        >
          <div className={styles.leftIcons}>
            <svg
              t="1638149670605"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="6125"
              width="32"
              height="32"
            >
              <path
                d="M512 608.3079436L801.06525215 319.24269145s48.1539718-48.1539718 0-96.3079436-96.3079436 0-96.3079436 0l-168.64496731 168.64496731L174.71006537 319.24269145l-48.15397179 48.1539718 289.06525214 144.53262607-134.91597385 134.91597385-130.10764773-14.42497833-48.1539718 48.15397179L271.08871965 752.91128035l72.26631304 168.57425664 48.1539718-48.1539718-14.42497834-130.10764774L512 608.3079436z"
                p-id="6126"
                fill="#ffffff"
              ></path>
              <path
                d="M704.75730855 849.28993463L639.34993129 522.53589104l-113.49063838 113.49063838 130.74404384 261.417377z"
                p-id="6127"
                fill="#ffffff"
              ></path>
            </svg>
          </div>
          <div className={styles.leftTitles}>
            <p className={styles.leftTitlesText}>机场客户</p>
            <p>68</p>
          </div>
        </div>
        <div className={styles.customer_card_right}>
          {titleListA.length > 4 && (
            <div className={styles.customer_card_icons}>
              <svg
                onClick={() =>
                  changeList({ type: 'titleListA', direction: 'left' })
                }
                t="1638162553398"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="8090"
                width="28"
                height="28"
              >
                <path
                  d="M452.266667 507.733333l-29.866667 29.866667 29.866667 29.866667 115.2 115.2 29.866666-29.866667-115.2-115.2L597.333333 422.4l-29.866666-29.866667-115.2 115.2z m81.066666 388.266667c200.533333 0 362.666667-162.133333 362.666667-362.666667S733.866667 170.666667 533.333333 170.666667 170.666667 332.8 170.666667 533.333333 332.8 896 533.333333 896z m0-42.666667C358.4 853.333333 213.333333 708.266667 213.333333 533.333333S358.4 213.333333 533.333333 213.333333 853.333333 358.4 853.333333 533.333333 708.266667 853.333333 533.333333 853.333333z"
                  fill="#bfbfbf"
                  p-id="8091"
                ></path>
              </svg>
              <svg
                onClick={() =>
                  changeList({ type: 'titleListA', direction: 'right' })
                }
                t="1638162608099"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="9100"
                width="30"
                height="30"
              >
                <path
                  d="M614.4 507.733333l29.866667 29.866667-29.866667 29.866667-115.2 115.2-29.866667-34.133334 115.2-115.2L469.333333 422.4l29.866667-29.866667 115.2 115.2zM533.333333 896C332.8 896 170.666667 733.866667 170.666667 533.333333S332.8 170.666667 533.333333 170.666667 896 332.8 896 533.333333 733.866667 896 533.333333 896z m0-42.666667c174.933333 0 320-145.066667 320-320S708.266667 213.333333 533.333333 213.333333 213.333333 358.4 213.333333 533.333333 358.4 853.333333 533.333333 853.333333z"
                  fill="#bfbfbf"
                  p-id="9101"
                ></path>
              </svg>
            </div>
          )}

          <div className={styles.customer_card_main}>
            {titleListA.map(item => {
              return (
                <div key={item.id} className={styles.customer_items}>
                  <div className={styles.customer_items_main}>
                    <p className={styles.leftTitlesText}>{item.text}</p>
                    <p>{item.number}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      <div className={styles.customer_card}>
        <div
          className={styles.customer_card_left}
          style={{ background: 'rgb(217 112 107)' }}
        >
          <div className={styles.leftIcons}>
            <svg
              t="1638149670605"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="6125"
              width="32"
              height="32"
            >
              <path
                d="M512 608.3079436L801.06525215 319.24269145s48.1539718-48.1539718 0-96.3079436-96.3079436 0-96.3079436 0l-168.64496731 168.64496731L174.71006537 319.24269145l-48.15397179 48.1539718 289.06525214 144.53262607-134.91597385 134.91597385-130.10764773-14.42497833-48.1539718 48.15397179L271.08871965 752.91128035l72.26631304 168.57425664 48.1539718-48.1539718-14.42497834-130.10764774L512 608.3079436z"
                p-id="6126"
                fill="#ffffff"
              ></path>
              <path
                d="M704.75730855 849.28993463L639.34993129 522.53589104l-113.49063838 113.49063838 130.74404384 261.417377z"
                p-id="6127"
                fill="#ffffff"
              ></path>
            </svg>
          </div>
          <div className={styles.leftTitles}>
            <p className={styles.leftTitlesText}>渠道客户</p>
            <p>68</p>
          </div>
        </div>
        <div className={styles.customer_card_right}>
          {titleListA.length > 4 && (
            <div className={styles.customer_card_icons}>
              <svg
                onClick={() =>
                  changeList({ type: 'titleListA', direction: 'left' })
                }
                t="1638162553398"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="8090"
                width="28"
                height="28"
              >
                <path
                  d="M452.266667 507.733333l-29.866667 29.866667 29.866667 29.866667 115.2 115.2 29.866666-29.866667-115.2-115.2L597.333333 422.4l-29.866666-29.866667-115.2 115.2z m81.066666 388.266667c200.533333 0 362.666667-162.133333 362.666667-362.666667S733.866667 170.666667 533.333333 170.666667 170.666667 332.8 170.666667 533.333333 332.8 896 533.333333 896z m0-42.666667C358.4 853.333333 213.333333 708.266667 213.333333 533.333333S358.4 213.333333 533.333333 213.333333 853.333333 358.4 853.333333 533.333333 708.266667 853.333333 533.333333 853.333333z"
                  fill="#bfbfbf"
                  p-id="8091"
                ></path>
              </svg>
              <svg
                onClick={() =>
                  changeList({ type: 'titleListA', direction: 'right' })
                }
                t="1638162608099"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="9100"
                width="30"
                height="30"
              >
                <path
                  d="M614.4 507.733333l29.866667 29.866667-29.866667 29.866667-115.2 115.2-29.866667-34.133334 115.2-115.2L469.333333 422.4l29.866667-29.866667 115.2 115.2zM533.333333 896C332.8 896 170.666667 733.866667 170.666667 533.333333S332.8 170.666667 533.333333 170.666667 896 332.8 896 533.333333 733.866667 896 533.333333 896z m0-42.666667c174.933333 0 320-145.066667 320-320S708.266667 213.333333 533.333333 213.333333 213.333333 358.4 213.333333 533.333333 358.4 853.333333 533.333333 853.333333z"
                  fill="#bfbfbf"
                  p-id="9101"
                ></path>
              </svg>
            </div>
          )}
          <div className={styles.customer_card_main}>
            {titleListA.map(item => {
              return (
                <div key={item.id} className={styles.customer_items}>
                  <div className={styles.customer_items_main}>
                    <p className={styles.leftTitlesText}>{item.text}</p>
                    <p>{item.number}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <div className={styles.customer_card}>
        <div
          className={styles.customer_card_left}
          style={{ background: 'rgb(209 159 86)' }}
        >
          <div className={styles.leftIcons}>
            <svg
              t="1638149670605"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="6125"
              width="32"
              height="32"
            >
              <path
                d="M512 608.3079436L801.06525215 319.24269145s48.1539718-48.1539718 0-96.3079436-96.3079436 0-96.3079436 0l-168.64496731 168.64496731L174.71006537 319.24269145l-48.15397179 48.1539718 289.06525214 144.53262607-134.91597385 134.91597385-130.10764773-14.42497833-48.1539718 48.15397179L271.08871965 752.91128035l72.26631304 168.57425664 48.1539718-48.1539718-14.42497834-130.10764774L512 608.3079436z"
                p-id="6126"
                fill="#ffffff"
              ></path>
              <path
                d="M704.75730855 849.28993463L639.34993129 522.53589104l-113.49063838 113.49063838 130.74404384 261.417377z"
                p-id="6127"
                fill="#ffffff"
              ></path>
            </svg>
          </div>
          <div className={styles.leftTitles}>
            <p className={styles.leftTitlesText}>通航客户</p>
            <p>68</p>
          </div>
        </div>
        <div className={styles.customer_card_right}>
          {titleListA.length > 4 && (
            <div className={styles.customer_card_icons}>
              <svg
                onClick={() =>
                  changeList({ type: 'titleListA', direction: 'left' })
                }
                t="1638162553398"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="8090"
                width="28"
                height="28"
              >
                <path
                  d="M452.266667 507.733333l-29.866667 29.866667 29.866667 29.866667 115.2 115.2 29.866666-29.866667-115.2-115.2L597.333333 422.4l-29.866666-29.866667-115.2 115.2z m81.066666 388.266667c200.533333 0 362.666667-162.133333 362.666667-362.666667S733.866667 170.666667 533.333333 170.666667 170.666667 332.8 170.666667 533.333333 332.8 896 533.333333 896z m0-42.666667C358.4 853.333333 213.333333 708.266667 213.333333 533.333333S358.4 213.333333 533.333333 213.333333 853.333333 358.4 853.333333 533.333333 708.266667 853.333333 533.333333 853.333333z"
                  fill="#bfbfbf"
                  p-id="8091"
                ></path>
              </svg>
              <svg
                onClick={() =>
                  changeList({ type: 'titleListA', direction: 'right' })
                }
                t="1638162608099"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="9100"
                width="30"
                height="30"
              >
                <path
                  d="M614.4 507.733333l29.866667 29.866667-29.866667 29.866667-115.2 115.2-29.866667-34.133334 115.2-115.2L469.333333 422.4l29.866667-29.866667 115.2 115.2zM533.333333 896C332.8 896 170.666667 733.866667 170.666667 533.333333S332.8 170.666667 533.333333 170.666667 896 332.8 896 533.333333 733.866667 896 533.333333 896z m0-42.666667c174.933333 0 320-145.066667 320-320S708.266667 213.333333 533.333333 213.333333 213.333333 358.4 213.333333 533.333333 358.4 853.333333 533.333333 853.333333z"
                  fill="#bfbfbf"
                  p-id="9101"
                ></path>
              </svg>
            </div>
          )}
          <div className={styles.customer_card_main}>
            {titleListA.map(item => {
              return (
                <div key={item.id} className={styles.customer_items}>
                  <div className={styles.customer_items_main}>
                    <p className={styles.leftTitlesText}>{item.text}</p>
                    <p>{item.number}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
