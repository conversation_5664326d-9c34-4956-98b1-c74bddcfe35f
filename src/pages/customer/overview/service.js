import request from '@/utils/request';
import { BASE_URl } from '../../../utils/constant';

/**
 * 1.项目营收-营收把握度（项目数量）
 */
export function projectFinancialNumByDept(params) {
  return request(`${BASE_URl}/statisticsFinancial/getIncomeMasteryNum`);
}
/**
 * 2.项目营收-营收把握度（年度计划收入总和）
 */
export function getIncomeMasterySum(params) {
  return request(`${BASE_URl}/statisticsFinancial/getIncomeMasterySum`);
}
/**
 * 3.项目营收-收入统计（饼状图）
 */
export function selectIncomeStatistics(params) {
  return request(`${BASE_URl}/item/selectIncomeStatistics?${params}`);
}
/**
 * 4.项目营收-收入分类
 */
export function projectFinancialNum(params) {
  return request(`${BASE_URl}/statisticsFinancial/getIncomeBranch?${params}`);
}
/**
 * 5.项目营收-部门收入
 */
export function getDeptIncome(params) {
  return request(`${BASE_URl}/statisticsFinancial/getDeptIncome`);
}
/**
 * 项目进度
 * */
export function selectProjectNumsByStage() {
  return request(`${BASE_URl}/statistics/selectProjectschedule`);
}
/**
 * 7. 项目统计，已结项项目查询
 */
export function selectArchiveProjects(params) {
  const paramsUrl = params.stageName ? `?stageName=${params.stageName}` : '';
  return request(`${BASE_URl}/statistics/selectArchiveProjects${paramsUrl}`);
}

/**
 * 10.项目进度二级页面，部门进度页
 * */
export function selectAllDeptProjectStage() {
  return request(`${BASE_URl}/item/selectAllDeptProjectStage`);
}

/**
 * 11.项目进度三级页面，当前部门进度详情
 * */
export function selectDeptProjectsSchedule(params) {
  return request(
    `${BASE_URl}/item/selectDeptProjectsSchedule?dept=${params.dept}&type=${params.type}`,
  );
}

/**
 * 12.项目进度三级页面，当前部门进度详情
 * */
export function selectDeptProjectsList(params) {
  return request(
    `${BASE_URl}/item/selectDeptProjectsList?dept=${params.dept}&type=${params.type}&stage=${params.stage}`,
  );
}
// 获取项目相关统计数据
export function projectSpeedNum() {
  return request(`${BASE_URl}/statistics/projectSpeedNum`);
}

/**
 * 8.项目营收-部门项目营收
 */
export function getDeptRevenue() {
  return request(`${BASE_URl}/statisticsFinancial/getDeptRevenue`);
}
/**
 * 项目营收-部门项目营收详情（经营类、非经营类）
 */
export function getDeptRevenueInfo(params) {
  return request(
    `${BASE_URl}/statisticsFinancial/getDeptRevenueInfo?dept=${params.dept}`,
  );
}

// 按部门统计项目进度
export function projectSpeedNumByDepartment() {
  return request(`${BASE_URl}/statistics/projectSpeedNumByDepartment`);
}

// 获取当前部门所有项目进度
export function selectDeptProjectsStage(params) {
  return request(
    `${BASE_URl}/item/selectDeptProjectsStage?dept=${params.dept}`,
  );
}

/**
 * 获取项目详情
 */
// export  function projectInfo(params) {
//   return request(`${BASE_URl}/item/projectInfo?itemNum=${params.itemNum}`);
// }

export function projectInfo(params) {
  return request(
    `${BASE_URl}/item/selectProjectBasic?itemNum=${params.itemNum}`,
  );
}

// 获取项目财务数据
export function financeInfo(params) {
  return request(
    `${BASE_URl}/item/selectFinanceBasic?itemNum=${params.itemNum}`,
  );
}

// 项目详情  项目把握度趋势分析
export function graspInfo(params) {
  return request(`${BASE_URl}/item/selectItemGrasp?itemNum=${params.itemNum}`);
}

// 项目详情  项目状况趋势
export function stateInfo(params) {
  return request(`${BASE_URl}/item/selectItemState?itemNum=${params.itemNum}`);
}

// 项目详情  项目关键里程碑
export function milestoneInfo(params) {
  return request(`${BASE_URl}/item/t_item_milestone?itemNum=${params.itemNum}`);
}

// 项目详情  项目风险解决
export function riskAndSolveInfo(params) {
  return request(
    `${BASE_URl}/statisticsFinancial/getProjectRiskAndSolve?item_num=${params.itemNum}`,
  );
}

// 项目详情 项目近况-项目周报
export function reportInfo(params) {
  return request(
    `${BASE_URl}/statisticsFinancial/getProjectReport?item_num=${params.itemNum}`,
  );
}

// 项目详情 项目预算与成本
export function budgetAndCostInfo(params) {
  return request(
    `${BASE_URl}/statisticsFinancial/getProjectBudgetAndCost?item_num=${params.itemNum}`,
  );
}

// 获取部门项目详情
export function revenueProjectFinancialNum(params) {
  return request(
    `${BASE_URl}/statisticsFinancial/projectFinancialNum?dept=${params.dept}`,
  );
}

export function selectDeptOutComeDetails(params) {
  return request(
    `${BASE_URl}/statisticsFinancial/selectDeptOutComeDetails?${params}`,
  );
}
