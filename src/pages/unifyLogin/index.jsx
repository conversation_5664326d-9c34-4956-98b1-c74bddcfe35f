import React, { useEffect, useState } from 'react';
import styles from './index.less';
import logo from '@/assets/img/logo.png';
import { Form, Input, Button, notification } from 'antd';
import { UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons';
import { randomLenNum, encryption } from '@/utils/utils';
import { queryUserLogin } from '../../services/login';
import { BASE_URl } from "../../utils/constant";

const AppLogin = props => {
  const [codeurl, setCodeurl] = useState(undefined);
  const [randomStr, setRandomStr] = useState(randomLenNum(4, true));

  useEffect(() => {
    refreshCode(undefined);
  }, []);

  const refreshCode = e => {
    if (e) {
      e.preventDefault(); // 修复 Android 上点击穿透
      e.stopPropagation();
    }
    const code = randomLenNum(4, true);
    setRandomStr(code);
    setCodeurl(`${BASE_URl}/code/${code}`);
  };

  const onFinish = async values => {
    const userInfo = {
      ...values,
      // randomStr,
      grant_type: 'password',
      scope: 'server',
      randomStr: randomStr,
    };
    //加密处理
    let key = '1234567887654321';
    const user = encryption({
      data: userInfo,
      key,
      param: ['password'],
    });
    const response = await queryUserLogin(user);
    console.log('loginfanhui', response);
    if (response && response.userId && response.access_token) {
      localStorage.clear();
      localStorage.setItem('userInfo', JSON.stringify(response));
      localStorage.setItem('updateTime', new Date().getTime());
      window.location.replace('/index');
    } else {
      if (response.code == 500) {
        const width = document.body.clientWidth;
        notification.error({
          description: '登录失败',
          message: response.msg ? response.msg : '账号密码错误!!!',
          style: {
            width: 400,
            marginRight: width / 2 - 200,
          },
        });
      }
    }
  };

  const onFinishFailed = errorInfo => {
    console.log('Failed:', errorInfo);
  };

  return (
    <div className={styles.unifyLogin}>
      <div className={styles.unifyLogin_top}>
        <div className={styles.unifyLogin_top_img}>
          <img src={logo}></img>
        </div>
      </div>
      <div className={styles.unifyLogin_mian}>
        <div className={styles.unifyLogin_loginForm}>
          <div className={styles.unifyLogin_loginFormMain}>
            <div className={styles.loginTitle}>市场经营管理平台</div>
            <div className={styles.formWrap}>
              <Form
                className={styles.formStyle}
                name="basic"
                initialValues={{ remember: true }}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
              >
                <Form.Item
                  label=""
                  name="username"
                  rules={[{ required: true, message: '请输入账户名称' }]}
                >
                  <Input
                    placeholder="请输入账号"
                    className={styles.antdInput}
                    prefix={<UserOutlined />}
                  />
                </Form.Item>

                <Form.Item
                  label=""
                  name="password"
                  rules={[{ required: true, message: '请输入登录密码' }]}
                >
                  <Input.Password
                    placeholder="请输入密码"
                    className={styles.antdInput}
                    prefix={<LockOutlined />}
                  />
                </Form.Item>

                <Form.Item
                  label=""
                  name="code"
                  rules={[{ required: true, message: '请输入验证码' }]}
                >
                  <div className={styles.inputCodeBox}>
                    <div>
                      {' '}
                      <Input
                        placeholder="请输入验证码"
                        className={styles.inputCode}
                        prefix={<SafetyOutlined />}
                      />
                    </div>
                    <div onClick={refreshCode} className={styles.inputCodecode}>
                      <img src={codeurl} alt="" />
                    </div>
                  </div>
                </Form.Item>
                <Button className={styles.antdBtnLogin} htmlType="submit">
                  登录
                </Button>
              </Form>
            </div>
          </div>
        </div>
        <div className={styles.unifyLogin_loginBottom}>
          <div className={styles.textB}>西南凯亚市场经营管理平台</div>
          <div className={styles.text}>
            权限所有：成都民航西南凯亚责任有限公司
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppLogin;
