import React from 'react';
import styles from './index.less';
import logo from '@/assets/img/logo.png';
import banner from '@/assets/img/banner1.png';
import project from '@/assets/img/project.png';
import manager from '@/assets/img/manager.png';
import projectPannel from '@/assets/img/projectPannel.png';
import exit from '@/assets/img/exit.png';

const AppLogin = props => {
  // const userInfo = JSON.parse(localStorage.getItem('userInfo')) || {};
  const edit = () => {
    localStorage.clear();
    window.location.replace('/hrsweb');
  };

  return (
    <div className={styles.unifyHome}>
      <div className={styles.homeTop}>
        <div className={styles.HomeLOGO}>
          <div className={styles.HomeLOGImg}>
            <img src={logo}></img>
          </div>
          <div onClick={edit} className={styles.HomeLOGExit}>
            <img src={exit}></img>
          </div>
        </div>
        <div className={styles.homeBanner}>
          <img src={banner}></img>
        </div>
      </div>
      <div className={styles.unifyHomeMain}>
        <div className={styles.homeContent}>
          <div className={styles.homeItem}>
            <div
              // onClick={() => {
              //   window.location.replace(
              //     'http://10.150.64.154:8099/#/overview/list?token=' +
              //       userInfo.access_token,
              //   );
              // }}
              className={styles.MenuItemNOclick}
            >
              <div className={styles.sysContentWrap}>
                <div className={styles.softwareImg}>
                  <img src={manager}></img>
                </div>
                <div className={styles.infoWrap}>
                  <div className={styles.infoName}>市场经营管理</div>
                  <div className={styles.infoDesc}>企业级经营管理协作</div>
                </div>
              </div>
            </div>
            <div
              onClick={() => {
                window.location.replace(
                  'http://10.150.64.154:8098/workbench?token=' +
                    userInfo.access_token,
                );
              }}
              className={styles.MenuItem}
            >
              <div className={styles.sysContentWrap}>
                <div className={styles.softwareImg}>
                  <img src={project}></img>
                </div>
                <div className={styles.infoWrap}>
                  <div className={styles.infoName}>项目管理系统</div>
                  <div className={styles.infoDesc}>企业级项目管理协作</div>
                </div>
              </div>
            </div>
            <div
              onClick={() => {
                window.location.replace(
                  'https://dz.swcare.com.cn/dashboard?token=' +
                    userInfo.access_token,
                );
              }}
              className={styles.MenuItem}
            >
              <div className={styles.sysContentWrap}>
                <div className={styles.softwareImg}>
                  <img src={projectPannel}></img>
                </div>
                <div className={styles.infoWrap}>
                  <div className={styles.infoName}>数据看板</div>
                  <div className={styles.infoDesc}>项目数据动态看板</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.homeFooter}>
          <div className={styles.homeFooterTitel}>
            {/* 西南凯亚市场经营管理平台 */}
          </div>
          <div className={styles.homeFooterText}>
            版权所有：成都民航西南凯亚责任有些公司
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppLogin;
