.unifyHome {
  width: 100%;
  min-height: 100vh;
  height: 100%;
  overflow: hidden;
  padding: 0;
  margin: 0;
}
.homeTop {
  width: 100%;
  height: 228px;
  .HomeLOGO {
    padding-left: 8px;
    padding-right: 20px;
    height: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .HomeLOGImg img {
      height: 24px;
      width: auto;
    }
    .HomeLOGExit img {
      height: 20px;
      width: auto;
    }
    .HomeLOGExit:hover {
      cursor: pointer;
    }
  }

  .homeBanner {
    width: 100%;
    height: 180px;
    overflow: hidden;
    img {
      width: 100%;
      height: 180px;
    }
  }
}
.homeFooter {
  width: 100%;
  height: 70px;
  .homeFooterTitel {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    font-size: 22px;
    font-weight: 800;
  }
  .homeFooterText {
    width: 100%;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: rgb(139, 136, 136);
    background-color: white;
  }
}
.unifyHomeMain {
  width: 100%;
  height: calc(~'100% -  228px');
  background-image: url('../../../assets/img/homebg.png');
  background-repeat: no-repeat;
  background-size: 115%;
  background-position: 50%;
}
.homeContent {
  width: 100%;
  height: calc(~'100% - 70px');
  display: flex;
  justify-content: center;
  align-items: center;
  .homeItem {
    min-width: 930px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .MenuItem {
      cursor: pointer;
      height: 90px;
      width: 250px;
      padding: 0 12px;
      background-color: white;
      box-shadow: 0 3px 9px rgb(211, 209, 209);
      border-radius: 4px;
      transition: all 0.3s ease-in;
      .sysContentWrap {
        height: 100%;
        display: flex;
        position: relative;
        justify-content: flex-start;
        align-items: center;
        .softwareImg {
          width: 66px;
          height: 66px;
          img {
            height: 100%;
            width: 100%;
          }
        }
        .infoWrap {
          width: auto;
          max-width: calc(~'100% - 70px');
          padding-left: 15px;
          .infoName {
            font-size: 17px;
            color: #252d3a;
            line-height: 22px;
          }
          .infoDesc {
            font-size: 12px;
            color: #63707a;
            line-height: 22px;
            height: 40px;
            padding-top: 10px;
          }
        }
        .infophone {
          width: 11px;
          height: 18px;
          position: absolute;
          top: 10px;
          right: 0px;
          img {
            height: 100%;
            width: 100%;
          }
        }
      }
    }
    .MenuItem:hover {
      transform: scale(1.1);
    }

    .MenuItemNOclick {
      height: 90px;
      width: 250px;
      padding: 0 12px;
      background-color: white;
      box-shadow: 0 3px 9px rgb(211, 209, 209);
      border-radius: 4px;
      transition: all 0.3s ease-in;
      .sysContentWrap {
        height: 100%;
        display: flex;
        position: relative;
        justify-content: flex-start;
        align-items: center;
        .softwareImg {
          width: 66px;
          height: 66px;
          img {
            height: 100%;
            width: 100%;
          }
        }
        .infoWrap {
          width: auto;
          max-width: calc(~'100% - 70px');
          padding-left: 15px;
          .infoName {
            font-size: 17px;
            color: #252d3a;
            line-height: 22px;
          }
          .infoDesc {
            font-size: 12px;
            color: #63707a;
            line-height: 22px;
            height: 40px;
            padding-top: 10px;
          }
        }
        .infophone {
          width: 11px;
          height: 18px;
          position: absolute;
          top: 10px;
          right: 0px;
          img {
            height: 100%;
            width: 100%;
          }
        }
      }
    }
  }
}
