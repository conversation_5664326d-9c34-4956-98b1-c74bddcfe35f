.unifyLogin {
  width: 100%;
  min-height: 100vh;
  height: 100%;
  overflow: hidden;
}

.unifyLogin_top {
  width: 100%;
  height: 48px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.unifyLogin_top_img {
  width: auto;
  height: 28px;
  img {
    width: auto;
    height: 100%;
  }
}
.unifyLogin_mian {
  width: 100%;
  height: calc(~'100% - 48px');
  //height:calc(~"100vh - 28px");
  background-image: url('../../assets/img/unifyLogin.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top;
}

.unifyLogin_loginBottom {
  width: 100%;
  height: 80px;
  z-index: -1;
}
.textB {
  width: 100%;
  text-align: center;
  font-size: 22px;
  font-weight: 800;
}
.text {
  width: 100%;
  text-align: center;
  font-size: 14px;
  padding-top: 5px;
  padding-bottom: 5px;
  color: rgb(83, 81, 81);
}
.unifyLogin_loginForm {
  width: 100%;
  height: calc(~'100% - 80px');
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}
.unifyLogin_loginFormMain {
  min-width: 380px;
  width: 380px;
  height: auto;
  background: #fff;
  box-shadow: 0 2px 8px rgb(199, 196, 196);
  border-radius: 8px;
  padding: 36px 40px;
  z-index: 120;
}
.loginTitle {
  font-family: 'OPPOSans';
  text-align: center;
  font-size: 30px;
  line-height: 38px;
  color: #333;
}
.formWrap {
  margin-top: 36px;
}
.inputCodeBox {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
}
.inputCode {
  width: 180px;
  height: 40px;
}
.formStyle {
  width: 100%;
  height: auto;
  :global {
    .ant-form-item {
      height: 100%;
    }
  }
}
.antdInput {
  width: 100%;
  height: 40px;
}
.antdBtnLogin {
  width: 100%;
  height: 50px;
  color: #fff;
  background: #1890ff;
  border-color: #1890ff;
}
.antdBtnLogin:hover {
  background: #1d89ee;
  border-color: #1890ff;
  color: #fff;
}

.antdBtnLogin:active {
  background: #5ea5e7;
  border-color: #6798c7;
  color: #fff;
}

.ant-btn:hover,
.ant-btn:focus,
.ant-btn:active {
  text-decoration: none;
  background: #5ea5e7;
}
.antdBtnLogin:focus {
  background: #8ab9e6;
  color: #fff;
}
