.contentBox {
  height: 100%;
  padding: 24px;
  background: #f7f8fa;
}

.card {
  width: 100%;
  background: #ffffff;
  border: 1px solid #edeff2;
  box-sizing: border-box;
  padding: 13px 24px;
  box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
  border-radius: 8px;
  margin-bottom: 24px;

  :global {
    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      background-color: #3d7bf8;
      border-color: #3d7bf8;
    }

    .ant-radio-button-wrapper:first-child {
      border-radius: 5px 0 0 5px;
    }

    .ant-radio-button-wrapper:last-child {
      border-radius: 0 5px 5px 0;
    }

    .ant-btn-primary {
      background-color: #3d7bf8;
    }
  }

  .cardOption {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .tabSwitch {
    width: 40%;

    span {
      margin-right: 20px;
    }
  }

  .cardRight {
    width: 280px;
    display: flex;
    justify-content: flex-end;

    .spotIcon > div {
      width: 4px;
      height: 4px;
      background-color: #7a7a7a;
      border-radius: 50%;
    }

    .spotIcon > div:not(:first-child) {
      margin-top: 2px;
    }
  }

  .project {
    display: flex;
    justify-content: space-between;

    button {
      border-radius: 0;
      transition: all 0s ease;
      position: relative;
      top: 0;
      vertical-align: middle;
    }

    .manage {
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
    }

    .no_manage {
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }

    .active {
      background: #3d7bf8;
      color: #ffffff;
    }

    .focus {
      button {
        font-family: OPPOSans;
        font-style: normal;
        font-size: 14px;
        border: 1px solid #edeff2;
        box-sizing: border-box;
        border-radius: 6px;
      }
    }
  }

  p {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    /* identical to box height, or 157% */
    /* Netural Color/Grey 600 */
    color: #525252;
  }

  input,
  button {
    box-sizing: border-box;
    border-radius: 6px;
  }
}

.searchInput {
  display: flex;
  justify-content: space-between;
  .tips {
    font-size: 10px;
    color: #ff4d4f;
  }
  > div {
    width: 15%;
  }

  > div:nth-child(3) {
    width: 30%;
  }

  > div:last-child {
    width: 20%;

    > button {
      margin-right: 20px;
    }
  }
}

.rangePicker {
  border: 1px solid #edeff2;
  box-sizing: border-box;
  border-radius: 6px;

  input {
    border: 0;
  }
}

.splitPigination {
  display: flex;
  justify-content: space-between;
  padding: 0 0 0 0px;
  margin-top: 20px;

  .pageSelect {
    box-sizing: border-box;
    border-radius: 6px;
  }

  .total {
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    margin-left: 8px;
    color: #7a7a7a;
  }

  input,
  button,
  li {
    box-sizing: border-box;
    border-radius: 6px;
  }
}

.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;

  > div {
    display: flex;

    > div {
      width: 144px;
      height: 32px;
      left: 0px;
      top: 0px;
      background: #ffffff;
      border: 1px solid #edeff2;
      box-sizing: border-box;
      border-radius: 6px;
    }
  }

  p {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    margin: 6px 0 0 10px;
    /* identical to box height, or 167% */

    /* Netural Color/Grey 500 */

    color: #7a7a7a;
  }

  // width: 100%;
  // float: right;
  // >li:last-child {
  //   float: left;
  //   margin-right: 10px;
  //   margin-left: 0;
  // }
  > li {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
  }
}

.anTdTable tr > th,
.anTdTable tr > td {
  border-bottom: 0px;
  padding: 10px 8px !important;
}

.anTdTable tr > th {
  font-size: 12px;
  line-height: 20px;
  color: #7a7a7a;

  > div > div {
    padding: 10px 16px;
  }
}

.deleteBt {
  a {
    color: #e8684a;
  }

  span {
    margin-right: 10px;
    color: #1890ff;
  }

  cursor: pointer;
}

.selects,
.selects > div {
  border-color: #edeff2 !important;
  box-sizing: border-box;
  border-radius: 6px !important;
}

.add_type_item {
  > div > div > div {
    display: flex;
  }

  span {
    line-height: 32px;
    margin-left: 10px;
    cursor: pointer;
  }
}

.search_bottom {
  margin-bottom: -20px;
}
