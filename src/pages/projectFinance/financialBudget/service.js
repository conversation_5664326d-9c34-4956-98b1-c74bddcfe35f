import request from '@/utils/request';
import { BASE_URl } from "../../../utils/constant";

//获取收入列表list
export function getDepBudget(parmas) {
  const data = parmas;
  if (parmas.deptId) {
    data.deptId = parmas.deptId.toString();
  }
  return request(`${BASE_URl}/Finance/getDepBudget`, {
    method: 'post',
    data: data,
  });
}

//删除
export function deleteDepBudget(parmas) {
  return request(`${BASE_URl}/Finance/deleteDepBudget?id=${parmas}`, {
    method: 'delete',
  });
}

//新增
export function editDepBudget(parmas) {
  return request(`${BASE_URl}/Finance/editDepBudget`, {
    method: 'post',
    data: parmas,
  });
}
//新增
export function addBudget(parmas) {
  return request(`${BASE_URl}/Finance/insertDepBudget`, {
    method: 'post',
    data: parmas,
  });
}

//获取部门下拉框数据
export function getDeptListInfo() {
  return request(`${BASE_URl}/dept/SWCARESdeptAll`, {
    method: 'POST',
  });
}

//下拉框信息
export function getBasics() {
  return request(`${BASE_URl}/Finance/getBasics`, {
    method: 'post',
  });
}
