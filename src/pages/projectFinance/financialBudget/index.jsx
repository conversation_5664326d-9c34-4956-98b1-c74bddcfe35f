import React, { useState, useEffect } from 'react';
import {
  getDepBudget,
  editDepBudget,
  deleteDepBudget,
  getDeptListInfo,
  getBasics,
  addBudget,
} from './service';
import {
  Button,
  Modal,
  Pagination,
  Table,
  Form,
  Tooltip,
  Input,
  InputNumber,
  Select,
  Slider,
  Popconfirm,
  Radio,
  Row,
  Col,
  DatePicker,
  message,
  Upload,
} from 'antd';
import {
  ClockCircleOutlined,
  PlusOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import classNames from 'classnames';
import styles from './index.less';
import moment from 'moment';
import { MyContext } from '../../home';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea, Search } = Input;
const RadioButton = Radio.Button;
export default props => {
  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  useEffect(() => {
    setButtonList(context);
  }, [context]);
  useEffect(() => {
    setColumns(totalColumns);
  }, [buttonList]);

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [data, setData] = useState([]);
  const [dataTotal, setDataTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [parmas, setParmas] = useState({ page: 1, limit: 10 });

  //弹窗标题
  const [modalTitle, setModalTitle] = useState('');
  //
  const [modalItem, setModalItem] = useState({});
  const [visibleModal, setVisibleModal] = useState(false);

  const [form] = Form.useForm();
  const [seachForm] = Form.useForm();

  //费用类分类
  const [inComeTypeList, setInComeTypeList] = useState([]);
  //预算总额
  const [totalBudget, setTotalBudget] = useState(0);
  //部门列表
  const [deptList, setDeptList] = useState([]);
  //部门列表
  const [addDeptList, setAddDeptList] = useState([]);
  // 点击按钮查询
  const [searchParmas, setSearchParmas] = useState({});
  const getlist = async parmas => {
    setLoading(true);
    const { data, code } = await getDepBudget(parmas);
    if (code === 200) {
      setLoading(false);
      setData(data.records);
      setDataTotal(data.total);
      if (data.records && data.records.length > 0) {
        setTotalBudget(data.records[0].totalBudget || 0);
      }
    } else {
    }
  };
  //获取费用类分类
  const getBasicss = async () => {
    const { data, code } = await getBasics();
    if (code === 200) {
      setDeptList(data.dept_type);
      setInComeTypeList(data.cost_type);
    }
  };
  //获取部门下拉
  const getDeptListInfos = async value => {
    const { code, data } = await getDeptListInfo(value);
    if (code === 200) {
      const addDeptList = new Set();
      data.forEach(item => {
        let repeat = false;
        addDeptList.forEach(value => {
          if (value.deptFullName === item.deptFullName) {
            repeat = true;
          }
        });
        if (!repeat) {
          item.deptFullName && addDeptList.add(item);
        }
      });
      setAddDeptList(addDeptList);
    }
  };
  useEffect(() => {
    getBasicss();
    getDeptListInfos();
  }, []);
  useEffect(() => {
    getlist(parmas);
  }, [parmas]);
  //动态表头
  const totalColumns = [
    {
      title: '部门名称',
      dataIndex: 'departmentName',
      key: '1',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '预算年度',
      dataIndex: 'year',
      ellipsis: {
        showTitle: false,
      },
      key: '2',
      align: 'left',
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '费用类别',
      dataIndex: 'costName',
      key: '3',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      filterIcon: filtered => handleGetIcon(filtered),
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '预算金额(w)',
      align: 'left',
      render: value => <span>{value}</span>,
      dataIndex: 'budgetMoney',
      key: '4',
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '预算备注',
      dataIndex: 'remark',
      key: '5',
      render: value => <span>{value}</span>,
      align: 'left',
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: '10%',
      render: item => {
        return (
          <div className={styles.deleteBt}>
            {(buttonList.includes('/Finance/editDepBudget') ||
              buttonList.includes('admin')) && (
              <span
                onClick={() => showMoald({ title: '编辑预算', item: item })}
              >
                编辑
              </span>
            )}
            {(buttonList.includes('/Finance/deleteDepBudget') ||
              buttonList.includes('admin')) && (
              <Popconfirm
                title="是否删除？"
                okText="是"
                cancelText="否"
                onConfirm={() => deleteItem(item)}
              >
                <a>删除</a>
              </Popconfirm>
            )}
          </div>
        );
      },
    },
  ];
  const onChangePageNumber = (value, size) => {
    setParmas({ page: value, limit: size });
  };

  const [columns, setColumns] = useState(totalColumns);
  //改变每页条数
  const pageSizeChange = value => {
    setParmas({ ...parmas, limit: value });
  };

  const [modalAddType, setModalAddType] = useState('');
  const showMoald = porps => {
    const { title, item } = porps;
    if (title === '编辑预算') {
      setModalItem({ ...item });
      setModalTitle(`${item.departmentName}`);
      setModalAddType(`${item.costName}`);
      form.setFieldsValue({
        budgetMoney: item.budgetMoney,
        surplusMoney: item.surplusMoney,
        remark: item.remark,
      });
    } else {
      setModalTitle(title);
      form.setFieldsValue({
        year: '',
        depart: '',
        totalBudget: '',
      });
      setTypeItemList([
        {
          costType: '',
          budgetMoney: '',
          remark: '',
        },
      ]);
    }
    setVisibleModal(true);
  };
  const handleOk = () => {
    setVisibleModal(false);
  };

  const handleCancel = () => {
    setVisibleModal(false);
  };

  const [typeItemList, setTypeItemList] = useState([
    {
      costType: '',
      budgetMoney: '',
      remark: '',
    },
  ]);
  const [totalMoney, setTotalMoney] = useState(0);
  useEffect(() => {
    const list = typeItemList;
    let total = 0;
    list.forEach(item => {
      total += Number(item.budgetMoney);
    });
    setTotalMoney(total);
    console.log(typeItemList, 'typeItemList', totalMoney);
  }, [typeItemList]);

  const typeItemListChange = porps => {
    const { value, key, index } = porps;
    const list = typeItemList;
    list[index][key] = value;

    setTypeItemList([...list]);
  };
  const addTypeItem = () => {
    setTypeItemList([
      ...typeItemList,
      {
        costType: '',
        budgetMoney: 0,
        remark: '',
      },
    ]);
  };
  const deleteTypeItem = index => {
    const list = typeItemList;
    list.splice(index, 1);
    setTypeItemList([...list]);
  };

  //表单提交
  const onFinishFormData = value => {
    if (modalTitle === '新增预算') {
      const index = value.depart.indexOf('@');
      typeItemList.forEach(item => {
        const idIndex = item.cost.indexOf('@');
        item.costType = item.cost.substring(0, idIndex);
        item.costName = item.cost.substring(idIndex + 1);
        item.departmentId = value.depart.substring(0, index);
        item.departmentName = value.depart.substring(index + 1);
        item.totalBudget = value.totalBudget;
        item.year = moment(value.year).format('YYYY');
      });
      addItem([...typeItemList]);
    } else {
      editItem([{ ...modalItem, ...value }]);
    }
  };

  //新增预算
  const addItem = async value => {
    const resp = await addBudget(value);
    if (resp.code === 200) {
      message.success({ content: '成功!', key: 'editItem', duration: 2 });
      getlist(parmas);
      getBasicss();
      setVisibleModal(false);
    } else {
      message.error({ content: '失败!', key: 'editItem', duration: 2 });
    }
  };

  const editItem = async value => {
    const resp = await editDepBudget(value);
    if (resp.code === 200) {
      message.success({ content: '成功!', key: 'editItem', duration: 2 });
      getlist(parmas);
      getBasicss();
      setVisibleModal(false);
    } else {
      message.error({ content: '失败!', key: 'editItem', duration: 2 });
    }
  };

  /**
   * 搜索值变化
   */
  const handleSearchParams = props => {
    const { key, value } = props;
    // 时间插件获取值不同
    if (key === 'year') {
      setSearchParmas({
        ...searchParmas,
        startYear: value[0],
        endYear: value[1],
      });
    } else {
      setSearchParmas({ ...searchParmas, [key]: value });
    }
  };
  // 搜索
  const handleOnSearch = () => {
    setParmas({ ...parmas, ...searchParmas, page: 1 });
  };
  const { limit, page } = parmas;

  const resetSearch = () => {
    setSearchParmas({});
    seachForm.setFieldsValue({
      deptId1: '',
      costType1: '',
      year: '',
    });
  };
  //删除
  const deleteItem = value => {
    deleteItems({ id: value.id, departmentId: value.departmentId });
  };

  const deleteItems = async value => {
    const { id, departmentId } = value;
    const { data, code } = await deleteDepBudget(id);
    if (code === 200) {
      message.success({
        content: '删除成功!',
        key: 'deleteDepBudget',
        duration: 2,
      });
      getlist({ ...parmas, deptId: departmentId });
    }
  };

  return (
    <div className={styles.contentBox}>
      <div className={styles.card}>
        <Form form={seachForm} onFinish={handleOnSearch}>
          <div className={classNames(styles.searchInput, styles.search_bottom)}>
            <div>
              <p>预算部门</p>
              <Form.Item name="deptId1">
                <Select
                  showSearch
                  mode="multiple"
                  defaultActiveFirstOption={false}
                  placeholder="选择部门"
                  style={{ width: '80%' }}
                  filterOption={false}
                  // onSearch={handleSearch}
                  allowClear={true}
                  className={styles.selects}
                  notFoundContent={null}
                  onChange={value =>
                    handleSearchParams({ value, key: 'deptId' })
                  }
                >
                  {deptList.map(
                    item =>
                      item && (
                        <Option
                          value={item.department_id}
                          key={item.department_name}
                        >
                          {item.department_name}
                        </Option>
                      ),
                  )}
                </Select>
              </Form.Item>
            </div>
            <div>
              <p>费用类别</p>
              <Form.Item name="costType1">
                <Select
                  style={{ width: '80%' }}
                  showSearch
                  allowClear={true}
                  defaultActiveFirstOption={false}
                  placeholder="选择费用类别"
                  filterOption={false}
                  // onSearch={handleSearch}
                  onChange={value =>
                    handleSearchParams({ value, key: 'costType' })
                  }
                  className={styles.selects}
                  notFoundContent={null}
                  filterOption={(input, option) =>
                    option.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {inComeTypeList.map(item => (
                    <Option value={item.code} key={item.code}>
                      {item.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
            <div>
              <p>预算年度</p>
              <Form.Item name="year">
                <RangePicker
                  className={styles.selects}
                  suffixIcon={<ClockCircleOutlined />}
                  picker="year"
                  format="YYYY"
                  onChange={(date, deteString) =>
                    handleSearchParams({ value: deteString, key: 'year' })
                  }
                />
              </Form.Item>
            </div>

            <>
              <div>
                <p></p>
                <br />
                <Button onClick={resetSearch}>重置</Button>
                <Button type="primary" htmlType="submit">
                  查询
                </Button>
              </div>
            </>
          </div>
        </Form>
      </div>

      <div className={styles.card}>
        <div className={styles.cardOption}>
          <div className={styles.tabSwitch}>
            {/* <Radio.Group
              defaultValue="a"
              buttonStyle="solid"
              onChange={e => tabChange(e)}
            >
              <RadioButton value="a">项目收入</RadioButton>
              <RadioButton value="b">项目成本</RadioButton>
            </Radio.Group> */}
            <span>预算总额：{totalBudget}W</span>
          </div>
          <div className={styles.cardRight}>
            {(buttonList.includes('/Finance/insertDepBudget') ||
              buttonList.includes('admin')) && (
              <Button
                type="primary"
                onClick={() => showMoald({ title: '新增预算' })}
              >
                新增预算
              </Button>
            )}
          </div>
        </div>
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          // rowSelection={rowSelection}
          loading={loading}
          size="middle"
          className={styles.anTdTable}
        />
        <div className={styles.splitPigination}>
          <div>
            <Select
              defaultValue="10"
              style={{ width: 150 }}
              className={styles.selects}
              onChange={pageSizeChange}
            >
              <Option value="10">显示结果：10条</Option>
              <Option value="20">显示结果：20条</Option>
              <Option value="50">显示结果：50条</Option>
            </Select>
            <span className={styles.total}>共{dataTotal}条</span>
          </div>
          <Pagination
            total={dataTotal || 0}
            pageSize={limit}
            showSizeChanger={false}
            current={page}
            key={67}
            onChange={onChangePageNumber}
          />
        </div>
        <Modal
          title={`${modalTitle}`}
          visible={visibleModal}
          onOk={handleOk}
          onCancel={handleCancel}
          footer={null}
        >
          <Form
            layout="vertical"
            form={form}
            name="nest-messages"
            onFinish={onFinishFormData}
          >
            {modalTitle === '新增预算' && (
              <>
                <Form.Item
                  label="部门"
                  name="depart"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    showSearch
                    defaultActiveFirstOption={false}
                    placeholder="选择部门"
                    filterOption={false}
                    style={{ width: 300 }}
                    // onSearch={handleSearch}
                    className={styles.selects}
                    notFoundContent={null}
                    filterOption={(input, option) =>
                      option.children
                        .toLowerCase()
                        .indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {addDeptList.map(item => (
                      <Option
                        value={`${item.id}@${item.deptFullName}`}
                        key={item.deptFullName}
                      >
                        {item.deptFullName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Row>
                  <Col span={8}>
                    <Form.Item
                      label="年度预算"
                      name="year"
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                    >
                      <DatePicker
                        suffixIcon={<ClockCircleOutlined />}
                        className={styles.selects}
                        style={{ width: 150 }}
                        picker="year"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      style={{ marginTop: 30 }}
                      // name="totalBudget"
                    >
                      <InputNumber
                        className={styles.selects}
                        min={0}
                        value={totalMoney}
                        disabled
                        formatter={value => `${value}W`}
                        parser={value => value.replace('W', '')}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                {typeItemList.map((items, index) => {
                  return (
                    <Row key={index}>
                      <Col span={8}>
                        <Form.Item
                          label={index === 0 && '费用类别'}
                          rules={[
                            {
                              required: true,
                            },
                          ]}
                        >
                          <Select
                            showSearch
                            defaultActiveFirstOption={false}
                            placeholder="选择费用类别"
                            style={{ width: 150 }}
                            value={items.cost}
                            filterOption={false}
                            className={styles.selects}
                            onChange={value =>
                              typeItemListChange({ value, key: 'cost', index })
                            }
                            notFoundContent={null}
                            filterOption={(input, option) =>
                              option.children
                                .toLowerCase()
                                .indexOf(input.toLowerCase()) >= 0
                            }
                          >
                            {inComeTypeList.map(item => (
                              <Option
                                value={`${item.code}@${item.name}`}
                                key={item.code}
                              >
                                {item.name}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </Col>
                      <Col span={5}>
                        <Form.Item style={{ marginTop: index === 0 && 30 }}>
                          <InputNumber
                            className={styles.selects}
                            onChange={value =>
                              typeItemListChange({
                                value,
                                key: 'budgetMoney',
                                index,
                              })
                            }
                            value={items.budgetMoney}
                            min={0}
                            formatter={value => `${value}W`}
                            parser={value => value.replace('W', '')}
                          />
                        </Form.Item>
                      </Col>

                      <Col span={9}>
                        <Form.Item
                          style={{ marginTop: index === 0 && 30 }}
                          label={''}
                          className={styles.add_type_item}
                        >
                          <Input
                            className={styles.selects}
                            value={items.remark}
                            placeholder="备注"
                            onChange={e =>
                              typeItemListChange({
                                value: e.target.value,
                                key: 'remark',
                                index,
                              })
                            }
                          />
                          {index === 0 ? (
                            <PlusOutlined onClick={addTypeItem} />
                          ) : (
                            <CloseOutlined
                              onClick={() => deleteTypeItem(index)}
                            />
                          )}
                        </Form.Item>
                      </Col>
                    </Row>
                  );
                })}
              </>
            )}
            {modalTitle !== '新增预算' && (
              <>
                <Form.Item
                  label={`${modalAddType} 预算(W)`}
                  name="budgetMoney"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <InputNumber
                    className={styles.selects}
                    min={0}
                    style={{ width: 300 }}
                  />
                </Form.Item>
                <Form.Item
                  label="备注"
                  name="remark"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <TextArea
                    className={styles.selects}
                    style={{ width: 300 }}
                    rows={4}
                  />
                </Form.Item>
              </>
            )}
            <Form.Item style={{ marginBottom: 0 }}>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
};
