import React, { useState, useEffect } from 'react';
import {
  getIncomeListPage,
  getOutcomeListPage,
  deleteCome,
  insertIncome,
  updateOutcome,
  insertOutcome,
  uploadExcelInCome,
  uploadExcelOutCome,
  getInComeType,
  getFileUrl,
  getInComeListType,
  editIncome,
  selectOutcomeDetail,
} from './service';
import {
  Button,
  Modal,
  Pagination,
  Table,
  Form,
  Tooltip,
  Input,
  InputNumber,
  Select,
  Radio,
  DatePicker,
  message,
  Upload,
} from 'antd';
import styles from './index.less';
import moment from 'moment';
import {
  ClockCircleOutlined,
  PlusOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import classNames from 'classnames';
import uniqueId from 'lodash/uniqueId';
import { MyContext } from '../../home';
import { BASE_URl } from "../../../utils/constant";

const { Option } = Select;
const RadioButton = Radio.Button;
export default props => {
  const context = React.useContext(MyContext);
  const [buttonList, setButtonList] = useState([]);
  useEffect(() => {
    setButtonList(context);
  }, [context]);
  useEffect(() => {
    setColumns(totalColumns1);
  }, [buttonList]);

  const [data, setData] = useState([]);
  const [dataTotal, setDataTotal] = useState(0);
  const [tab, setTab] = useState('a');
  const [loading, setLoading] = useState(true);
  const [parmas, setParmas] = useState({ page: 1, limit: 10 });
  //存储费用类别数组
  const [typeData, setTypeData] = useState([]);

  //弹窗标题
  const [modalTitle, setModalTitle] = useState('');
  //编辑成本弹窗标题
  const [modalTitleUpdate, setModalTitleUpdate] = useState('');
  const [ModalTypeUpdate, setModalTypeUpdate] = useState('');
  const [ModalTypeUpdateIn, setModalTypeUpdateIn] = useState('');
  const [modalTitleUpdateIn, setModalTitleUpdateIn] = useState('');
  //编辑成本数据回显

  const [outcomeDetail, setOutcomeDetail] = useState([]);

  const [modalItem, setModalItem] = useState({});
  const [NumberI, setNumberI] = useState(-4);
  const [arrCreate, setArrCreate] = useState([
    {
      id: null,
      uniqueId: uniqueId(),
      actualBillingAmount: 0,
      planBillingTime: null,
      refundNumber: null,
      refundTime: null,
    },
  ]);

  const [visibleModal, setVisibleModal] = useState(false);
  const [visibleModalUpdate, setVisibleModalUpdate] = useState(false);
  const [visibleModalUpdateIn, setVisibleModalUpdateIn] = useState(false);
  const [form] = Form.useForm();
  const MonthPicker = DatePicker.MonthPicker;
  const [inComeTypeList, setInComeTypeList] = useState([]);
  // 点击按钮查询
  const [searchParmas, setSearchParmas] = useState({});
  //获取表单数据
  const [MoneyFormParmasIn, setMoneyFormParmasIn] = useState({});
  //编辑成本费用类型数组
  const [moneyArr, setMoneyArr] = useState({});

  //编辑弹出框表单样式
  const formItemLayout = {
    labelCol: { span: 6 },
  };
  //编辑弹出框表单样式
  const formItemLayoutUpdateIn = {
    labelCol: { span: 4 },
  };

  const getlist = async (parmas, tab) => {
    setLoading(true);
    console.log(tab, 'tabtabtab');
    if (tab == 'a') {
      const resp1 = await getIncomeListPage(parmas);
      if (resp1.code === 200) {
        setLoading(false);
        setData(resp1.data.records);
        setDataTotal(resp1.data.total);
      } else {
      }
    } else {
      const resp2 = await getOutcomeListPage(parmas);
      if (resp2.code === 200) {
        setLoading(false);
        setData(resp2.data.records);
        setDataTotal(resp2.data.total);
      } else {
      }
    }
  };
  const [itemCostUrl, setItemCostUrl] = useState('');
  const [itemPlanUrl, setItemPlanUrl] = useState('');
  const getFileUrlList = async () => {
    const { code, data } = await getFileUrl();
    if (data) {
      data.forEach(item => {
        if (item.common_name === 'item_cost_url') {
          setItemCostUrl(item.common_info);
        }
        if (item.common_name === 'item_plan_url') {
          setItemPlanUrl(item.common_info);
        }
      });
    }
  };
  //项目收入详情
  const getInComeListTypes = async (getItemNum, type) => {
    const { data, code } = await getInComeListType(getItemNum);
    if (code === 200) {
      if (type == 'update') {
        data;
        if (data.length == 0) {
          setArrCreate([
            {
              id: null,
              actualBillingAmount: 0,
              uniqueId: uniqueId(),
              planBillingTime: null,
              refundNumber: 0,
              refundTime: null,
            },
          ]);
        } else {
          setArrCreate([]);
          const getcreate = data.map((items, index) => {
            let {
              id,
              planBillingTime,
              actualBillingAmount,
              refundNumber,
              refundTime,
            } = items;
            return {
              id: id,
              uniqueId: uniqueId(),
              planBillingTime: planBillingTime,
              actualBillingAmount: actualBillingAmount,
              refundNumber: refundNumber,
              refundTime: refundTime,
            };
          });
          setArrCreate(getcreate);
        }
      }
      if (type == 'query') {
        data;
        if (data.length == 0) {
          setArrCreate([]);
        } else {
          var newData = data.map(item => {
            let {
              actualBillingAmount,
              dept,
              id,
              itemName,
              itemNum,
              planBillingTime,
              refundNumber,
              refundTime,
            } = item;
            return {
              actualBillingAmount: actualBillingAmount,
              dept: dept,
              id: id,
              itemName: itemName,
              uniqueId: uniqueId(),
              itemNum: itemNum,
              planBillingTime: planBillingTime,
              refundNumber: refundNumber,
              refundTime: refundTime,
            };
          });
          setArrCreate(newData);
        }
      }
    }
  };

  //获取费用类分类
  const getInComeTypes = async () => {
    const { data, code } = await getInComeType();
    if (code === 200) {
      data;
      const typeData = data.map(item => {
        let { ORDER_BY_DERIVED_0, name, code } = item;
        return {
          title: name + '(W)',
          dataIndex: [code, 'actualCost'],
          key: 'calss' + ORDER_BY_DERIVED_0,
          align: 'center',
        };
      });
      setTypeData(typeData);
    }
  };

  useEffect(() => {
    getFileUrlList();
    getInComeTypes();
  }, []);

  useEffect(() => {
    getlist(parmas, tab);
  }, [parmas, tab]);
  //动态表头
  const totalColumns1 = [
    {
      title: '部门',
      dataIndex: 'dept',
      key: 'dept',
      align: 'left',
      fixed: 'left',
      onCell: () => {
        return {
          style: {
            maxWidth: 150,
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            cursor: 'pointer',
          },
        };
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '项目名称',
      dataIndex: 'itemName',
      fixed: 'left',
      ellipsis: {
        showTitle: false,
      },
      key: '2',
      align: 'left',
      width: '180px',
      onCell: () => {
        return {
          style: {
            maxWidth: 100,
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            cursor: 'pointer',
          },
        };
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },

    {
      title: '项目编号',
      dataIndex: 'itemNum',
      fixed: 'left',
      key: 'itemNum',
      align: 'left',
      ellipsis: {
        showTitle: false,
      },
      width: '180px',
      onCell: () => {
        return {
          style: {
            maxWidth: 100,
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            cursor: 'pointer',
          },
        };
      },
      render: item_num => (
        <Tooltip placement="topLeft" title={item_num}>
          {item_num}
        </Tooltip>
      ),
    },
    {
      title: '合计开票金额(w)',
      align: 'center',
      dataIndex: 'actualBillingAmount',
      key: 'actualBillingAmount',
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '开票/计划（%）',
      align: 'center',
      dataIndex: 'percentPilling',
      key: 'percentPilling',
      render: value => Number(value * 100).toFixed(2),
      filterIcon: filtered => handleGetIcon(filtered),
    },
    {
      title: '合计回款金额(w)',
      align: 'center',
      dataIndex: 'refundNumber',
      key: '5',
      filterIcon: filtered => handleGetIcon(filtered),
    },

    {
      title: '回款/计划(%)',
      dataIndex: 'percentRefund',
      key: '5-1',
      render: value => Number(value * 100).toFixed(2),
      align: 'center',
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: '120px',
      render: item => {
        return (
          <>
            {(buttonList.includes('/Finance/editIncome') ||
              buttonList.includes('admin')) && (
              <span
                className={styles.updateBt}
                onClick={() =>
                  showMoaldUpdateIn({
                    title: '编辑收入(税后)',
                    item: item,
                    type: 'update',
                  })
                }
              >
                <a>编辑</a>
              </span>
            )}

            <span
              className={styles.updateBt}
              onClick={() =>
                showMoaldUpdateIn({ title: '详情', item: item, type: 'query' })
              }
            >
              <a>详情</a>
            </span>
          </>
        );
      },
    },
  ];

  const totalColumns2 = [
    {
      title: '立项部门',
      dataIndex: 'deptName',
      key: '3',
      align: 'left',
      fixed: 'left',
      onCell: () => {
        return {
          style: {
            maxWidth: 140,
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            cursor: 'pointer',
          },
        };
      },
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '项目编号',
      dataIndex: 'itemNum',
      key: '1',
      align: 'left',
      fixed: 'left',
      onCell: () => {
        return {
          style: {
            maxWidth: 190,
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            cursor: 'pointer',
          },
        };
      },
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '项目名称',
      dataIndex: 'itemName',
      key: '4',
      align: 'left',

      fixed: 'left',
      onCell: () => {
        return {
          style: {
            maxWidth: 170,
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            cursor: 'pointer',
          },
        };
      },
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
  ];

  const operation = [
    {
      title: '总费用(w)',
      dataIndex: 'total',
      align: 'left',
      fixed: 'right',
      width: '80px',
      key: '2',
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      width: '100px',
      render: item => {
        return (
          <div>
            {(buttonList.includes('/Finance/editOutcome') ||
              buttonList.includes('admin')) && (
              <span
                className={styles.updateBt}
                onClick={() =>
                  showMoaldUpdate({
                    title: '编辑成本',
                    item: item,
                    type: 'update',
                  })
                }
              >
                <a>编辑</a>
              </span>
            )}
            <span
              className={styles.updateBt}
              onClick={() =>
                showMoaldUpdate({ title: '详情', item: item, type: 'query' })
              }
            >
              <a>详情</a>
            </span>
          </div>
        );
      },
    },
  ];

  const totalColumns3 = totalColumns2.concat(typeData).concat(operation);

  const onChangePageNumber = (value, size) => {
    setParmas({ page: value, limit: size });
  };

  const [columns, setColumns] = useState(totalColumns1);

  //改变每页条数
  const pageSizeChange = value => {
    setParmas({ ...parmas, limit: value });
  };
  //tab切换
  const tabChange = e => {
    switch (e.target.value) {
      case 'a':
        setTab('a');
        setColumns(totalColumns1);
        setParmas({ page: 1, limit: 10 });
        serUploadProps({
          action: BASE_URl + '/File/uploadExcelInCome',
          headers: {
            isToken: false,
            Authorization: `Bearer ${userInfo.access_token}`,
          },
          onChange(info) {
            if (info.file.status === 'done') {
              message.success(`上传成功`);
              getlist(parmas, 'a');
            } else if (info.file.status === 'error') {
              message.error({
                content: `上传失败!${info.file.response.msg}`,
                key: 'error',
                duration: 3,
              });
            }
          },
        });
        break;
      case 'b':
        setTab('b');
        setColumns(totalColumns3);
        setParmas({ page: 1, limit: 10 });
        serUploadProps({
          action: BASE_URl+'/File/uploadExcelOutCome',
          headers: {
            isToken: false,
            Authorization: `Bearer ${userInfo.access_token}`,
          },
          onChange(info) {
            if (info.file.status === 'done') {
              message.success(`上传成功`);
              getlist(parmas, 'b');
            } else if (info.file.status === 'error') {
              message.error({
                content: `上传失败!${info.file.response.msg}`,
                key: 'error',
                duration: 3,
              });
            }
          },
        });
        break;
    }
  };

  const [selectData, setSelectData] = useState();
  const [selectDataId, setSelectDataId] = useState({});

  const showMoaldUpdate = porps => {
    var selectDataIdArr = {};
    var nember = 0;
    setOutEditList([]);
    setNumberI(nember);
    const { title, item, type } = porps;
    setVisibleModalUpdate(true);
    setModalTitleUpdate(title);
    setModalTypeUpdate(type);
    setModalItem({ ...item });
    form.setFieldsValue({
      deptName: item.deptName,
      itemNum: item.itemNum,
      itemName: item.itemName,
    });
    const newObj = {};
    newObj['itemName'] = item.deptName;
    newObj['itemNum'] = item.itemNum;
    newObj['itemName'] = item.itemName;
    setSelectData(newObj);
    const keys = Object.keys(item);
    var i = -4;
    keys.forEach(key => {
      if (item[key].id && item[key].id !== ' ') {
        selectDataIdArr[key] = item[key].id;
      }
    });
    keys.forEach(key => {
      if (item[key].actualCost != '-' && item[key].planCost != ' ') {
        i = i + 1;
      } else {
        setNumberI(-4);
      }
    });
    setSelectDataId({ ...selectDataIdArr });
    setNumberI(i);
    if (item.itemNum) {
      setOutcomeDetail([]);
      getOutcomeDetail(item.itemNum);
    }
  };

  //修改
  const getOutcomeDetail = async value => {
    const { code, data } = await selectOutcomeDetail(value);
    if (code === 200) {
      setOutcomeDetail([...data.list]);
    }
  };

  //收入更新
  const showMoaldUpdateIn = porps => {
    setArrCreate([]);
    const { title, item, type } = porps;
    setModalTypeUpdateIn(type);
    getInComeListTypes(item.itemNum, type);
    const formObjPublic = {};
    formObjPublic['itemNum'] = item.itemNum;
    formObjPublic['itemName'] = item.itemName;
    formObjPublic['dept'] = item.dept;
    setMoneyFormParmasIn(formObjPublic);
    setVisibleModalUpdateIn(true);
    setModalTitleUpdateIn(title);
    form.setFieldsValue({
      dept: item.dept,
      itemNum: item.itemNum,
      itemName: item.itemName,
    });
  };

  const handleOkUp = () => {
    setNumberI(-4);
    const newobjs = {};
    setMoneyArr(newobjs);
    setModalItem(newobjs);
    setVisibleModalUpdate(false);
  };

  const handleCancelUp = () => {
    setNumberI(-4);
    const newobjs = {};
    setMoneyArr(newobjs);
    setModalItem(newobjs);
    setVisibleModalUpdate(false);
  };

  const handleOkUpIn = () => {
    setVisibleModalUpdateIn(false);
  };

  const handleCancelUpIn = () => {
    setArrCreate([]);
    setVisibleModalUpdateIn(false);
  };

  const handleOk = () => {
    setArrCreate([]);
    setVisibleModal(false);
  };

  const handleCancel = () => {
    setVisibleModal(false);
  };

  //表单提交
  const onFinishFormData = value => {
    if (tab === 'a') {
      const planBillingTime = moment(value.planBillingTime).format(
        'YYYY-MM-DD',
      );
      const refundTime = moment(value.refundTime).format('YYYY-MM-DD');
      addProjectItem({
        ...value,
        planBillingTime,
        refundTime,
      });
    }
    if (tab === 'b') {
      addProjectItem({
        ...value,
      });
    }
  };
  const addProjectItem = async parmas => {
    if (tab === 'a') {
      const resp1 = await insertIncome(parmas);
      if (resp1.code === 200) {
        message.success({
          content: '新增成功!',
          key: 'addProjectItem',
          duration: 2,
        });
        setVisibleModal(false);
        getlist(parmas, tab);
      } else {
        message.error({
          content: '新增失败!',
          key: 'addProjectItem',
          duration: 2,
        });
      }
    }
    if (tab === 'b') {
      const resp2 = await insertOutcome(parmas);
      if (resp2.code === 200) {
        message.success({
          content: '新增成功!',
          key: 'addProjectItem',
          duration: 2,
        });
        getlist(parmas, tab);
        setVisibleModal(false);
      } else {
        message.error({
          content: '新增失败!',
          key: 'addProjectItem',
          duration: 2,
        });
      }
    }
  };

  const handleCreate = props => {
    const { value, key, index } = props;
    arrCreate[index][key] = value;
    setArrCreate([...arrCreate]);
  };

  const [outEditList, setOutEditList] = useState([]);
  //获取提交的form 值
  const handleMoneyChange = props => {
    const {
      value,
      id,
      deptName,
      deptId,
      costType,
      itemName,
      itemNum,
      planTime,
    } = props;
    const list = outEditList;
    let have = false;
    let listIndex = '';
    list.forEach((item, index) => {
      if (item.deptId === deptId && item.costType === costType) {
        listIndex = index;
      }
    });
    if (listIndex !== '') {
      list[listIndex] = {
        actualCost: Number(value).toFixed(2),
        deptName,
        itemNum,
        itemName,
        deptId,
        planTime: `${planTime}-01-01`,
        costType,
        id: id || '',
      };
    } else {
      list.push({
        actualCost: Number(value).toFixed(2),
        deptName,
        itemNum,
        itemName,
        deptId,
        planTime: `${planTime}-01-01`,
        costType,
        id: id || '',
      });
    }
    setOutEditList(list);
  };

  const addTypeItem = () => {
    setArrCreate([
      ...arrCreate,
      {
        id: null,
        uniqueId: uniqueId(),
        actualBillingAmount: 0,
        planBillingTime: null,
        refundNumber: null,
        refundTime: null,
      },
    ]);
  };

  const deleteTypeItem = index => {
    const list = arrCreate;
    list.splice(index, 1);
    setArrCreate([...list]);
  };

  /**
   * 搜索值变化
   */
  const handleSearchParams = ({ value, key }) => {
    // 时间插件获取值不同
    setSearchParmas({ ...searchParmas, [key]: value });
  };
  //修改
  const handleUpdataDate = async () => {
    const list = outEditList;
    const resp = await updateOutcome([...list]);
    if (resp.code === 200) {
      message.success({
        content: '修改成功!',
        key: 'addProjectItem',
        duration: 2,
      });
      setOutEditList([]);
      setVisibleModalUpdate(false);
      setSelectDataId({});
      const newobjs = {};
      setMoneyArr(newobjs);
      setModalItem(newobjs);
      form.setFieldsValue({});
      getlist(parmas, tab);
    } else {
      message.error({
        content: '修改失败!',
        key: 'addProjectItem',
        duration: 2,
      });
    }
  };

  //修改
  const handleMoneyDateIn = async () => {
    var sendList = [];
    const depe = MoneyFormParmasIn;
    sendList = arrCreate.map(items => {
      let {
        actualBillingAmount,
        dept,
        id,
        itemName,
        itemNum,
        planBillingTime,
        refundNumber,
        refundTime,
      } = items;
      return {
        actualBillingAmount: actualBillingAmount,
        planBillingTime: planBillingTime,
        dept: dept,
        id: id,
        itemName: itemName,
        itemNum: itemNum,
        refundNumber: refundNumber,
        refundTime: refundTime,
        dept: depe.dept,
        itemName: depe.itemName,
        itemNum: depe.itemNum,
      };
    });
    const resp2 = await editIncome(sendList);
    if (resp2.code === 200) {
      message.success({
        content: '修改成功!',
        key: 'addProjectItem',
        duration: 2,
      });
      setVisibleModalUpdateIn(false);
      form.setFieldsValue({});

      getlist(parmas, tab);
      setArrCreate([]);
    } else {
      message.error({
        content: '修改失败!',
        key: 'addProjectItem',
        duration: 2,
      });
    }
  };

  // 搜索
  const handleOnSearch = () => {
    setParmas({ ...parmas, ...searchParmas, page: 1 });
  };
  const { limit, page } = parmas;

  const userInfo = JSON.parse(localStorage.getItem('userInfo'));
  const [uploadProps, serUploadProps] = useState({
    action: BASE_URl+'/File/uploadExcelInCome',
    headers: {
      isToken: false,
      Authorization: `Bearer ${userInfo.access_token}`,
    },
    accept: '.xlsx',
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`上传成功`);
        getlist(parmas, tab);
      } else if (info.file.status === 'error') {
        message.error({
          content: `上传失败!${info.file.response.msg}`,
          key: 'error',
          duration: 3,
        });
      }
    },
  });

  return (
    <div className={styles.contentBox}>
      <div className={styles.card}>
        <div className={styles.searchInput}>
          <div>
            <p>项目名称</p>
            <Input
              onChange={e =>
                handleSearchParams({ value: e.target.value, key: 'itemName' })
              }
              placeholder="请输入"
            ></Input>
          </div>
          <div>
            <p>项目编号</p>
            <Input
              onChange={e =>
                handleSearchParams({ value: e.target.value, key: 'itemNum' })
              }
              placeholder="请输入"
            ></Input>
          </div>
          {(tab === 'a' && (
            <>
              <div>
                <p></p>
                <br />
                <Button type="primary" onClick={handleOnSearch}>
                  查询
                </Button>
              </div>
              <div></div>
              <div></div>
            </>
          )) || (
            <>
              <div>
                <p></p>
                <br />
                <Button type="primary" onClick={handleOnSearch}>
                  查询
                </Button>
              </div>
              <div></div>
            </>
          )}
        </div>
      </div>

      <div className={styles.card}>
        <div className={styles.cardOption}>
          <div className={styles.tabSwitch}>
            <Radio.Group
              defaultValue="a"
              buttonStyle="solid"
              onChange={e => tabChange(e)}
            >
              <RadioButton value="a">项目收入(税后)</RadioButton>
              <RadioButton value="b">项目成本</RadioButton>
            </Radio.Group>
          </div>
          <div className={styles.cardRight}>
            {tab === 'a' ? (
              <>
                {' '}
                <Upload
                  showUploadList={false}
                  {...uploadProps}
                  withCredentials={true}
                >
                  <Button type="file">附件导入</Button>
                </Upload>
                <Button type="file">
                  <a href={itemPlanUrl} download={itemPlanUrl}>
                    下载导入模版
                  </a>
                </Button>
              </>
            ) : (
              <>
                {' '}
                <Upload
                  showUploadList={false}
                  {...uploadProps}
                  withCredentials={true}
                >
                  <Button type="file">附件导入</Button>
                </Upload>
                <Button>
                  <a href={itemCostUrl} download={itemCostUrl}>
                    下载导入模版
                  </a>
                </Button>
              </>
            )}
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          scroll={
            tab == 'b'
              ? { x: totalColumns3.length * 110 }
              : { x: totalColumns1.length * 140 }
          }
          // rowSelection={rowSelection}
          loading={loading}
          size="middle"
          className={styles.anTdTable}
        />
        <div className={styles.splitPigination}>
          <div>
            <Select
              defaultValue="10"
              style={{ width: 150 }}
              className={styles.selects}
              onChange={pageSizeChange}
            >
              <Option value="10">显示结果：10条</Option>
              <Option value="20">显示结果：20条</Option>
              <Option value="50">显示结果：50条</Option>
            </Select>
            <span className={styles.total}>共{dataTotal}条</span>
          </div>
          <Pagination
            total={dataTotal || 0}
            pageSize={limit}
            showSizeChanger={false}
            current={page}
            key={67}
            onChange={onChangePageNumber}
          />
        </div>
        <Modal
          width="900px"
          title={`${modalTitleUpdateIn}`}
          visible={visibleModalUpdateIn}
          onOk={handleOkUpIn}
          maskClosable={false}
          onCancel={handleCancelUpIn}
          footer={null}
        >
          <Form
            {...formItemLayoutUpdateIn}
            form={form}
            name="nest-messages"
            onFinish={onFinishFormData}
            labelAlign="right"
          >
            <Form.Item
              label="部门"
              name="dept"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input
                className={styles.disabledInput}
                style={{ width: 300 }}
                bordered={false}
                disabled
              />
            </Form.Item>

            <Form.Item
              label="项目名称"
              name="itemName"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input
                className={styles.disabledInput}
                style={{ width: 300 }}
                bordered={false}
                disabled
              />
            </Form.Item>
            <Form.Item
              label="项目编号"
              name="itemNum"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input
                className={styles.disabledInput}
                style={{ width: 300 }}
                bordered={false}
                disabled
              />
            </Form.Item>

            {ModalTypeUpdateIn == 'update' && (
              <div className={styles.FromLeftRightBOX}>
                <div className={styles.FromLeftRightLeft}>
                  <span
                    style={{
                      lineHeight: 2,
                      color: 'rgba(0, 0, 0, 0.85)',
                      fontSize: '14px',
                      marginTop: '10px',
                    }}
                  >
                    开票金额(W):
                  </span>
                </div>
                <div className={styles.FromLeftRight}>
                  <div className={styles.FromLeft}>
                    {arrCreate.map((items, index) => {
                      return (
                        <div
                          key={items.uniqueId}
                          className={styles.rowLeftRight}
                        >
                          <div className={styles.rowLeft}>
                            <InputNumber
                              style={{ width: 110 }}
                              defaultValue={items.actualBillingAmount}
                              className={styles.selects}
                              min={0}
                              onChange={e =>
                                handleCreate({
                                  value: e,
                                  key: 'actualBillingAmount',
                                  index,
                                })
                              }
                              formatter={value => `${value}`}
                              parser={value => value.replace('W', '')}
                            />
                          </div>
                          <div className={styles.rowRight}>
                            <DatePicker
                              className={classNames(
                                styles.selects,
                                styles.rangePicker,
                              )}
                              suffixIcon={<ClockCircleOutlined />}
                              onChange={(date, deteString) =>
                                handleCreate({
                                  value: deteString,
                                  key: 'planBillingTime',
                                  index,
                                })
                              }
                              value={
                                (items.planBillingTime &&
                                  moment(items.planBillingTime)) ||
                                ''
                              }
                              picker="month"
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  <div className={styles.FromRight}>
                    <div className={styles.FromRightLef}>
                      <span
                        style={{
                          lineHeight: 2,
                          color: 'rgba(0, 0, 0, 0.85)',
                          fontSize: '14px',
                          marginTop: '10px',
                        }}
                      >
                        回款金额(W)：
                      </span>
                    </div>
                    <div className={styles.FromRightRight}>
                      {arrCreate.map((items, index) => {
                        return (
                          <div
                            key={items.uniqueId}
                            className={styles.rowLeftRight}
                          >
                            <div className={styles.rowLeft}>
                              <InputNumber
                                style={{ width: 110 }}
                                defaultValue={items.refundNumber}
                                className={classNames(styles.selects)}
                                min={0}
                                onChange={e =>
                                  handleCreate({
                                    value: e,
                                    key: 'refundNumber',
                                    index,
                                  })
                                }
                                formatter={value => `${value}`}
                                parser={value => value.replace('W', '')}
                              />
                            </div>
                            <div className={styles.rowRight}>
                              <DatePicker
                                picker="month"
                                className={classNames(
                                  styles.selects,
                                  styles.rangePicker,
                                )}
                                suffixIcon={<ClockCircleOutlined />}
                                onChange={(date, deteString) =>
                                  handleCreate({
                                    value: deteString,
                                    key: 'refundTime',
                                    index,
                                  })
                                }
                                value={
                                  (items.refundTime &&
                                    moment(items.refundTime)) ||
                                  ''
                                }
                              />
                              {index === 0 && (
                                <PlusOutlined
                                  className={styles.incons}
                                  onClick={addTypeItem}
                                />
                              )}
                              {index > 0 && (
                                <CloseOutlined
                                  className={styles.incons}
                                  onClick={() => deleteTypeItem(index)}
                                />
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            )}
            {ModalTypeUpdateIn == 'query' && (
              <div className={styles.FromLeftRightBOX}>
                <div className={styles.FromLeftRightLeft}>
                  {arrCreate.length > 0 && (
                    <span
                      style={{
                        lineHeight: 2,
                        color: 'rgba(0, 0, 0, 0.85)',
                        fontSize: '14px',
                        marginTop: '10px',
                      }}
                    >
                      {' '}
                      开票金额(W):{' '}
                    </span>
                  )}
                </div>
                <div className={styles.FromLeftRight}>
                  <div className={styles.FromLeft}>
                    {arrCreate.map((items, index) => {
                      return (
                        <div key={index} className={styles.rowLeftRight}>
                          <div className={styles.rowLeft}>
                            <div
                              style={{
                                width: 60,
                                height: 30,
                                paddingLeft: 5,
                                paddingTop: 3,
                                color: 'rgb(21 17 17)',
                              }}
                            >
                              {items.actualBillingAmount}
                            </div>
                          </div>
                          <div className={styles.rowRightUpdate}>
                            {items.planBillingTime ? (
                              <MonthPicker
                                className={styles.disabledInput}
                                picker="month"
                                defaultValue={
                                  (items.planBillingTime &&
                                    moment(items.planBillingTime)) ||
                                  ''
                                }
                                disabled
                                Value={moment(items.planBillingTime)}
                                bordered={false}
                              />
                            ) : (
                              <span className={styles.timeSpan}>暂无数据</span>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  <div className={styles.FromRight}>
                    <div className={styles.FromRightLef}>
                      {arrCreate.length > 0 && (
                        <span
                          style={{
                            lineHeight: 2,
                            color: 'rgba(0, 0, 0, 0.85)',
                            fontSize: '14px',
                            marginTop: '10px',
                          }}
                        >
                          回款金额(W)：
                        </span>
                      )}
                    </div>
                    <div className={styles.FromRightRight}>
                      {arrCreate.map((items, index) => {
                        return (
                          <div key={index} className={styles.rowLeftRight}>
                            <div className={styles.rowLeft}>
                              <div
                                style={{
                                  width: 60,
                                  height: 30,
                                  paddingLeft: 5,
                                  paddingTop: 3,
                                  color: 'rgb(21 17 17)',
                                }}
                              >
                                {items.refundNumber}
                              </div>
                            </div>
                            <div className={styles.rowRightUpdate}>
                              {items.refundTime ? (
                                <MonthPicker
                                  className={styles.disabledInput}
                                  picker="month"
                                  defaultValue={
                                    (items.refundTime &&
                                      moment(items.refundTime)) ||
                                    ''
                                  }
                                  disabled
                                  Value={moment(items.refundTime)}
                                  bordered={false}
                                />
                              ) : (
                                <span className={styles.timeSpan}>
                                  暂无数据
                                </span>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            )}
            {ModalTypeUpdateIn == 'update' && (
              <div className={styles.btnForm}>
                <Button onClick={handleMoneyDateIn} type="primary">
                  提交
                </Button>
              </div>
            )}
          </Form>
        </Modal>
        <Modal
          width="600px"
          title={`${modalTitleUpdate}`}
          visible={visibleModalUpdate}
          onOk={handleOkUp}
          maskClosable={false}
          onCancel={handleCancelUp}
          footer={null}
        >
          <Form
            {...formItemLayout}
            form={form}
            name="nest-messages"
            onFinish={onFinishFormData}
            labelAlign="right"
          >
            <Form.Item
              label="立项部门"
              name="deptName"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input style={{ width: 400 }} bordered={false} disabled />
            </Form.Item>
            <Form.Item
              label="项目名称"
              name="itemName"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input style={{ width: 400 }} bordered={false} disabled />
            </Form.Item>
            <Form.Item
              label="项目编号"
              name="itemNum"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input style={{ width: 400 }} bordered={false} disabled />
            </Form.Item>
            <>
              {outcomeDetail.length > 0 && (
                <div className={styles.MoneyBoxBoxTop}>
                  <span>年度</span>
                  <span>部门</span>
                  <span>费用类别</span>
                  <span>预算金额(W)</span>
                  <span>已核成本(W)</span>
                </div>
              )}

              {outcomeDetail.length > 0 &&
                outcomeDetail.map(item => (
                  <>
                    {item.detailList &&
                      item.detailList.length > 0 &&
                      item.detailList.map((detailItem, detailItemIndex) => (
                        <div className={styles.MoneyBoxBox}>
                          <div>{detailItem.plan_time}</div>
                          <Tooltip placement="topLeft" title={item.deptName}>
                            <div>{item.deptName}</div>
                          </Tooltip>
                          <Tooltip
                            placement="topLeft"
                            title={detailItem.costName}
                          >
                            <div>{detailItem.costName}</div>
                          </Tooltip>
                          <div>{detailItem.planCost}</div>
                          {ModalTypeUpdate == 'update' ? (
                            <InputNumber
                              size="small"
                              key={detailItemIndex}
                              precision={2}
                              defaultValue={detailItem.actualCost}
                              onChange={value =>
                                handleMoneyChange({
                                  value,
                                  itemName: item.itemName,
                                  itemNum: item.itemNum,
                                  id: detailItem.id,
                                  deptName: item.deptName,
                                  deptId: item.deptId,
                                  planTime: detailItem.plan_time,
                                  costType: detailItem.costType,
                                })
                              }
                              min={0}
                              className={classNames(styles.selects)}
                              style={{ width: '25%' }}
                            />
                          ) : (
                            <div>{detailItem.actualCost}</div>
                          )}
                        </div>
                      ))}
                  </>
                ))}
            </>
            <div className={styles.btnForm}>
              {ModalTypeUpdate == 'update' && (
                <Button onClick={handleUpdataDate} type="primary">
                  提交
                </Button>
              )}
            </div>
          </Form>
        </Modal>
        <Modal
          title={`${modalTitle}`}
          visible={visibleModal}
          onOk={handleOk}
          maskClosable={false}
          onCancel={handleCancel}
          footer={null}
        >
          <Form
            layout="vertical"
            form={form}
            name="nest-messages"
            onFinish={onFinishFormData}
          >
            {tab === 'a' && (
              <>
                <Form.Item
                  label="实际开票金额"
                  name="actualBillingAmount"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <InputNumber
                    min={0}
                    formatter={value => `${value}W`}
                    parser={value => value.replace('W', '')}
                    style={{ width: 300 }}
                  />
                </Form.Item>
                <Form.Item
                  label="开票时间"
                  name="planBillingTime"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <DatePicker style={{ width: 300 }} />
                </Form.Item>
                <Form.Item
                  label="项目编号"
                  name="itemNum"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Input style={{ width: 300 }} />
                </Form.Item>
                <Form.Item
                  label="回款金额"
                  name="refundNumber"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <InputNumber
                    min={0}
                    style={{ width: 300 }}
                    formatter={value => `${value}W`}
                    parser={value => value.replace('W', '')}
                  />
                </Form.Item>
                <Form.Item
                  label="回款时间"
                  name="refundTime"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <DatePicker style={{ width: 300 }} />
                </Form.Item>
              </>
            )}
            {tab === 'b' && (
              <>
                <Form.Item
                  label="项目编号"
                  name="itemNum"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Input style={{ width: 300 }} />
                </Form.Item>
                <Form.Item
                  label="费用类别"
                  name="costType"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    style={{ width: 300 }}
                    showSearch
                    filterOption={(input, option) =>
                      option.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {inComeTypeList.length > 0 &&
                      inComeTypeList.map((item, index) => (
                        <Option value={item.name} key={index}>
                          {item.name}
                        </Option>
                      ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  label="实际费用"
                  name="actualCost"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <InputNumber
                    min={0}
                    style={{ width: 300 }}
                    formatter={value => `${value}W`}
                    parser={value => value.replace('W', '')}
                  />
                </Form.Item>
              </>
            )}
            <Form.Item style={{ marginBottom: 0 }}>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
};
