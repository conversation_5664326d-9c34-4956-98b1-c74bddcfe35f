import request from '@/utils/request';
import { BASE_URl } from '../../../utils/constant';

//获取收入列表list
export function getIncomeListPage(parmas) {
  return request(`${BASE_URl}/Finance/getIncomeListPage`, {
    method: 'post',
    data: parmas,
  });
}
//获取成本列表list
export function getOutcomeListPage(parmas) {
  return request(`${BASE_URl}/Finance/getOutcomeListPage`, {
    method: 'post',
    data: parmas,
  });
}

//删除
export function deleteCome(parmas) {
  const { name, id } = parmas;
  return request(`${BASE_URl}/Finance/deleteCome?name=${name}&id=${id}`, {
    method: 'post',
  });
}

//新增收入
export function insertIncome(parmas) {
  return request(`${BASE_URl}/Finance/insertIncome`, {
    method: 'post',
    data: parmas,
  });
}
//新增成本
export function insertOutcome(parmas) {
  return request(`${BASE_URl}/Finance/insertOutcome`, {
    method: 'post',
    data: parmas,
  });
}

//上传项目收入
export function uploadExcelInCome(parmas) {
  const userInfo = JSON.parse(localStorage.getItem('userInfo'));

  return request(`${BASE_URl}/File/uploadExcelInCome`, {
    method: 'post',
    requestType: 'form',
    headers: {
      isToken: false,
      Authorization: `Bearer ${userInfo.access_token}`,
    },
    data: parmas,
  });
}

//上传项目成本
export function uploadExcelOutCome(parmas) {
  return request(`${BASE_URl}/File/uploadExcelOutCome`, {
    method: 'post',
    requestType: 'form',
    data: parmas,
  });
}

//费用类型下拉框信息
export function getInComeType() {
  return request(`${BASE_URl}/Finance/getInComeType`, {
    method: 'post',
  });
}

//项目收入详情
export function getInComeListType(parmas) {
  return request(
    `${BASE_URl}/Finance/getIncomeListByItemNum?itemNum=${parmas}`,
    {
      method: 'post',
    },
  );
}

//修改项目成本
export function updateOutcome(parmas) {
  return request(`${BASE_URl}/Finance/editOutcome`, {
    method: 'post',
    data: parmas,
  });
}
//编辑项目收入
export function editIncome(parmas) {
  return request(`${BASE_URl}/Finance/editIncome`, {
    method: 'post',
    data: parmas,
  });
}

export function getFileUrl(parmas) {
  return request(`${BASE_URl}/File/getFileUrl`, {
    method: 'post',
    data: parmas,
  });
}

export function selectOutcomeDetail(parmas) {
  return request(`${BASE_URl}/Finance/selectOutcomeDetail?itemNum=${parmas}`, {
    method: 'post',
  });
}
