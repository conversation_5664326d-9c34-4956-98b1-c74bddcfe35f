.contentBox {
  height: 100%;
  padding: 24px;
  background: #f7f8fa;
}

.card {
  width: 100%;
  background: #ffffff;
  border: 1px solid #edeff2;
  box-sizing: border-box;
  padding: 13px 24px;
  box-shadow: 0px 4px 16px -8px rgba(22, 45, 90, 0.08);
  border-radius: 8px;
  margin-bottom: 24px;

  :global {
    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      background-color: #3d7bf8;
      border-color: #3d7bf8;
    }

    .ant-radio-button-wrapper:first-child {
      border-radius: 5px 0 0 5px;
    }

    .ant-radio-button-wrapper:last-child {
      border-radius: 0 5px 5px 0;
    }

    .ant-btn-primary {
      background-color: #3d7bf8;
    }
  }

  .cardOption {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .cardRight {
    width: 210px;
    display: flex;
    justify-content: space-between;

    .spotIcon > div {
      width: 4px;
      height: 4px;
      background-color: #7a7a7a;
      border-radius: 50%;
    }

    .spotIcon > div:not(:first-child) {
      margin-top: 2px;
    }
  }

  .project {
    display: flex;
    justify-content: space-between;

    button {
      border-radius: 0;
      transition: all 0s ease;
      position: relative;
      top: 0;
      vertical-align: middle;
    }

    .manage {
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
    }

    .no_manage {
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }

    .active {
      background: #3d7bf8;
      color: #ffffff;
    }

    .focus {
      button {
        font-family: OPPOSans;
        font-style: normal;
        font-size: 14px;
        border: 1px solid #edeff2;
        box-sizing: border-box;
        border-radius: 6px;
      }
    }
  }

  p {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    /* identical to box height, or 157% */
    /* Netural Color/Grey 600 */
    color: #525252;
  }

  input,
  button {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
  }
}

.searchInput {
  display: flex;
  justify-content: flex-start;

  > div {
    width: 15%;
    margin-right: 20px;
  }

  > div:nth-child(3) {
    width: 15%;
  }

  > div:nth-child(5) {
    width: 10%;
  }
}

.rangePicker {
  border: 1px solid #edeff2;
  box-sizing: border-box;
  border-radius: 6px;

  input {
    border: 0;
  }
}

.splitPigination {
  display: flex;
  justify-content: space-between;
  padding: 0 0 0 0px;
  margin-top: 20px;

  .pageSelect {
    box-sizing: border-box;
    border-radius: 6px;
  }

  .total {
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    margin-left: 8px;
    color: #7a7a7a;
  }

  input,
  button,
  li {
    box-sizing: border-box;
    border-radius: 6px;
  }
}

.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;

  > div {
    display: flex;

    > div {
      width: 144px;
      height: 32px;
      left: 0px;
      top: 0px;
      background: #ffffff;
      border: 1px solid #edeff2;
      box-sizing: border-box;
      border-radius: 6px;
    }
  }

  p {
    font-family: OPPOSans;
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    margin: 6px 0 0 10px;
    /* identical to box height, or 167% */

    /* Netural Color/Grey 500 */

    color: #7a7a7a;
  }

  // width: 100%;
  // float: right;
  // >li:last-child {
  //   float: left;
  //   margin-right: 10px;
  //   margin-left: 0;
  // }
  > li {
    border: 1px solid #edeff2;
    box-sizing: border-box;
    border-radius: 6px;
  }
}

.anTdTable tr > th,
.anTdTable tr > td {
  border-bottom: 0px;
  padding: 10px 8px !important;
}

.anTdTable tr > th {
  font-size: 12px;
  line-height: 20px;
  color: #7a7a7a;

  > div > div {
    padding: 10px 16px;
  }
}

.deleteBt > a {
  color: #e8684a;
  cursor: pointer;
}

.updateBt > a {
  color: #1890ff;
  cursor: pointer;
}

.updateBt {
  margin-left: 5px;
  margin-right: 5px;
}

.queryBt {
  margin-left: 12px;
}

.queryBt > a {
  color: #175c47;
  cursor: pointer;
  margin-left: 5px;
}

.selects,
.selects > div {
  border-color: #edeff2 !important;
  box-sizing: border-box;
  border-radius: 6px !important;
}

.MoneyBoxBoxTop {
  display: flex;
  justify-content: space-between;
  text-align: center;
  margin-bottom: 10px;

  > span:first-child {
    width: 30%;
    white-space: nowrap;
    overflow: hidden;
  }

  > span {
    width: 25%;
  }
}

.MoneyBoxBox {
  height: auto;
  display: flex;
  flex-wrap: nowrap;
  margin-bottom: 10px;
  text-align: center;

  > div:nth-child(2) {
    width: 30%;
    white-space: nowrap;
    overflow: hidden;
  }

  > div {
    width: 25%;
    justify-content: space-between;
    white-space: nowrap;
  }
}

.MoneyBoxLeft {
  width: 300px;
  text-align: right;

  white-space: nowrap;
}

.MoneyBoxRight {
  width: 400px;
  height: auto;
  display: flex;
  flex-wrap: nowrap;
}

.spanRight {
  margin-left: 60px;
}

//编辑项目成本费用列表回显样式
.MoneyListBox {
  max-height: auto;
}

//-div
.MoneyListDiv {
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
  margin-bottom: 10px;
}

//--span
.MoneyListSpan {
  text-align: right;
  width: 100px;
  height: 20px;
  display: block;
  padding-right: 8px;
  color: rgba(0, 0, 0, 0.25);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* autoprefixer: ignore next */
  -webkit-box-orient: vertical;
}

//form表单左右结构
.FromLeftRight {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: flex-start;
}

.FromLeft {
  width: 43%;
  height: auto;
}

.FromRight {
  width: 55%;
  height: auto;
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
}

.FromRightLef {
  width: 92px;
  height: auto;
  white-space: nowrap;
}

.FromRightRight {
  width: calc(100%-92px);
}

.rowLeftRight {
  width: 100%;
  height: auto;
  display: flex;
  margin-bottom: 10px;
  justify-content: flex-start;
}

.rowRight {
  margin-left: 10px;
}

.inconStyle {
  margin-left: 5px;
}

.inconStyle:hover {
  cursor: pointer;
}

.spanText {
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: space-between;

  > div:first-child {
    width: 100px;
    overflow: hidden;
  }
}

.SpanLeft {
  width: 70px;
  height: 30px;
  display: block;
  padding-left: 25px;
  color: rgba(0, 0, 0, 0.85);
}

.SpanLeftS {
  width: 117px;
  height: 30px;
  display: block;
  padding-left: 60px;
  color: rgba(0, 0, 0, 0.85);
}

.btnForm {
  width: 100%;
  height: 80px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.FromRightLefSpan {
  transform: translateY('10px');
}

.incons {
  margin-left: 5px;
}

.FromLeftRightBOX {
  width: 780px;
  height: auto;
  margin-left: 70px;
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  margin-bottom: 20px;
}

.FromLeftRightLeft {
  width: 98px;
  height: 50px;
}

.rowRightUpdate > div > div > span {
  display: none;
}

.rangePicker {
  border: 1px solid #edeff2;
  box-sizing: border-box;
  border-radius: 6px;

  input {
    border: 0;
  }
}

.disabledInput:disabled,
.disabledInput[disabled] {
  color: rgb(21 17 17);
  opacity: 1;
  -webkit-text-fill-color: rgb(
    21 17 17
  ); // ios 和 安卓9.0 必须添加此属性，才会生效
  -webkit-opacity: 1;
}
.disabledInput {
  div {
    input:disabled,
    input[disabled] {
      color: rgb(21 17 17);
      opacity: 1;
      -webkit-text-fill-color: rgb(
        21 17 17
      ); // ios 和 安卓9.0 必须添加此属性，才会生效
      -webkit-opacity: 1;
    }
  }
}

.timeSpan {
  color: rgb(160 148 145);
  display: block;
  width: 80px;
  height: 20px;
  padding-left: 12px;
}

.textHover:hover {
  cursor: pointer;
}
