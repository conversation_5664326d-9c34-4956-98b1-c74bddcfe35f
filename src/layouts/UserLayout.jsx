import { Link } from 'umi';
import {
  Default<PERSON>ooter,
  getMenuData,
  getPageTitle,
} from '@ant-design/pro-layout';
import React, { useState, useEffect } from 'react';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import { connect } from 'dva';
import { queryCurrent } from '../services/user';
import { Alert, Spin, Space } from 'antd';
// import SelectLang from '@/components/SelectLang';
// import logo from '../assets/logo.png';
import styles from './UserLayout.less';
import Children from '../pages/user/login';
import getUrlParams from "../utils/getUrlParams";
const UserLayout = props => {
  const {
    route = {
      routes: [],
    },
  } = props;
  const { routes = [] } = route;
  const {
    children,
    location = {
      pathname: '',
    },
  } = props;

  const token = getUrlParams("token") || "";

  const { breadcrumb } = getMenuData(routes);
  const title = getPageTitle({
    pathname: location.pathname,
    breadcrumb,
    ...props,
  });

  // useEffect(() => {
  //   if (location.query.token) {
  //     queryCurrents(location.query.token);
  //   }
  // });
  // useEffect(() => {
  //   if (token) {
  //     queryCurrents(location.query.token);
  //   }
  // }, []);
  // const queryCurrents = async parmas => {
  //   const { data, code } = await queryCurrent(parmas);
  //   if (code === 200) {
  //     if (data) {
  //       const userInfo = {
  //         access_token: location.query.token,
  //         userId: data.sysUser.userId,
  //       };
  //       localStorage.clear();
  //       localStorage.setItem('userInfo', JSON.stringify(userInfo));
  //       localStorage.setItem('updateTime', new Date().getTime());
  //       localStorage.setItem('loginType', 'unified');
  //       window.location.replace('/workbench');
  //     }
  //   }
  // };
  return (
    <>
      {location.query.token ? (
        <div className={styles.space}>
          <Space size="middle">
            <Spin size="large" />
          </Space>
        </div>
      ) : (
        <HelmetProvider style={[{ height: '1260px' }]}>
          <Helmet>
            <title>{title}</title>
            <meta name="description" content={title} />
          </Helmet>
          <div className={styles.container}>
            {/* <div className={styles.lang}>
          <SelectLang />
        </div> */}
            <div className={styles.content}>
              <div className={styles.top}>
                <div className={styles.header}>
                  <Link to="/">
                    {/* <img alt="logo" className={styles.logo} src={logo} /> */}
                    {/* <span className={styles.title}>Cares DayDayUp </span> */}
                  </Link>
                </div>
                <div className={styles.desc}>西南凯亚市场经营管理平台</div>
              </div>
              <Children />
            </div>
            <DefaultFooter />
          </div>
        </HelmetProvider>
      )}
    </>
  );
};

export default connect(({ settings }) => ({ ...settings }))(UserLayout);
