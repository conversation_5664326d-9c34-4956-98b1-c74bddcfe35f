const NavModel = {
  namespace: 'navModel',
  state: {
    navList:[1,123,123]
  },
  reducers: {
    'changeNavModel'(state,value) {
      // console.log(value,this.state,'state');
      console.log(state,'statestate');
      console.log(value,'valuevalue');
      // state.push(1)
      // console.log(state);
      // state.navList.push(44)
      // setState
      return value;
    },
  },
};
export default NavModel;