import { history } from 'umi';
import { queryUserLogin } from '@/services/login';
import { setAuthority } from '@/utils/authority';
import { notification } from 'antd';

const avatarServer = 'http://110.185.210.123:10158/video/caresImg/';

const Model = {
  namespace: 'login',
  state: {
    status: undefined,
    key: '1234567887654321',
    avatarServer: avatarServer,
  },
  effects: {
    *login({ payload, callback }, { call }) {
      const response = yield call(queryUserLogin, payload);
      // yield put({
      //   type: 'changeLoginStatus',
      //   payload: response,
      // });

      if (response && response.userId && response.access_token) {
        localStorage.clear();
        localStorage.setItem('userInfo', JSON.stringify(response));
        localStorage.setItem('updateTime', new Date().getTime());
        window.location.replace('/workbench');
        // history.push('/projectOverview/projectList');
      } else {
        if (callback && typeof callback === 'function') {
          callback('账号密码错误!!!');
        }
        const width = document.body.clientWidth;
        notification.error({
          description: '登录失败',
          message: response && response.msg ? response.msg : '账号密码错误!!!',
          style: {
            width: 400,
            marginRight: width / 2 - 200,
          },
        });
      }
    },
    logout() {
      localStorage.clear();
      history.replace('/user/login');
    },
  },
  reducers: {
    changeLoginStatus(state, { payload }) {
      setAuthority(payload.currentAuthority);
      return { ...state, status: payload.status, type: payload.type };
    },
  },
};
export default Model;
