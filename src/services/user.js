import request from '@/utils/request';
import { BASE_URl } from "../utils/constant";

export async function query() {
  return request(`${BASE_URl}/users`);
}

export async function queryCurrent(params) {
  return request(`${BASE_URl}/user/info`, {
    headers: {
      isToken: false,
      Authorization: `Bearer ${params}`,
    },
  });
}

export async function queryNotices() {
  return request(`${BASE_URl}/notices`);
}
// 获取用户的所有官方账号
export async function getAllRole() {
  return request(`${BASE_URl}/community/talkComment/getOfficialRole`, {
    method: 'POST',
  });
}

//
export function getButtonObject() {
  return request(`${BASE_URl}/access/getItemAccess`, {
    method: 'POST',
  });
}
