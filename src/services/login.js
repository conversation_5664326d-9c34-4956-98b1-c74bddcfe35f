import request from '@/utils/request';
import { BASE_URl } from "../utils/constant";

export async function fakeAccountLogin(params) {
  return request(`${BASE_URl}/login/account`, {
    method: 'POST',
    data: params,
  });
}

export async function queryUserLogin(params) {
  return request(`${BASE_URl}/auth/oauth/token`, {
    method: 'POST',
    headers: {
      isToken:false,
      'Authorization': 'Basic cGlnOnBpZw=='
    },
    params,
  });
}

export async function refreshToken(params){
  return request(`${BASE_URl}/auth/oauth/token`,{
    method: 'POST',
    headers: {
      'isToken': false,
      'Authorization': 'Basic cGlnOnBpZw==',
    },
    params,
  })
}

export async function getFakeCaptcha(mobile) {
  return request(`${BASE_URl}/login/captcha?mobile=${mobile}`);
}
