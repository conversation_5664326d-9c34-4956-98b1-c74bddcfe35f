export const BASE_URl =
  process.env.NODE_ENV === 'development' ? '/hrs' : '/hrs';
/** 用户当前地址*/
export const CURRENT_URL = window.location.origin;

/** 跳转回来的地址*/
export const LOCATION_URL =
  CURRENT_URL?.indexOf('swit-test') > -1
    ? window.location.origin + '/mp'
    : window.location.origin;

/** 用户中心跳转地址 */
export const LOGIN_URL =
  CURRENT_URL?.indexOf('swit-test') > -1 ||
  process.env.NODE_ENV === 'development'
    ? // ? 'http://swit-test.psc.sw/login/?redirectUrl=' + LOCATION_URL
      'http://pub.it.sw/login/?redirectUrl=' + LOCATION_URL
    : 'http://pub.it.sw/login/?redirectUrl=' + LOCATION_URL;

// console.log('=== 地址：', process.env.Deploy, _CURRENT_URL, LOGIN_URL);
