import Index from './routerList/index';
import HideRouter from './hideRouter/index';
export default [
  // {
  //   path: '/user',
  //   component: '../src/layouts/UserLayout',
  //   routes: [
  //     {
  //       name: 'login',
  //       path: '/user/login',
  //       component: './user/login',
  //     },
  //   ],
  // },
  {
    path: '/',
    component: '../layouts/SecurityLayout',
    routes: [
      {
        path: '/',
        component: '../layouts/BasicLayout',
        authority: ['admin', 'user'],
        routes: [
          {
            path: '/',
            redirect: '/welcome',
          },
          {
            path: '/welcome',
            name: 'welcome',
            icon: 'smile',
            component: './Welcome',
          },
          {

          },
          // ...Index,
          // ...HideRouter,
          // {
          //   name: 'test',
          //   icon: 'FileTextOutlined',
          //   path: '/test',
          //   component: './test',
          // },
          {
            component: './404',
          },
        ],
      },
      {
        component: './404',
      },
    ],
  },
  {
    component: './404',
  },
];
