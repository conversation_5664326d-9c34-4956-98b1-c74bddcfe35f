# 项目状态输入框修复说明

## 问题描述
在项目列表页面（mp/projectList）的收起状态下，点击重置按钮后项目状态的输入框出现以下问题：
1. 输入框大小改变了
2. 值展示不全

## 问题原因
1. **CSS类名错误**：项目状态输入框使用了错误的CSS类名 `.esarchItema`，而样式文件中只定义了 `.searchItema`
2. **样式缺失**：`.esarchItemb` 和 `.esarchItemc` 类名在样式文件中也不存在，应该分别对应 `.searchItemb` 和 `.searchItemc`

## 修复内容

### 1. 修复CSS类名
- 将 `.esarchItema` 改为 `.searchItema`
- 将 `.esarchItemb` 改为 `.searchItemb` 
- 将 `.esarchItemc` 改为 `.searchItemc`

### 2. 更新样式文件
- 在 `index.less` 中将 `.esarchItemc` 改为 `.searchItemc`

## 修复后的代码

### JSX 代码（收起状态的项目状态输入框）
```jsx
<div className={styles.searchItema}>
  <p>项目状态</p>
  <Select
    allowClear
    value={searchParmas.item_stage}
    style={{ width: '100%' }}
    placeholder="请选择"
    className={styles.selects}
    onChange={value => handleSearchParams('item_stage', value)}
  >
    <Option value="running">进行中</Option>
    <Option value="end">已结项</Option>
  </Select>
</div>
```

### CSS 样式
```less
.searchInput {
  display: flex;
  justify-content: space-between;
  position: relative;
  flex-wrap: wrap;

  .searchItema {
    width: 15%;
  }

  .searchItemb {
    width: 30%;
  }
  
  .searchItemc {
    width: 10%;
  }
}
```

## 验证方法
1. 打开项目列表页面
2. 确保页面处于收起状态
3. 在项目状态输入框中选择一个值
4. 点击重置按钮
5. 验证项目状态输入框的大小和显示是否正常

## 预期结果
- 项目状态输入框在重置后保持正确的宽度（15%）
- 输入框的值能够完整显示
- 重置功能正常清空所有搜索条件
